"""
Test runner configuration and utilities for the campaigns app test suite.

This module provides:
- Custom test runner with enhanced reporting
- Test discovery and organization
- Performance monitoring for tests
- Coverage reporting integration
- Test data cleanup utilities
"""

import time
import sys
from io import StringIO
from django.test.runner import Discover<PERSON>unner
from django.test import TestCase
from django.core.management import call_command
from django.db import connection
from django.conf import settings


class CampaignsTestRunner(DiscoverRunner):
    """
    Custom test runner for the campaigns app with enhanced features.
    
    Features:
    - Performance monitoring
    - Enhanced error reporting
    - Test data cleanup
    - Coverage integration
    """
    
    def __init__(self, *args, **kwargs):
        self.start_time = None
        self.test_times = {}
        super().__init__(*args, **kwargs)
    
    def setup_test_environment(self, **kwargs):
        """Set up the test environment."""
        super().setup_test_environment(**kwargs)
        self.start_time = time.time()
        
        # Disable logging during tests to reduce noise
        import logging
        logging.disable(logging.CRITICAL)
        
        print("Setting up campaigns test environment...")
    
    def teardown_test_environment(self, **kwargs):
        """Tear down the test environment."""
        super().teardown_test_environment(**kwargs)
        
        # Re-enable logging
        import logging
        logging.disable(logging.NOTSET)
        
        if self.start_time:
            total_time = time.time() - self.start_time
            print(f"\nTotal test execution time: {total_time:.2f} seconds")
    
    def run_tests(self, test_labels, **kwargs):
        """Run the test suite with enhanced reporting."""
        print("Starting campaigns app test suite...")
        print("=" * 70)
        
        # Run tests
        result = super().run_tests(test_labels, **kwargs)
        
        # Print summary
        self.print_test_summary()
        
        return result
    
    def print_test_summary(self):
        """Print a summary of test execution."""
        print("\n" + "=" * 70)
        print("TEST EXECUTION SUMMARY")
        print("=" * 70)
        
        if self.test_times:
            print("\nSlowest tests:")
            sorted_tests = sorted(
                self.test_times.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]
            
            for test_name, duration in sorted_tests:
                print(f"  {test_name}: {duration:.3f}s")
        
        # Database query count
        if hasattr(connection, 'queries'):
            query_count = len(connection.queries)
            print(f"\nTotal database queries: {query_count}")
        
        print("\nTest categories covered:")
        print("  ✓ Model validation and relationships")
        print("  ✓ Form validation and field handling")
        print("  ✓ View functionality and responses")
        print("  ✓ External service integration")
        print("  ✓ End-to-end workflows")
        print("  ✓ Performance and bulk operations")


class TestDataManager:
    """
    Utility class for managing test data across test runs.
    """
    
    @staticmethod
    def clean_test_data():
        """Clean up test data from the database."""
        from campaigns.models import (
            Campaign, DynamicTag, TagGroup, TagCategory,
            CampaignTag, LocationTarget, UsernameTarget,
            WorkflowExecution, TagAnalysisResult, CampaignResult
        )
        
        # Delete in reverse dependency order
        models_to_clean = [
            TagAnalysisResult,
            CampaignResult,
            WorkflowExecution,
            CampaignTag,
            LocationTarget,
            UsernameTarget,
            Campaign,
            DynamicTag,
            TagGroup,
            TagCategory,
        ]
        
        for model in models_to_clean:
            count = model.objects.count()
            if count > 0:
                model.objects.all().delete()
                print(f"Cleaned {count} {model.__name__} objects")
    
    @staticmethod
    def create_test_fixtures():
        """Create standard test fixtures for consistent testing."""
        from campaigns.tests.factories import (
            TagCategoryFactory, TagGroupFactory, DynamicTagFactory,
            UserFactory
        )
        
        # Create standard categories
        categories = {
            'fitness': TagCategoryFactory.create_fitness(),
            'fashion': TagCategoryFactory.create_fashion(),
        }
        
        # Create standard groups
        groups = {
            'influencers': TagGroupFactory.create_influencer_group(),
        }
        
        # Create standard tags
        tags = {
            'fitness_enthusiast': DynamicTagFactory.create_fitness_tag(),
            'system_tag': DynamicTagFactory.create_system_tag(),
        }
        
        # Create test users
        users = {
            'test_user': UserFactory.create(username='test_user'),
            'admin_user': UserFactory.create_admin(username='admin_user'),
        }
        
        return {
            'categories': categories,
            'groups': groups,
            'tags': tags,
            'users': users
        }


class PerformanceTestMixin:
    """
    Mixin for adding performance testing capabilities to test cases.
    """
    
    def assertExecutionTime(self, func, max_time, *args, **kwargs):
        """Assert that function execution time is under the specified limit."""
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = time.time() - start_time
        
        self.assertLess(
            execution_time,
            max_time,
            f"Function {func.__name__} took {execution_time:.3f}s, "
            f"expected under {max_time}s"
        )
        
        return result
    
    def assertQueryCount(self, func, max_queries, *args, **kwargs):
        """Assert that function executes under the specified query limit."""
        from django.test.utils import override_settings
        from django.db import connection
        
        with override_settings(DEBUG=True):
            initial_queries = len(connection.queries)
            result = func(*args, **kwargs)
            query_count = len(connection.queries) - initial_queries
            
            self.assertLessEqual(
                query_count,
                max_queries,
                f"Function {func.__name__} executed {query_count} queries, "
                f"expected under {max_queries}"
            )
        
        return result


class CoverageTestMixin:
    """
    Mixin for adding coverage testing capabilities.
    """
    
    @classmethod
    def setUpClass(cls):
        """Set up coverage tracking."""
        super().setUpClass()
        try:
            import coverage
            cls.cov = coverage.Coverage()
            cls.cov.start()
        except ImportError:
            cls.cov = None
    
    @classmethod
    def tearDownClass(cls):
        """Stop coverage tracking and report."""
        if cls.cov:
            cls.cov.stop()
            cls.cov.save()
        super().tearDownClass()
    
    def get_coverage_report(self):
        """Get coverage report for the current test."""
        if self.cov:
            output = StringIO()
            self.cov.report(file=output)
            return output.getvalue()
        return "Coverage not available"


def run_test_suite():
    """
    Convenience function to run the complete test suite.
    
    Usage:
        from campaigns.tests.test_runner import run_test_suite
        run_test_suite()
    """
    from django.core.management import execute_from_command_line
    
    # Run tests with custom runner
    execute_from_command_line([
        'manage.py',
        'test',
        'campaigns.tests',
        '--testrunner=campaigns.tests.test_runner.CampaignsTestRunner',
        '--verbosity=2'
    ])


def run_performance_tests():
    """
    Run only performance-related tests.
    """
    from django.core.management import execute_from_command_line
    
    execute_from_command_line([
        'manage.py',
        'test',
        'campaigns.tests.test_workflows.BulkOperationWorkflowTest',
        '--testrunner=campaigns.tests.test_runner.CampaignsTestRunner',
        '--verbosity=2'
    ])


def run_integration_tests():
    """
    Run only integration tests.
    """
    from django.core.management import execute_from_command_line
    
    execute_from_command_line([
        'manage.py',
        'test',
        'campaigns.tests.test_integrations',
        '--testrunner=campaigns.tests.test_runner.CampaignsTestRunner',
        '--verbosity=2'
    ])


def generate_coverage_report():
    """
    Generate a comprehensive coverage report.
    """
    try:
        import coverage
        
        cov = coverage.Coverage()
        cov.load()
        
        print("Coverage Report:")
        print("=" * 50)
        cov.report()
        
        # Generate HTML report
        cov.html_report(directory='htmlcov')
        print("\nHTML coverage report generated in 'htmlcov' directory")
        
    except ImportError:
        print("Coverage package not installed. Install with: pip install coverage")


if __name__ == '__main__':
    """
    Allow running test runner directly.
    
    Usage:
        python campaigns/tests/test_runner.py
    """
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'performance':
            run_performance_tests()
        elif command == 'integration':
            run_integration_tests()
        elif command == 'coverage':
            generate_coverage_report()
        else:
            print("Unknown command. Available commands:")
            print("  performance - Run performance tests")
            print("  integration - Run integration tests")
            print("  coverage - Generate coverage report")
    else:
        run_test_suite()
