"""
Management command to set up initial workflow templates.
"""
from django.core.management.base import BaseCommand
from campaigns.models.workflow import WorkflowTemplate


class Command(BaseCommand):
    help = 'Set up initial workflow templates'

    def handle(self, *args, **options):
        templates = [
            {
                'name': 'Growth Focused',
                'description': 'Aggressive growth strategy with high engagement rates. Focuses on follow and like actions to maximize reach.',
                'category': 'growth',
                'available_actions': {
                    'follow': {
                        'description': 'Follow target accounts',
                        'parameters': {
                            'max_follows_per_hour': 15,
                            'follow_ratio_limit': 1.5
                        }
                    },
                    'like': {
                        'description': 'Like recent posts',
                        'parameters': {
                            'posts_per_account': 3,
                            'max_likes_per_hour': 30
                        }
                    },
                    'story_view': {
                        'description': 'View user stories',
                        'parameters': {
                            'max_views_per_hour': 20
                        }
                    }
                },
                'default_daily_limit': 100,
                'default_hourly_limit': 20,
                'default_delay_between_actions': 45,
                'dag_structure': {
                    'sequence': ['follow', 'like', 'story_view'],
                    'dependencies': {
                        'like': ['follow'],
                        'story_view': ['follow']
                    }
                },
                'is_predefined': True
            },
            {
                'name': 'Engagement Heavy',
                'description': 'Deep engagement strategy with comments and direct messages. Best for building meaningful connections.',
                'category': 'engagement',
                'available_actions': {
                    'follow': {
                        'description': 'Follow target accounts',
                        'parameters': {
                            'max_follows_per_hour': 8
                        }
                    },
                    'like': {
                        'description': 'Like recent posts',
                        'parameters': {
                            'posts_per_account': 2,
                            'max_likes_per_hour': 15
                        }
                    },
                    'comment': {
                        'description': 'Comment on posts',
                        'parameters': {
                            'comments_per_account': 1,
                            'max_comments_per_hour': 5,
                            'comment_templates': [
                                'Great content! 👏',
                                'Love this! 🔥',
                                'Amazing work! ✨'
                            ]
                        }
                    },
                    'dm': {
                        'description': 'Send direct messages',
                        'parameters': {
                            'max_dms_per_hour': 3,
                            'message_template': 'Hi! Love your content. Would love to connect!'
                        }
                    }
                },
                'default_daily_limit': 50,
                'default_hourly_limit': 10,
                'default_delay_between_actions': 120,
                'dag_structure': {
                    'sequence': ['follow', 'like', 'comment', 'dm'],
                    'dependencies': {
                        'like': ['follow'],
                        'comment': ['like'],
                        'dm': ['comment']
                    }
                },
                'is_predefined': True
            },
            {
                'name': 'Conservative Outreach',
                'description': 'Safe and steady approach with minimal risk. Perfect for maintaining account safety while growing slowly.',
                'category': 'conservative',
                'available_actions': {
                    'follow': {
                        'description': 'Follow target accounts',
                        'parameters': {
                            'max_follows_per_hour': 5
                        }
                    },
                    'like': {
                        'description': 'Like recent posts',
                        'parameters': {
                            'posts_per_account': 1,
                            'max_likes_per_hour': 10
                        }
                    }
                },
                'default_daily_limit': 30,
                'default_hourly_limit': 5,
                'default_delay_between_actions': 180,
                'dag_structure': {
                    'sequence': ['follow', 'like'],
                    'dependencies': {
                        'like': ['follow']
                    }
                },
                'is_predefined': True
            },
            {
                'name': 'Account Discovery',
                'description': 'Focus on discovering and analyzing potential targets without direct engagement.',
                'category': 'discovery',
                'available_actions': {
                    'discover': {
                        'description': 'Discover new accounts',
                        'parameters': {
                            'discovery_methods': ['hashtag', 'location', 'similar_accounts'],
                            'max_discoveries_per_hour': 50
                        }
                    },
                    'story_view': {
                        'description': 'View user stories for analysis',
                        'parameters': {
                            'max_views_per_hour': 30
                        }
                    }
                },
                'default_daily_limit': 200,
                'default_hourly_limit': 40,
                'default_delay_between_actions': 30,
                'dag_structure': {
                    'sequence': ['discover', 'story_view'],
                    'dependencies': {
                        'story_view': ['discover']
                    }
                },
                'is_predefined': True
            }
        ]

        created_count = 0
        updated_count = 0

        for template_data in templates:
            template, created = WorkflowTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults=template_data
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created template: {template.name}')
                )
            else:
                # Update existing template
                for key, value in template_data.items():
                    setattr(template, key, value)
                template.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'Updated template: {template.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'Setup complete! Created {created_count} new templates, '
                f'updated {updated_count} existing templates.'
            )
        )
