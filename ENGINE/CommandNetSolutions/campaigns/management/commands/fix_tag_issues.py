"""
Management command to fix critical tag management issues.

This command addresses:
1. Tag search functionality not working for new tags
2. Tag deletion UI problems
3. Database field errors
4. CampaignResult synchronization issues
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from campaigns.models import DynamicTag, TagGroup, CampaignResult
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Fix critical tag management issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix-tag-visibility',
            action='store_true',
            help='Fix tag visibility issues by making non-global tags global if needed',
        )
        parser.add_argument(
            '--fix-specific-tag',
            type=str,
            help='Fix a specific tag by name (make it global)',
        )
        parser.add_argument(
            '--sync-campaign-results',
            action='store_true',
            help='Sync all CampaignResult records to fix field issues',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=' * 60))
        self.stdout.write(self.style.SUCCESS('FIXING TAG MANAGEMENT ISSUES'))
        self.stdout.write(self.style.SUCCESS('=' * 60))

        dry_run = options['dry_run']
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))

        # Fix specific tag visibility
        if options['fix_specific_tag']:
            self.fix_specific_tag(options['fix_specific_tag'], dry_run)

        # Fix general tag visibility issues
        if options['fix_tag_visibility']:
            self.fix_tag_visibility_issues(dry_run)

        # Sync campaign results
        if options['sync_campaign_results']:
            self.sync_campaign_results(dry_run)

        self.stdout.write(self.style.SUCCESS('\n' + '=' * 60))
        self.stdout.write(self.style.SUCCESS('TAG MANAGEMENT FIXES COMPLETE'))
        self.stdout.write(self.style.SUCCESS('=' * 60))

    def fix_specific_tag(self, tag_name, dry_run=False):
        """Fix a specific tag by making it global."""
        self.stdout.write(f'\n1. Fixing specific tag: {tag_name}')
        
        try:
            tag = DynamicTag.objects.filter(name=tag_name).first()
            if not tag:
                self.stdout.write(self.style.ERROR(f'✗ Tag "{tag_name}" not found'))
                return

            self.stdout.write(f'Found tag: {tag.name} (ID: {tag.id})')
            self.stdout.write(f'Current status: Global={tag.is_global}')

            if not tag.is_global:
                if not dry_run:
                    with transaction.atomic():
                        tag.is_global = True
                        tag.save()
                    self.stdout.write(self.style.SUCCESS(f'✓ Updated tag "{tag.name}" to be global'))
                else:
                    self.stdout.write(self.style.WARNING(f'Would update tag "{tag.name}" to be global'))
            else:
                self.stdout.write(f'✓ Tag "{tag.name}" is already global')

            # Check if tag is in any global tag groups
            global_groups = tag.tag_groups.filter(is_global=True)
            if global_groups.exists():
                self.stdout.write(f'✓ Tag is in {global_groups.count()} global tag group(s)')
            else:
                self.stdout.write('ℹ Tag is not in any global tag groups')

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Error fixing tag: {str(e)}'))

    def fix_tag_visibility_issues(self, dry_run=False):
        """Fix tag visibility issues by analyzing and fixing non-global tags."""
        self.stdout.write('\n2. Analyzing tag visibility issues...')

        try:
            # Find tags that are not global and not in global groups
            non_global_tags = DynamicTag.objects.filter(is_global=False)
            self.stdout.write(f'Found {non_global_tags.count()} non-global tags')

            problematic_tags = []
            for tag in non_global_tags:
                # Check if tag is in any global groups
                global_groups = tag.tag_groups.filter(is_global=True)
                if not global_groups.exists():
                    problematic_tags.append(tag)

            self.stdout.write(f'Found {len(problematic_tags)} tags that are not visible in search')

            if problematic_tags:
                self.stdout.write('\nProblematic tags:')
                for tag in problematic_tags[:10]:  # Show first 10
                    self.stdout.write(f'  - {tag.name} (ID: {tag.id})')
                
                if len(problematic_tags) > 10:
                    self.stdout.write(f'  ... and {len(problematic_tags) - 10} more')

                if not dry_run:
                    # Ask for confirmation
                    response = input(f'\nMake all {len(problematic_tags)} tags global? (y/N): ')
                    if response.lower() == 'y':
                        with transaction.atomic():
                            for tag in problematic_tags:
                                tag.is_global = True
                                tag.save()
                        self.stdout.write(self.style.SUCCESS(f'✓ Updated {len(problematic_tags)} tags to be global'))
                    else:
                        self.stdout.write('Skipped making tags global')
                else:
                    self.stdout.write(self.style.WARNING(f'Would update {len(problematic_tags)} tags to be global'))
            else:
                self.stdout.write('✓ No problematic tags found')

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Error analyzing tag visibility: {str(e)}'))

    def sync_campaign_results(self, dry_run=False):
        """Sync CampaignResult records to fix field issues."""
        self.stdout.write('\n3. Syncing CampaignResult records...')

        try:
            results = CampaignResult.objects.all()
            self.stdout.write(f'Found {results.count()} CampaignResult records')

            fixed_count = 0
            for result in results:
                try:
                    # Check for missing or invalid fields
                    needs_fix = False
                    
                    # Ensure average_confidence_score is not None
                    if result.average_confidence_score is None:
                        needs_fix = True
                        if not dry_run:
                            result.average_confidence_score = 0.0

                    # Ensure other fields have valid values
                    if result.total_accounts_found is None:
                        needs_fix = True
                        if not dry_run:
                            result.total_accounts_found = 0

                    if result.total_accounts_processed is None:
                        needs_fix = True
                        if not dry_run:
                            result.total_accounts_processed = 0

                    if result.total_accounts_whitelisted is None:
                        needs_fix = True
                        if not dry_run:
                            result.total_accounts_whitelisted = 0

                    if needs_fix:
                        if not dry_run:
                            result.save()
                        fixed_count += 1

                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'✗ Error fixing result {result.id}: {str(e)}'))

            if fixed_count > 0:
                if not dry_run:
                    self.stdout.write(self.style.SUCCESS(f'✓ Fixed {fixed_count} CampaignResult records'))
                else:
                    self.stdout.write(self.style.WARNING(f'Would fix {fixed_count} CampaignResult records'))
            else:
                self.stdout.write('✓ All CampaignResult records are valid')

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Error syncing CampaignResult records: {str(e)}'))

    def test_tag_search(self, tag_name):
        """Test tag search functionality for a specific tag."""
        self.stdout.write(f'\n4. Testing tag search for: {tag_name}')

        try:
            from django.db.models import Q

            # Simulate the TagSearchView logic
            tags_query = Q(is_global=True)
            
            global_tag_groups = TagGroup.objects.filter(is_global=True)
            if global_tag_groups.exists():
                tags_query |= Q(tag_groups__in=global_tag_groups)

            # Get all tags matching the base query
            all_tags = DynamicTag.objects.filter(tags_query).distinct()
            self.stdout.write(f'Total tags in search pool: {all_tags.count()}')

            # Apply search filter
            search_tags = all_tags.filter(
                Q(name__icontains=tag_name) |
                Q(description__icontains=tag_name) |
                Q(pattern__icontains=tag_name)
            )

            self.stdout.write(f'Tags matching search "{tag_name}": {search_tags.count()}')
            for tag in search_tags:
                self.stdout.write(f'  - {tag.name} (global: {tag.is_global})')

            # Check if the specific tag exists
            specific_tag = DynamicTag.objects.filter(name=tag_name).first()
            if specific_tag:
                self.stdout.write(f'\nSpecific tag analysis:')
                self.stdout.write(f'  Name: {specific_tag.name}')
                self.stdout.write(f'  Global: {specific_tag.is_global}')
                self.stdout.write(f'  Groups: {list(specific_tag.tag_groups.all())}')
                
                if specific_tag in search_tags:
                    self.stdout.write(self.style.SUCCESS('✓ Tag appears in search results'))
                else:
                    self.stdout.write(self.style.ERROR('✗ Tag does NOT appear in search results'))
            else:
                self.stdout.write(self.style.ERROR(f'✗ Tag "{tag_name}" not found in database'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Error testing tag search: {str(e)}'))
