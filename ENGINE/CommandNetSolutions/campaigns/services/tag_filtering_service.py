"""
Tag Filtering Service - Correct Implementation

This service implements tag analysis as a direct synchronous filtering process,
not as a separate asynchronous workflow. Tag conditions are applied immediately
to account data to determine whitelist eligibility.

Key Principles:
1. Tag analysis is a filtering operation, not a separate workflow
2. Evaluation happens synchronously during account processing
3. Whitelist entries are generated directly from filtered accounts
4. No intermediate TagAnalysisResult storage needed for basic filtering
"""

import json
import logging
from typing import List, Dict, Any, Tuple, Optional
from django.db import transaction

from campaigns.models import Campaign, CampaignTag, DynamicTag, CampaignTagRule, CampaignTagCondition
from instagram.models import Accounts, WhiteListEntry

logger = logging.getLogger(__name__)


class TagFilteringService:
    """
    Service for applying tag-based filtering to accounts.

    This service evaluates accounts against campaign tag conditions
    and generates whitelist entries for accounts that pass the filters.
    """

    def __init__(self):
        self.logger = logger

    def filter_and_whitelist_accounts(self, campaign: Campaign, accounts: List[Accounts]) -> Dict[str, Any]:
        """
        Apply tag filtering to accounts and generate whitelist entries.

        This is the main method that implements the correct architecture:
        1. Get campaign tag conditions
        2. Apply filters directly to accounts
        3. Generate whitelist entries for passing accounts

        Args:
            campaign: Campaign instance
            accounts: List of account instances to filter

        Returns:
            Dict containing filtering results and statistics
        """
        self.logger.info(f"Starting tag filtering for campaign {campaign.id} with {len(accounts)} accounts")

        # Get campaign tag rules and conditions
        tag_rules = self._get_campaign_tag_rules(campaign)
        if not tag_rules:
            self.logger.warning(f"No tag rules found for campaign {campaign.id}")
            return self._create_empty_result()

        # Apply filtering
        filtering_results = []
        whitelist_entries = []

        for account in accounts:
            # Evaluate account against all tag rules
            evaluation_result = self._evaluate_account_against_tags(account, tag_rules)

            if evaluation_result['passes_filter']:
                # Generate whitelist entry
                whitelist_entry = self._create_whitelist_entry(
                    account,
                    evaluation_result['matched_tags'],
                    evaluation_result['total_score']
                )
                whitelist_entries.append(whitelist_entry)

            filtering_results.append({
                'account': account,
                'passes_filter': evaluation_result['passes_filter'],
                'matched_tags': evaluation_result['matched_tags'],
                'total_score': evaluation_result['total_score'],
                'evaluation_details': evaluation_result['details']
            })

        # Calculate statistics
        stats = self._calculate_filtering_statistics(filtering_results, whitelist_entries)

        self.logger.info(
            f"Tag filtering completed: {len(whitelist_entries)} accounts whitelisted "
            f"out of {len(accounts)} total accounts"
        )

        return {
            'campaign_id': str(campaign.id),
            'total_accounts': len(accounts),
            'filtered_accounts': len(whitelist_entries),
            'conversion_rate': stats['conversion_rate'],
            'whitelist_entries': whitelist_entries,
            'filtering_results': filtering_results,
            'tag_rules_applied': len(tag_rules),
            'statistics': stats
        }

    def _get_campaign_tag_rules(self, campaign: Campaign) -> List[Dict[str, Any]]:
        """
        Get tag rules and conditions for a campaign.

        This method consolidates both DynamicTag-based rules and CampaignTagRule-based rules.
        """
        rules = []

        # Get DynamicTag-based rules (via CampaignTag)
        campaign_tags = CampaignTag.objects.filter(campaign=campaign)
        for campaign_tag in campaign_tags:
            tag = campaign_tag.tag
            conditions = self._extract_conditions_from_dynamic_tag(tag)
            if conditions:
                rules.append({
                    'type': 'dynamic_tag',
                    'tag': tag,
                    'conditions': conditions,
                    'logic': 'all',  # Default logic for DynamicTag
                    'min_confidence': getattr(campaign_tag, 'min_confidence', 0.0)
                })

        # Get active CampaignTagRule-based rules (simplified - no global filtering)
        tag_rules = CampaignTagRule.objects.filter(
            active=True
        ).prefetch_related('conditions')

        for rule in tag_rules:
            conditions = list(rule.conditions.all())
            if conditions:
                rules.append({
                    'type': 'tag_rule',
                    'rule': rule,
                    'conditions': conditions,
                    'logic': rule.logic,
                    'min_confidence': 0.0
                })

        return rules

    def _extract_conditions_from_dynamic_tag(self, tag: DynamicTag) -> List[Dict[str, Any]]:
        """
        Extract filtering conditions from a DynamicTag.

        This converts the tag's pattern and field into evaluable conditions.
        """
        conditions = []

        if tag.tag_type == 'keyword' and tag.pattern and tag.field:
            # Parse keyword pattern into conditions
            if '>' in tag.pattern:
                # Numeric comparison (e.g., "followers > 1000")
                parts = tag.pattern.split('>')
                if len(parts) == 2:
                    field_name = parts[0].strip()
                    value = parts[1].strip()
                    conditions.append({
                        'field': tag.field,
                        'operator': 'gte',
                        'value': value,
                        'field_type': 'number'
                    })
            elif ',' in tag.pattern:
                # Multiple keywords (e.g., "fitness,gym,workout")
                keywords = [kw.strip() for kw in tag.pattern.split(',')]
                conditions.append({
                    'field': tag.field,
                    'operator': 'contains_any',
                    'value': keywords,
                    'field_type': 'string'
                })
            else:
                # Single keyword
                conditions.append({
                    'field': tag.field,
                    'operator': 'contains',
                    'value': tag.pattern,
                    'field_type': 'string'
                })

        return conditions

    def _evaluate_account_against_tags(self, account: Accounts, tag_rules: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Evaluate an account against all tag rules.

        Returns whether the account passes the filter and detailed evaluation results.
        """
        matched_tags = []
        total_score = 0
        evaluation_details = []

        for rule_data in tag_rules:
            rule_result = self._evaluate_single_rule(account, rule_data)

            evaluation_details.append({
                'rule_type': rule_data['type'],
                'rule_name': self._get_rule_name(rule_data),
                'passed': rule_result['passed'],
                'score': rule_result['score'],
                'matched_conditions': rule_result['matched_conditions']
            })

            if rule_result['passed']:
                matched_tags.append(self._get_rule_name(rule_data))
                total_score += rule_result['score']

        # Determine if account passes overall filter
        passes_filter = len(matched_tags) > 0  # At least one tag must match

        return {
            'passes_filter': passes_filter,
            'matched_tags': matched_tags,
            'total_score': total_score,
            'details': evaluation_details
        }

    def _evaluate_single_rule(self, account: Accounts, rule_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate an account against a single tag rule.
        """
        conditions = rule_data['conditions']
        logic = rule_data['logic']

        matched_conditions = []
        total_score = 0

        for condition in conditions:
            if self._evaluate_condition(account, condition):
                matched_conditions.append(condition)
                # Score calculation depends on condition type
                if hasattr(condition, 'score'):
                    total_score += condition.score
                else:
                    total_score += 1

        # Apply logic (all vs any)
        if logic == 'all':
            passed = len(matched_conditions) == len(conditions)
        else:  # 'any'
            passed = len(matched_conditions) > 0

        return {
            'passed': passed,
            'score': total_score if passed else 0,
            'matched_conditions': matched_conditions
        }

    def _evaluate_condition(self, account: Accounts, condition) -> bool:
        """
        Evaluate a single condition against an account.

        This is the core filtering logic that checks if an account meets a specific condition.
        """
        # Handle both dict-based conditions (from DynamicTag) and model-based conditions (from CampaignTagCondition)
        if isinstance(condition, dict):
            field = condition['field']
            operator = condition['operator']
            value = condition['value']
            field_type = condition.get('field_type', 'string')
        else:
            field = condition.field
            operator = condition.operator
            value = condition.value
            field_type = condition.field_type

        # Get account field value
        account_value = getattr(account, field, None)
        if account_value is None:
            return False

        # Apply operator-specific logic
        return self._apply_operator(account_value, operator, value, field_type)

    def _apply_operator(self, account_value: Any, operator: str, condition_value: Any, field_type: str) -> bool:
        """
        Apply a specific operator to compare account value with condition value.
        """
        try:
            if field_type == 'number':
                account_value = float(account_value)
                condition_value = float(condition_value)

                if operator == 'gte':
                    return account_value >= condition_value
                elif operator == 'lte':
                    return account_value <= condition_value
                elif operator == 'eq':
                    return account_value == condition_value
                elif operator == 'between':
                    if isinstance(condition_value, list) and len(condition_value) == 2:
                        return condition_value[0] <= account_value <= condition_value[1]

            elif field_type == 'string':
                account_str = str(account_value).lower()

                if operator == 'contains':
                    return str(condition_value).lower() in account_str
                elif operator == 'contains_any':
                    if isinstance(condition_value, list):
                        return any(str(val).lower() in account_str for val in condition_value)
                    else:
                        keywords = str(condition_value).split(',')
                        return any(kw.strip().lower() in account_str for kw in keywords)
                elif operator == 'equals':
                    return account_str == str(condition_value).lower()

            elif field_type == 'boolean':
                if isinstance(condition_value, str):
                    condition_bool = condition_value.lower() == 'true'
                else:
                    condition_bool = bool(condition_value)
                return bool(account_value) == condition_bool

            elif field_type == 'array':
                if isinstance(account_value, list):
                    if operator == 'contains_any':
                        if isinstance(condition_value, list):
                            return any(item in account_value for item in condition_value)
                        else:
                            return condition_value in account_value

        except (ValueError, TypeError, AttributeError) as e:
            self.logger.warning(f"Error evaluating condition: {e}")
            return False

        return False

    def _create_whitelist_entry(self, account: Accounts, matched_tags: List[str], total_score: float) -> WhiteListEntry:
        """
        Create a whitelist entry for an account that passed tag filtering.

        Privileges are determined based on matched tags and account characteristics.
        """
        # Determine privileges based on matched tags and account characteristics
        privileges = self._determine_privileges(account, matched_tags, total_score)

        # Create whitelist entry
        whitelist_entry = WhiteListEntry.objects.create(
            account=account,
            dm=privileges['dm'],
            follow=privileges['follow'],
            comment=privileges['comment'],
            post_like=privileges['post_like'],
            discover=privileges['discover'],
            favorite=privileges['favorite']
        )

        return whitelist_entry

    def _determine_privileges(self, account: Accounts, matched_tags: List[str], total_score: float) -> Dict[str, bool]:
        """
        Determine privileges for an account based on filtering results.
        """
        # Base privilege probabilities
        base_probs = {
            'dm': 0.3,
            'follow': 0.8,
            'comment': 0.6,
            'post_like': 0.9,
            'discover': 0.4,
            'favorite': 0.5
        }

        # Calculate multiplier based on account characteristics
        multiplier = 1.0

        # Higher follower count = more privileges
        if account.followers > 100000:
            multiplier += 0.4
        elif account.followers > 50000:
            multiplier += 0.3
        elif account.followers > 10000:
            multiplier += 0.2

        # Verified accounts get more privileges
        if account.is_verified:
            multiplier += 0.3

        # Business/Creator accounts get more privileges
        if account.account_type in ['business', 'creator']:
            multiplier += 0.2

        # Multiple tag matches = more privileges
        if len(matched_tags) > 2:
            multiplier += 0.2
        elif len(matched_tags) > 1:
            multiplier += 0.1

        # High score = more privileges
        if total_score > 5:
            multiplier += 0.2
        elif total_score > 3:
            multiplier += 0.1

        # Apply multiplier and determine privileges
        privileges = {}
        for privilege, base_prob in base_probs.items():
            adjusted_prob = min(base_prob * multiplier, 0.95)  # Cap at 95%
            privileges[privilege] = adjusted_prob > 0.5  # Deterministic for testing

        return privileges

    def _get_rule_name(self, rule_data: Dict[str, Any]) -> str:
        """Get a human-readable name for a rule."""
        if rule_data['type'] == 'dynamic_tag':
            return rule_data['tag'].name
        else:
            return rule_data['rule'].name or f"Rule {rule_data['rule'].id}"

    def _calculate_filtering_statistics(self, filtering_results: List[Dict], whitelist_entries: List[WhiteListEntry]) -> Dict[str, Any]:
        """Calculate comprehensive statistics from filtering results."""
        total_accounts = len(filtering_results)
        whitelisted_accounts = len(whitelist_entries)

        # Calculate conversion rate
        conversion_rate = (whitelisted_accounts / total_accounts * 100) if total_accounts > 0 else 0

        # Calculate privilege distribution
        privilege_stats = {
            'dm': sum(1 for entry in whitelist_entries if entry.dm),
            'follow': sum(1 for entry in whitelist_entries if entry.follow),
            'comment': sum(1 for entry in whitelist_entries if entry.comment),
            'post_like': sum(1 for entry in whitelist_entries if entry.post_like),
            'discover': sum(1 for entry in whitelist_entries if entry.discover),
            'favorite': sum(1 for entry in whitelist_entries if entry.favorite)
        }

        # Calculate tag effectiveness
        tag_matches = {}
        for result in filtering_results:
            for tag in result['matched_tags']:
                tag_matches[tag] = tag_matches.get(tag, 0) + 1

        # Calculate quality metrics
        verified_count = sum(1 for entry in whitelist_entries if entry.account.is_verified)
        high_follower_count = sum(1 for entry in whitelist_entries if entry.account.followers > 50000)
        business_creator_count = sum(1 for entry in whitelist_entries if entry.account.account_type in ['business', 'creator'])

        return {
            'conversion_rate': conversion_rate,
            'privilege_distribution': privilege_stats,
            'tag_effectiveness': tag_matches,
            'quality_metrics': {
                'verified_accounts': verified_count,
                'high_follower_accounts': high_follower_count,
                'business_creator_accounts': business_creator_count
            },
            'average_score': sum(result['total_score'] for result in filtering_results) / total_accounts if total_accounts > 0 else 0
        }

    def _create_empty_result(self) -> Dict[str, Any]:
        """Create an empty result when no tag rules are found."""
        return {
            'total_accounts': 0,
            'filtered_accounts': 0,
            'conversion_rate': 0,
            'whitelist_entries': [],
            'filtering_results': [],
            'tag_rules_applied': 0,
            'statistics': {
                'conversion_rate': 0,
                'privilege_distribution': {},
                'tag_effectiveness': {},
                'quality_metrics': {},
                'average_score': 0
            }
        }
