"""
Integration tests for external service interactions.

This module tests integration with external services including:
- Airflow DAG triggering and monitoring
- Redis communication and caching
- Database transaction handling
- Resource manager integration
- Workflow execution tracking
- Health checks and error handling
"""

import json
import uuid
from unittest.mock import patch, MagicMock, call
from django.test import TestCase, TransactionTestCase
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError

from campaigns.models import (
    Campaign, WorkflowExecution, WorkflowProgressUpdate,
    DynamicTag, CampaignTag
)
from campaigns.tests.base import (
    BaseTestCase, BaseTransactionTestCase, MockExternalServices,
    skip_if_no_external_services
)
from campaigns.tests.factories import (
    CampaignFactory, WorkflowExecutionFactory, DynamicTagFactory,
    CampaignTagFactory, UserFactory
)


class AirflowIntegrationTest(BaseTestCase):
    """Test Airflow integration functionality."""
    
    @patch('campaigns.utils.airflow_client')
    def test_trigger_campaign_workflow(self, mock_airflow):
        """Test triggering a campaign workflow in Airflow."""
        # Setup mock
        mock_airflow.trigger_dag.return_value = {
            'dag_run_id': 'test_run_123',
            'state': 'running'
        }
        
        campaign = CampaignFactory.create(status='draft')
        
        # Import the function that triggers workflows
        from campaigns.utils import trigger_campaign_workflow
        
        # Trigger workflow
        result = trigger_campaign_workflow(campaign.id, 'account_collection')
        
        # Verify Airflow was called correctly
        mock_airflow.trigger_dag.assert_called_once()
        call_args = mock_airflow.trigger_dag.call_args
        
        self.assertEqual(call_args[0][0], 'account_collection_dag')  # DAG ID
        self.assertIn('campaign_id', call_args[1]['conf'])
        self.assertEqual(call_args[1]['conf']['campaign_id'], str(campaign.id))
        
        # Verify return value
        self.assertEqual(result['dag_run_id'], 'test_run_123')
    
    @patch('campaigns.utils.airflow_client')
    def test_get_workflow_status(self, mock_airflow):
        """Test getting workflow status from Airflow."""
        # Setup mock
        mock_airflow.get_dag_run.return_value = {
            'state': 'success',
            'end_date': '2023-01-01T12:00:00Z',
            'execution_date': '2023-01-01T10:00:00Z'
        }
        
        workflow = WorkflowExecutionFactory.create(
            airflow_dag_id='test_dag',
            airflow_run_id='test_run_123'
        )
        
        from campaigns.utils import get_workflow_status
        
        # Get status
        status = get_workflow_status(workflow.airflow_dag_id, workflow.airflow_run_id)
        
        # Verify Airflow was called correctly
        mock_airflow.get_dag_run.assert_called_once_with('test_dag', 'test_run_123')
        
        # Verify return value
        self.assertEqual(status['state'], 'success')
    
    @patch('campaigns.utils.airflow_client')
    def test_airflow_connection_error_handling(self, mock_airflow):
        """Test handling of Airflow connection errors."""
        # Setup mock to raise exception
        mock_airflow.trigger_dag.side_effect = ConnectionError("Cannot connect to Airflow")
        
        campaign = CampaignFactory.create()
        
        from campaigns.utils import trigger_campaign_workflow
        
        # Should handle error gracefully
        with self.assertRaises(ConnectionError):
            trigger_campaign_workflow(campaign.id, 'account_collection')
    
    @patch('campaigns.utils.airflow_client')
    def test_airflow_health_check(self, mock_airflow):
        """Test Airflow health check functionality."""
        # Setup mock
        mock_airflow.get_health.return_value = {'status': 'healthy'}
        
        from campaigns.utils import check_airflow_health
        
        # Check health
        health = check_airflow_health()
        
        # Verify call
        mock_airflow.get_health.assert_called_once()
        self.assertEqual(health['status'], 'healthy')


class RedisIntegrationTest(BaseTestCase):
    """Test Redis integration functionality."""
    
    @patch('campaigns.utils.redis_client')
    def test_cache_campaign_data(self, mock_redis):
        """Test caching campaign data in Redis."""
        # Setup mock
        mock_redis.set.return_value = True
        mock_redis.get.return_value = None
        
        campaign = CampaignFactory.create()
        
        from campaigns.utils import cache_campaign_data, get_cached_campaign_data
        
        # Cache data
        cache_key = f"campaign:{campaign.id}"
        campaign_data = {
            'id': str(campaign.id),
            'name': campaign.name,
            'status': campaign.status
        }
        
        cache_campaign_data(campaign.id, campaign_data)
        
        # Verify Redis was called
        mock_redis.set.assert_called_once()
        call_args = mock_redis.set.call_args
        self.assertEqual(call_args[0][0], cache_key)
        self.assertEqual(json.loads(call_args[0][1]), campaign_data)
    
    @patch('campaigns.utils.redis_client')
    def test_get_cached_campaign_data(self, mock_redis):
        """Test retrieving cached campaign data from Redis."""
        campaign = CampaignFactory.create()
        campaign_data = {
            'id': str(campaign.id),
            'name': campaign.name,
            'status': campaign.status
        }
        
        # Setup mock
        mock_redis.get.return_value = json.dumps(campaign_data)
        
        from campaigns.utils import get_cached_campaign_data
        
        # Get cached data
        cached_data = get_cached_campaign_data(campaign.id)
        
        # Verify Redis was called
        cache_key = f"campaign:{campaign.id}"
        mock_redis.get.assert_called_once_with(cache_key)
        
        # Verify return value
        self.assertEqual(cached_data, campaign_data)
    
    @patch('campaigns.utils.redis_client')
    def test_redis_connection_error_handling(self, mock_redis):
        """Test handling of Redis connection errors."""
        # Setup mock to raise exception
        mock_redis.ping.side_effect = ConnectionError("Cannot connect to Redis")
        
        from campaigns.utils import check_redis_health
        
        # Should handle error gracefully
        with self.assertRaises(ConnectionError):
            check_redis_health()
    
    @patch('campaigns.utils.redis_client')
    def test_workflow_queue_management(self, mock_redis):
        """Test workflow queue management in Redis."""
        # Setup mock
        mock_redis.lpush.return_value = 1
        mock_redis.rpop.return_value = json.dumps({'campaign_id': 'test_id'})
        mock_redis.llen.return_value = 5
        
        from campaigns.utils import add_to_workflow_queue, get_from_workflow_queue, get_queue_length
        
        # Add to queue
        workflow_data = {'campaign_id': 'test_id', 'workflow_type': 'account_collection'}
        add_to_workflow_queue(workflow_data)
        
        # Verify Redis was called
        mock_redis.lpush.assert_called_once()
        
        # Get from queue
        queued_data = get_from_workflow_queue()
        
        # Verify Redis was called
        mock_redis.rpop.assert_called_once()
        self.assertEqual(queued_data['campaign_id'], 'test_id')
        
        # Get queue length
        length = get_queue_length()
        mock_redis.llen.assert_called_once()
        self.assertEqual(length, 5)


class WorkflowExecutionIntegrationTest(BaseTransactionTestCase):
    """Test workflow execution integration with external services."""
    
    def test_workflow_execution_creation_with_transaction(self):
        """Test workflow execution creation within database transaction."""
        campaign = CampaignFactory.create()
        
        with transaction.atomic():
            workflow = WorkflowExecutionFactory.create(
                campaign=campaign,
                workflow_type='account_collection',
                status='pending'
            )
            
            # Simulate external service call failure
            try:
                # This would normally trigger external service
                raise ConnectionError("External service unavailable")
            except ConnectionError:
                # Transaction should rollback
                transaction.set_rollback(True)
        
        # Workflow should not exist due to rollback
        self.assertFalse(WorkflowExecution.objects.filter(id=workflow.id).exists())
    
    @patch('campaigns.utils.airflow_client')
    @patch('campaigns.utils.redis_client')
    def test_end_to_end_workflow_execution(self, mock_redis, mock_airflow):
        """Test complete workflow execution from start to finish."""
        # Setup mocks
        mock_airflow.trigger_dag.return_value = {'dag_run_id': 'test_run_123'}
        mock_airflow.get_dag_run.return_value = {'state': 'running'}
        mock_redis.set.return_value = True
        mock_redis.get.return_value = None
        
        campaign = CampaignFactory.create(status='draft')
        
        # Start workflow
        from campaigns.utils import start_campaign_workflow
        
        workflow = start_campaign_workflow(campaign.id, 'account_collection')
        
        # Verify workflow was created
        self.assertIsNotNone(workflow)
        self.assertEqual(workflow.campaign, campaign)
        self.assertEqual(workflow.workflow_type, 'account_collection')
        self.assertEqual(workflow.status, 'running')
        
        # Verify external services were called
        mock_airflow.trigger_dag.assert_called_once()
        mock_redis.set.assert_called()
    
    def test_workflow_progress_tracking(self):
        """Test workflow progress tracking and updates."""
        workflow = WorkflowExecutionFactory.create(
            total_steps=100,
            completed_steps=0,
            progress_percentage=0
        )
        
        # Update progress
        workflow.update_progress(25, "Collecting accounts")
        
        # Verify progress was updated
        workflow.refresh_from_db()
        self.assertEqual(workflow.completed_steps, 25)
        self.assertEqual(workflow.progress_percentage, 25.0)
        
        # Check progress update was created
        progress_update = WorkflowProgressUpdate.objects.filter(workflow=workflow).first()
        self.assertIsNotNone(progress_update)
        self.assertEqual(progress_update.step_name, "Collecting accounts")


class ResourceManagerIntegrationTest(BaseTestCase):
    """Test resource manager integration."""
    
    @patch('campaigns.utils.get_available_resources')
    def test_resource_allocation_check(self, mock_get_resources):
        """Test checking available resources before workflow execution."""
        # Setup mock
        mock_get_resources.return_value = {
            'cpu_usage': 45.0,
            'memory_usage': 60.0,
            'active_workflows': 3,
            'max_concurrent_workflows': 5
        }
        
        from campaigns.utils import can_start_workflow
        
        # Check if workflow can start
        can_start = can_start_workflow()
        
        # Should be able to start (under limits)
        self.assertTrue(can_start)
        
        # Test with high resource usage
        mock_get_resources.return_value['active_workflows'] = 5
        can_start = can_start_workflow()
        
        # Should not be able to start (at limit)
        self.assertFalse(can_start)
    
    @patch('campaigns.utils.pause_workflow')
    @patch('campaigns.utils.resume_workflow')
    def test_workflow_pause_resume(self, mock_resume, mock_pause):
        """Test workflow pause and resume functionality."""
        workflow = WorkflowExecutionFactory.create(status='running')
        
        from campaigns.utils import pause_campaign_workflow, resume_campaign_workflow
        
        # Pause workflow
        pause_campaign_workflow(workflow.id)
        mock_pause.assert_called_once_with(workflow.airflow_dag_id, workflow.airflow_run_id)
        
        # Resume workflow
        resume_campaign_workflow(workflow.id)
        mock_resume.assert_called_once_with(workflow.airflow_dag_id, workflow.airflow_run_id)


class HealthCheckIntegrationTest(BaseTestCase):
    """Test health check functionality for external services."""
    
    @patch('campaigns.utils.redis_client')
    @patch('campaigns.utils.airflow_client')
    def test_comprehensive_health_check(self, mock_airflow, mock_redis):
        """Test comprehensive health check of all external services."""
        # Setup mocks
        mock_redis.ping.return_value = True
        mock_airflow.get_health.return_value = {'status': 'healthy'}
        
        from campaigns.utils import perform_health_check
        
        # Perform health check
        health_status = perform_health_check()
        
        # Verify all services were checked
        mock_redis.ping.assert_called_once()
        mock_airflow.get_health.assert_called_once()
        
        # Verify health status
        self.assertTrue(health_status['redis']['healthy'])
        self.assertTrue(health_status['airflow']['healthy'])
        self.assertTrue(health_status['overall']['healthy'])
    
    @patch('campaigns.utils.redis_client')
    @patch('campaigns.utils.airflow_client')
    def test_health_check_with_failures(self, mock_airflow, mock_redis):
        """Test health check when services are unavailable."""
        # Setup mocks to fail
        mock_redis.ping.side_effect = ConnectionError("Redis unavailable")
        mock_airflow.get_health.side_effect = ConnectionError("Airflow unavailable")
        
        from campaigns.utils import perform_health_check
        
        # Perform health check
        health_status = perform_health_check()
        
        # Verify health status shows failures
        self.assertFalse(health_status['redis']['healthy'])
        self.assertFalse(health_status['airflow']['healthy'])
        self.assertFalse(health_status['overall']['healthy'])
        
        # Verify error messages are included
        self.assertIn('error', health_status['redis'])
        self.assertIn('error', health_status['airflow'])


class DatabaseIntegrationTest(BaseTransactionTestCase):
    """Test database integration and transaction handling."""
    
    def test_atomic_campaign_creation_with_targets(self):
        """Test atomic creation of campaign with targets."""
        user = UserFactory.create()
        
        with transaction.atomic():
            campaign = CampaignFactory.create(creator=user)
            
            # Add targets
            from campaigns.tests.factories import LocationTargetFactory, UsernameTargetFactory
            LocationTargetFactory.create(campaign=campaign, location_id='loc_1')
            LocationTargetFactory.create(campaign=campaign, location_id='loc_2')
            UsernameTargetFactory.create(campaign=campaign, username='user1')
        
        # Verify all objects were created
        self.assertTrue(Campaign.objects.filter(id=campaign.id).exists())
        self.assertEqual(campaign.location_targets.count(), 2)
        self.assertEqual(campaign.username_targets.count(), 1)
    
    def test_rollback_on_constraint_violation(self):
        """Test transaction rollback on constraint violation."""
        campaign = CampaignFactory.create()
        tag = DynamicTagFactory.create()
        
        # Create first campaign tag
        CampaignTagFactory.create(campaign=campaign, tag=tag)
        
        # Try to create duplicate (should violate unique constraint)
        with self.assertRaises(Exception):  # IntegrityError or similar
            with transaction.atomic():
                CampaignTagFactory.create(campaign=campaign, tag=tag)
        
        # Should only have one campaign tag
        self.assertEqual(CampaignTag.objects.filter(campaign=campaign, tag=tag).count(), 1)
