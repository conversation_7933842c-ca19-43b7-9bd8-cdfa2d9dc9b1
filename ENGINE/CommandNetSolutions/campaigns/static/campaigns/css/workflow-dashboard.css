/* Workflow Dashboard Styles */

.workflow-dashboard {
    background-color: #f8f9fa;
    min-height: 100vh;
}

.workflow-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.workflow-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.workflow-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px 12px 0 0;
    border: none;
    color: white;
}

.workflow-status-badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.workflow-progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.workflow-progress .progress-bar {
    border-radius: 4px;
}

.workflow-actions .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.workflow-actions .btn:hover {
    transform: translateY(-1px);
}

.campaign-selection-card {
    border: 2px solid transparent;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.campaign-selection-card:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.campaign-selection-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.template-selection-card {
    border: 2px solid transparent;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.template-selection-card:hover {
    border-color: #28a745;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
}

.template-selection-card.selected {
    border-color: #28a745;
    background-color: #f8fff8;
}

.workflow-metric {
    text-align: center;
    padding: 1.5rem;
    border-radius: 12px;
    background: white;
    border: 1px solid #e9ecef;
}

.workflow-metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: #495057;
}

.workflow-metric-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.workflow-timeline {
    position: relative;
    padding-left: 2rem;
}

.workflow-timeline::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #28a745);
}

.workflow-timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.workflow-timeline-item::before {
    content: '';
    position: absolute;
    left: -0.75rem;
    top: 0.5rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #007bff;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #007bff;
}

.workflow-timeline-item.completed::before {
    background: #28a745;
    box-shadow: 0 0 0 2px #28a745;
}

.workflow-timeline-item.failed::before {
    background: #dc3545;
    box-shadow: 0 0 0 2px #dc3545;
}

.action-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 12px;
    background-color: #e9ecef;
    color: #495057;
    margin: 0.125rem;
}

.action-badge.enabled {
    background-color: #d4edda;
    color: #155724;
}

.template-category-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-weight: 500;
}

.template-category-growth {
    background-color: #d1ecf1;
    color: #0c5460;
}

.template-category-engagement {
    background-color: #d4edda;
    color: #155724;
}

.template-category-conservative {
    background-color: #fff3cd;
    color: #856404;
}

.template-category-discovery {
    background-color: #e2e3e5;
    color: #383d41;
}

.template-category-custom {
    background-color: #f8d7da;
    color: #721c24;
}

.workflow-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.btn-workflow-action {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.2s ease;
}

.btn-workflow-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.workflow-form-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
}

.workflow-form-section h5 {
    color: #495057;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.step-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    font-weight: 600;
    margin-right: 0.75rem;
}

.step-indicator.completed {
    background-color: #28a745;
}

.step-indicator.disabled {
    background-color: #6c757d;
}

@media (max-width: 768px) {
    .workflow-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .workflow-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .workflow-form-section {
        padding: 1rem;
    }
}

/* Animation for loading states */
.workflow-loading {
    opacity: 0.6;
    pointer-events: none;
}

.workflow-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error states */
.workflow-success {
    border-left: 4px solid #28a745;
    background-color: #d4edda;
    padding: 1rem;
    border-radius: 0 8px 8px 0;
}

.workflow-error {
    border-left: 4px solid #dc3545;
    background-color: #f8d7da;
    padding: 1rem;
    border-radius: 0 8px 8px 0;
}

.workflow-warning {
    border-left: 4px solid #ffc107;
    background-color: #fff3cd;
    padding: 1rem;
    border-radius: 0 8px 8px 0;
}
