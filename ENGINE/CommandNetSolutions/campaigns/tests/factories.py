"""
Test data factories for the campaigns app.

This module provides factory classes for creating test data with realistic
and consistent values. Factories help ensure tests are isolated and repeatable.

Usage:
    # Create a simple campaign
    campaign = CampaignFactory()

    # Create a campaign with specific attributes
    campaign = CampaignFactory(name='My Campaign', status='running')

    # Create related objects
    tag = DynamicTagFactory(tag_groups=[TagGroupFactory()])
    campaign_tag = CampaignTagFactory(campaign=campaign, tag=tag)
"""

import uuid
import json
from datetime import datetime, timedelta
from django.utils import timezone
from django.contrib.auth.models import User

from campaigns.models import (
    Campaign, DynamicTag, TagGroup, TagCategory, CampaignTag,
    LocationTarget, UsernameTarget, WorkflowExecution, WorkflowProgressUpdate,
    TagAnalysisResult, CampaignResult, TagMetrics, CampaignTagRule,
    CampaignTagCondition
)


class UserFactory:
    """Factory for creating test users."""

    @staticmethod
    def create(**kwargs):
        defaults = {
            'username': f'user_{uuid.uuid4().hex[:8]}',
            'email': f'user_{uuid.uuid4().hex[:8]}@example.com',
            'password': 'testpassword123',
            'first_name': 'Test',
            'last_name': 'User',
            'is_active': True
        }
        defaults.update(kwargs)

        password = defaults.pop('password')
        user = User.objects.create_user(**defaults)
        user.set_password(password)
        user.save()
        return user

    @staticmethod
    def create_admin(**kwargs):
        defaults = {
            'username': f'admin_{uuid.uuid4().hex[:8]}',
            'email': f'admin_{uuid.uuid4().hex[:8]}@example.com',
            'password': 'adminpassword123',
            'is_staff': True,
            'is_superuser': True
        }
        defaults.update(kwargs)
        return UserFactory.create(**defaults)


class TagCategoryFactory:
    """Factory for creating test tag categories."""

    @staticmethod
    def create(**kwargs):
        defaults = {
            'name': f'Category {uuid.uuid4().hex[:8]}',
            'description': 'Test category description',
            'color': '#007bff',
            'icon': 'tag',
            'priority': 1
        }
        defaults.update(kwargs)
        return TagCategory.objects.create(**defaults)

    @staticmethod
    def create_fitness():
        return TagCategoryFactory.create(
            name='Fitness',
            description='Fitness and health related tags',
            color='#28a745',
            icon='dumbbell',
            priority=10
        )

    @staticmethod
    def create_fashion():
        return TagCategoryFactory.create(
            name='Fashion',
            description='Fashion and style related tags',
            color='#e83e8c',
            icon='tshirt',
            priority=9
        )


class TagGroupFactory:
    """Factory for creating test tag groups."""

    @staticmethod
    def create(**kwargs):
        defaults = {
            'name': f'Group {uuid.uuid4().hex[:8]}',
            'description': 'Test group description',
            'color': '#6c757d',
            'is_global': True,
            'priority': 1
        }

        # Handle creator separately
        if 'creator' not in kwargs:
            defaults['creator'] = UserFactory.create()

        defaults.update(kwargs)
        return TagGroup.objects.create(**defaults)

    @staticmethod
    def create_influencer_group():
        return TagGroupFactory.create(
            name='Influencers',
            description='Tags for identifying influencers',
            color='#ffc107',
            is_global=True,
            priority=10
        )


class DynamicTagFactory:
    """Factory for creating test dynamic tags."""

    @staticmethod
    def create(**kwargs):
        defaults = {
            'name': f'Tag {uuid.uuid4().hex[:8]}',
            'description': 'Test tag description',
            'tag_type': 'keyword',
            'pattern': 'test',
            'field': 'bio',
            'is_global': True,
            'is_system': False,
            'weight': 1.0
        }

        # Handle relationships separately
        category = kwargs.pop('category', None)
        tag_groups = kwargs.pop('tag_groups', None)

        if category is None:
            category = TagCategoryFactory.create()

        defaults.update(kwargs)
        defaults['category'] = category

        tag = DynamicTag.objects.create(**defaults)

        # Add to tag groups
        if tag_groups is None:
            tag_groups = [TagGroupFactory.create()]

        for group in tag_groups:
            tag.tag_groups.add(group)

        return tag

    @staticmethod
    def create_fitness_tag():
        category = TagCategoryFactory.create_fitness()
        group = TagGroupFactory.create(name='Fitness Enthusiasts')

        return DynamicTagFactory.create(
            name='Fitness Enthusiast',
            description='Identifies fitness enthusiasts',
            pattern='fitness|gym|workout|training',
            field='bio',
            tag_type='regex',
            category=category,
            tag_groups=[group],
            weight=2.0
        )

    @staticmethod
    def create_system_tag():
        return DynamicTagFactory.create(
            name='System Tag',
            description='System-generated tag',
            is_system=True,
            is_global=True,
            pattern='verified',
            field='is_verified'
        )


class CampaignFactory:
    """Factory for creating test campaigns."""

    @staticmethod
    def create(**kwargs):
        defaults = {
            'name': f'Campaign {uuid.uuid4().hex[:8]}',
            'description': 'Test campaign description',
            'status': 'draft',
            'target_type': 'location',
            'audience_type': 'followers',
            'dmp_conf': {},
            'airflow_run_id': 'pending'
        }

        # Handle creator separately
        if 'creator' not in kwargs:
            defaults['creator'] = UserFactory.create()

        defaults.update(kwargs)
        return Campaign.objects.create(**defaults)

    @staticmethod
    def create_running_campaign():
        return CampaignFactory.create(
            name='Running Campaign',
            status='running',
            airflow_run_id=f'run_{uuid.uuid4().hex[:8]}'
        )

    @staticmethod
    def create_completed_campaign():
        return CampaignFactory.create(
            name='Completed Campaign',
            status='completed'
        )


class LocationTargetFactory:
    """Factory for creating test location targets."""

    @staticmethod
    def create(**kwargs):
        defaults = {
            'location_id': f'loc_{uuid.uuid4().hex[:8]}',
            'city': 'Test City',
            'country': 'Test Country'
        }

        # Handle campaign separately
        if 'campaign' not in kwargs:
            defaults['campaign'] = CampaignFactory.create()

        defaults.update(kwargs)
        return LocationTarget.objects.create(**defaults)


class UsernameTargetFactory:
    """Factory for creating test username targets."""

    @staticmethod
    def create(**kwargs):
        defaults = {
            'username': f'testuser_{uuid.uuid4().hex[:8]}',
            'audience_type': 'followers',
            'processed': False
        }

        # Handle campaign separately
        if 'campaign' not in kwargs:
            defaults['campaign'] = CampaignFactory.create()

        defaults.update(kwargs)
        return UsernameTarget.objects.create(**defaults)


class CampaignTagFactory:
    """Factory for creating test campaign tags."""

    @staticmethod
    def create(**kwargs):
        defaults = {
            'is_required': False
        }

        # Handle relationships separately
        if 'campaign' not in kwargs:
            defaults['campaign'] = CampaignFactory.create()

        if 'tag' not in kwargs:
            defaults['tag'] = DynamicTagFactory.create()

        defaults.update(kwargs)
        return CampaignTag.objects.create(**defaults)


class WorkflowExecutionFactory:
    """Factory for creating test workflow executions."""

    @staticmethod
    def create(**kwargs):
        defaults = {
            'workflow_type': 'account_collection',
            'status': 'pending',
            'started_at': timezone.now(),
            'airflow_dag_id': 'test_dag',
            'airflow_run_id': f'run_{uuid.uuid4().hex[:8]}',
            'progress_percentage': 0,
            'total_steps': 100,
            'completed_steps': 0
        }

        # Handle campaign separately
        if 'campaign' not in kwargs:
            defaults['campaign'] = CampaignFactory.create()

        defaults.update(kwargs)
        return WorkflowExecution.objects.create(**defaults)


class CampaignResultFactory:
    """Factory for creating test campaign results."""

    @staticmethod
    def create(**kwargs):
        defaults = {
            'total_accounts_collected': 100,
            'total_accounts_analyzed': 95,
            'total_accounts_whitelisted': 25,
            'analysis_completion_percentage': 95.0,
            'last_updated': timezone.now()
        }

        # Handle campaign separately
        if 'campaign' not in kwargs:
            defaults['campaign'] = CampaignFactory.create()

        defaults.update(kwargs)
        return CampaignResult.objects.create(**defaults)


class TagAnalysisResultFactory:
    """Factory for creating test tag analysis results."""

    @staticmethod
    def create(**kwargs):
        defaults = {
            'account_id': f'acc_{uuid.uuid4().hex[:8]}',
            'matched': True,
            'confidence_score': 0.85,
            'match_details': {'reason': 'keyword match in bio'}
        }

        # Handle relationships separately
        if 'campaign' not in kwargs:
            defaults['campaign'] = CampaignFactory.create()

        if 'tag' not in kwargs:
            defaults['tag'] = DynamicTagFactory.create()

        defaults.update(kwargs)
        return TagAnalysisResult.objects.create(**defaults)
