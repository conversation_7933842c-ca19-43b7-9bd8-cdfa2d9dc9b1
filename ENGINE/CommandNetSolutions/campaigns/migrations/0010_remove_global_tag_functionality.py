# Generated manually to remove global tag functionality

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0009_remove_average_confidence_score'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='dynamictag',
            name='is_global',
        ),
        migrations.RemoveField(
            model_name='taggroup',
            name='is_global',
        ),
        migrations.RemoveField(
            model_name='campaigntagrule',
            name='is_global',
        ),
    ]
