{% extends 'campaigns/base.html' %}
{% load static %}

{% block title %}{% if form.instance.pk %}Edit{% else %}Create{% endif %} Tag{% endblock %}

{% block extra_css %}
<style>
    .tag-form-container {
        max-width: 800px;
        margin: 0 auto;
    }
    .tag-form-header {
        background-color: #f8f9fa;
        border-radius: 8px 8px 0 0;
        padding: 20px;
        border-bottom: 1px solid #dee2e6;
    }
    .tag-form-body {
        padding: 25px;
        background-color: #fff;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    .form-section {
        margin-bottom: 25px;
    }
    .form-label {
        font-weight: 500;
    }
    .action-buttons {
        display: flex;
        justify-content: flex-end;
        margin-top: 30px;
    }
    .tag-info-box {
        background-color: #e9f7fe;
        border-left: 4px solid #0d6efd;
        padding: 15px;
        margin: 20px 0;
        border-radius: 4px;
    }
    .tag-preview {
        background-color: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        margin-top: 20px;
        border: 1px solid #dee2e6;
    }
    .tag-badge {
        display: inline-block;
        padding: 6px 12px;
        background-color: #0d6efd;
        color: white;
        border-radius: 30px;
        font-size: 14px;
        margin-right: 8px;
        margin-bottom: 8px;
    }
    .tag-badge i {
        margin-right: 5px;
    }
    .tag-preview-title {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 10px;
        font-weight: 500;
    }
    .form-step {
        display: none;
    }
    .form-step.active {
        display: block;
    }
    .step-indicator {
        display: flex;
        margin-bottom: 20px;
    }
    .step {
        flex: 1;
        text-align: center;
        padding: 10px;
        position: relative;
    }
    .step.active {
        font-weight: bold;
        color: #0d6efd;
    }
    .step.completed {
        color: #198754;
    }
    .step:not(:last-child):after {
        content: '';
        position: absolute;
        top: 50%;
        right: 0;
        width: 100%;
        height: 2px;
        background-color: #dee2e6;
        z-index: -1;
    }
    .step.completed:not(:last-child):after {
        background-color: #198754;
    }
    .step-number {
        display: inline-block;
        width: 30px;
        height: 30px;
        line-height: 30px;
        border-radius: 50%;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        margin-right: 5px;
    }
    .step.active .step-number {
        background-color: #0d6efd;
        color: white;
        border-color: #0d6efd;
    }
    .step.completed .step-number {
        background-color: #198754;
        color: white;
        border-color: #198754;
    }
    /* Condition Builder Styles */
    .condition {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin-bottom: 20px;
        background-color: #fff;
    }
    .condition .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 12px 15px;
    }
    .condition .card-body {
        padding: 20px;
    }
    .field-category-selector .btn-group {
        display: flex;
    }
    .field-category-selector .btn {
        flex: 1;
        padding: 8px 12px;
        font-size: 14px;
    }
    .value-help {
        font-size: 12px;
        color: #6c757d;
    }
</style>
{% endblock %}



{% block campaign_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title">{% if form.instance.pk %}Edit{% else %}Create{% endif %} Tag</h1>
        <p class="page-subtitle">{% if form.instance.pk %}Update{% else %}Create{% endif %} a tag to categorize accounts</p>
    </div>
    <div>
        <a href="{% url 'campaigns:dynamic_tag_list' %}" class="btn btn-outline-primary btn-icon back-button">
            <i class="fas fa-arrow-left me-1"></i> <span>Back</span>
        </a>
    </div>
</div>

<div class="tag-form-container">
    <div class="tag-form-header">
        <div class="d-flex align-items-center">
            <i class="fas fa-tag me-2 text-primary" style="font-size: 1.5rem;"></i>
            <div>
                <h5 class="mb-0">{% if form.instance.pk %}Edit{% else %}Create{% endif %} Tag</h5>
                <small class="text-muted">Tags help you categorize accounts based on common characteristics</small>
            </div>
        </div>
    </div>

    <div class="tag-form-body">


        <form method="post" id="tag-form" action="{% url 'campaigns:dynamic_tag_create' %}">
            {% csrf_token %}
            <!-- Hidden field to indicate if this is a create or update operation -->
            <input type="hidden" id="form_operation" name="form_operation" value="create" />
            <!-- Error message for tag not found -->
            <div id="tag-not-found-error" class="alert alert-danger mb-3" style="display: none;">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                    <div>
                        <h4 class="alert-heading mb-1">Tag Not Found</h4>
                        <p class="mb-0">The tag you are trying to update does not exist. It may have been deleted or never existed.</p>
                    </div>
                </div>
                <hr>
                <div class="d-flex justify-content-end">
                    <a href="{% url 'campaigns:dynamic_tag_list' %}" class="btn btn-primary">
                        <i class="fas fa-list me-1"></i> Go to Tag List
                    </a>
                </div>
            </div>



            <!-- Check if tag exists for update view -->
            {% if form.instance.pk and not form.instance.id %}
            <script>
                // Tag doesn't exist, show error message
                document.addEventListener('DOMContentLoaded', function() {
                    document.getElementById('tag-not-found-error').style.display = 'block';
                    document.getElementById('tag-form').style.display = 'none';
                });
            </script>
            {% endif %}
            <!-- Hidden inputs for form fields -->
            <input type="hidden" id="id_tag_type" name="tag_type" value="{{ form.tag_type.value|default:'keyword' }}">
            <input type="hidden" id="id_field" name="field" value="{{ form.field.value|default:'bio' }}">
            <input type="hidden" id="id_pattern" name="pattern" value="{{ form.pattern.value|default:'' }}">

            <!-- Step Indicator -->
            <div class="step-indicator mb-4">
                <div class="step active" data-step="1">
                    <span class="step-number">1</span>
                    <span class="d-none d-md-inline">Basic Info</span>
                </div>
                <div class="step" data-step="2">
                    <span class="step-number">2</span>
                    <span class="d-none d-md-inline">Conditions</span>
                </div>
                <div class="step" data-step="3">
                    <span class="step-number">3</span>
                    <span class="d-none d-md-inline">Review</span>
                </div>
            </div>

            <!-- Step 1: Basic Information -->
            <div class="form-step active" id="step-1">
                <h4 class="mb-3">Basic Information</h4>
                <div class="form-section">
                    <div class="mb-3">
                        <label for="id_name" class="form-label">Tag Name</label>
                        <input type="text" class="form-control" id="id_name" name="name" value="{{ form.name.value|default:'' }}" required>
                        <div class="form-text">Enter a descriptive name (e.g., "Travel Enthusiasts", "Photographers")</div>
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="id_description" class="form-label">Description (Optional)</label>
                        <textarea class="form-control" id="id_description" name="description" rows="2">{{ form.description.value|default:'' }}</textarea>
                        <div class="form-text">Brief description of what this tag represents</div>
                        {% if form.description.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.description.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="id_category" class="form-label">Category (Optional)</label>
                        <select class="form-select" id="id_category" name="category">
                            <option value="">No Category</option>
                            {% for category in form.fields.category.queryset %}
                            <option value="{{ category.id }}" {% if form.category.value == category.id %}selected{% endif %}>{{ category.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Group related tags together by assigning them to a category</div>
                    </div>

                    <!-- Global tag functionality removed as per requirements -->

                    <!-- Confidence level field removed as per requirements -->
                </div>
                <div class="d-flex justify-content-end">
                    <button type="button" class="btn btn-primary next-step" data-next="2">
                        Next <i class="fas fa-arrow-right ms-1"></i>
                    </button>
                </div>
            </div>

            <!-- Step 2: Conditions -->
            <div class="form-step" id="step-2">
                <h4 class="mb-3">Tag Conditions</h4>
                <p class="text-muted mb-4">Define one or more conditions for this tag. Accounts matching these conditions will be tagged.</p>

                <!-- Conditions Container -->
                <div id="conditions-container">
                    <!-- Condition Template - Will be cloned by JavaScript -->
                    <div class="condition-template d-none">
                        <div class="condition card mb-3">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Condition</h5>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-danger remove-condition">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Field Category Selection -->
                                <div class="mb-3">
                                    <label class="form-label">Field Category</label>
                                    <div class="field-category-selector">
                                        <div class="btn-group w-100" role="group">
                                            <input type="radio" class="btn-check field-category-radio" name="field-category-0" id="field-category-text-0" value="text" checked>
                                            <label class="btn btn-outline-primary" for="field-category-text-0">Text</label>

                                            <input type="radio" class="btn-check field-category-radio" name="field-category-0" id="field-category-numeric-0" value="numeric">
                                            <label class="btn btn-outline-primary" for="field-category-numeric-0">Numeric</label>

                                            <input type="radio" class="btn-check field-category-radio" name="field-category-0" id="field-category-lists-0" value="lists">
                                            <label class="btn btn-outline-primary" for="field-category-lists-0">Lists</label>

                                            <input type="radio" class="btn-check field-category-radio" name="field-category-0" id="field-category-boolean-0" value="boolean">
                                            <label class="btn btn-outline-primary" for="field-category-boolean-0">Boolean</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Field Selection -->
                                <div class="mb-3">
                                    <label class="form-label">Field</label>
                                    <select class="form-select field-select">
                                        <option value="">Select a field</option>
                                        <!-- Options will be populated by JavaScript -->
                                    </select>
                                </div>

                                <!-- Operator Selection -->
                                <div class="mb-3">
                                    <label class="form-label">Operator</label>
                                    <select class="form-select operator-select">
                                        <option value="">Select an operator</option>
                                        <!-- Options will be populated by JavaScript -->
                                    </select>
                                </div>

                                <!-- Value Input -->
                                <div class="mb-3 value-input-container">
                                    <label class="form-label">Value</label>
                                    <input type="text" class="form-control value-input" placeholder="Enter value">
                                    <div class="form-text value-help">Enter the value to match against the selected field.</div>
                                </div>

                                <!-- Required Checkbox -->
                                <div class="form-check">
                                    <input class="form-check-input required-check" type="checkbox" checked>
                                    <label class="form-check-label">
                                        Required condition
                                    </label>
                                    <div class="form-text">If checked, this condition must be met for the tag to be applied.</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- No Conditions Message -->
                    <div id="no-conditions-message" class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No conditions added yet. Click the "Add Condition" button below to create your first condition.
                    </div>

                    <!-- Actual Conditions Will Be Added Here -->
                    <div id="active-conditions"></div>
                </div>

                <!-- Add Condition Button -->
                <div class="text-center mb-4">
                    <button type="button" class="btn btn-success btn-lg" id="add-condition-btn">
                        <i class="fas fa-plus me-1"></i> Add Condition
                    </button>
                </div>



                <!-- Operator Groups (Hidden) -->
                <div id="string-operators" class="operator-group" style="display: none;"></div>
                <div id="number-operators" class="operator-group" style="display: none;"></div>
                <div id="array-operators" class="operator-group" style="display: none;"></div>
                <div id="boolean-operators" class="operator-group" style="display: none;"></div>

                <!-- Hidden inputs for condition logic -->
                <input type="radio" class="d-none" name="condition-logic" id="logic-all" value="all" checked>
                <input type="radio" class="d-none" name="condition-logic" id="logic-any" value="any">

                <!-- Navigation Buttons -->
                <div class="d-flex justify-content-between mt-4">
                    <button type="button" class="btn btn-outline-secondary prev-step" data-prev="1">
                        <i class="fas fa-arrow-left me-1"></i> Previous
                    </button>
                    <button type="button" class="btn btn-primary next-step" data-next="3" id="conditions-next-btn">
                        Next <i class="fas fa-arrow-right ms-1"></i>
                    </button>
                </div>
            </div>

            <!-- Step 3: Review and Create -->
            <div class="form-step" id="step-3">
                <h4 class="mb-3">Review Your Tag</h4>

                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Tag Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Name:</div>
                            <div class="col-md-8" id="review-name">{% if form.name.value %}{{ form.name.value }}{% else %}Tag Name{% endif %}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Description:</div>
                            <div class="col-md-8" id="review-description">{% if form.description.value %}{{ form.description.value }}{% else %}-{% endif %}</div>
                        </div>
                        <!-- Global status removed as per requirements -->
                    </div>
                </div>

                <!-- Conditions Review -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Conditions</h5>
                    </div>
                    <div class="card-body">
                        <div id="review-conditions">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No conditions added yet. Go back to add conditions.
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tag-preview mb-4">
                    <div class="tag-preview-title">Preview:</div>
                    <div class="tag-badge">
                        <i class="fas fa-tag"></i> <span id="preview-tag-name">{% if form.name.value %}{{ form.name.value }}{% else %}Tag Name{% endif %}</span>
                    </div>
                    <div class="mt-3 small text-muted" id="tag-description-preview">
                        <i class="fas fa-info-circle me-1"></i> This tag will match accounts where <span id="preview-field">bio</span> <span id="preview-operator">contains</span> "<span id="preview-match-text"></span>".
                    </div>
                </div>

                <!-- Hidden input to store conditions JSON -->
                <input type="hidden" id="conditions-json" name="conditions_json" value="[]">

                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-outline-secondary prev-step" data-prev="2">
                        <i class="fas fa-arrow-left me-1"></i> Previous
                    </button>
                    <div>
                        <a href="{% url 'campaigns:dynamic_tag_list' %}" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" id="create-tag-btn">
                            <i class="fas {% if form.instance.pk %}fa-save{% else %}fa-plus-circle{% endif %} me-1"></i>
                            {% if form.instance.pk %}Update{% else %}Create{% endif %} Tag
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'campaigns/js/tag_condition_builder.js' %}"></script>
<script>
// Function to validate UUID format
function isValidUUID(uuid) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
}

// Function to check if tag exists
function checkTagExists() {
    {% if form.instance.pk %}
    const tagId = '{{ form.instance.pk }}';
    console.log('Checking if tag exists with ID:', tagId);

    // Validate UUID format
    if (!isValidUUID(tagId)) {
        console.error('Invalid UUID format:', tagId);

        // Show error message
        document.getElementById('tag-not-found-error').style.display = 'block';
        document.getElementById('tag-form').style.display = 'none';

        // Update error message
        const errorHeading = document.querySelector('#tag-not-found-error .alert-heading');
        const errorMessage = document.querySelector('#tag-not-found-error p');

        if (errorHeading) errorHeading.textContent = 'Invalid Tag ID';
        if (errorMessage) errorMessage.textContent = 'The tag ID format is invalid. Please go back to the tag list and try again.';

        return false;
    }

    // Check if form.instance.id exists (this will be None if the tag doesn't exist)
    {% if not form.instance.id %}
    console.error('Tag does not exist with ID:', tagId);

    // Show error message
    document.getElementById('tag-not-found-error').style.display = 'block';
    document.getElementById('tag-form').style.display = 'none';

    return false;
    {% endif %}
    {% endif %}

    return true;
}

// Check if tag exists when the page loads
document.addEventListener('DOMContentLoaded', function() {
    checkTagExists();
});
</script>
<script>
    // Pass existing conditions to the tag condition builder if available
    {% if existing_conditions %}
    var existingConditions = {{ existing_conditions|safe }};
    console.log('Loading existing conditions from template:', existingConditions);
    {% else %}
    var existingConditions = [];
    {% endif %}

    // Make sure existingConditions is an array
    if (existingConditions && !Array.isArray(existingConditions)) {
        try {
            // Try to parse it as JSON if it's a string
            if (typeof existingConditions === 'string') {
                existingConditions = JSON.parse(existingConditions);
            } else {
                // If it's an object but not an array, wrap it in an array
                existingConditions = [existingConditions];
            }
        } catch (e) {
            console.error('Error parsing existingConditions in template:', e);
            existingConditions = [];
        }
    }

    // Log the number of conditions for debugging
    console.log('Number of conditions from template:', existingConditions.length);
    for (let i = 0; i < existingConditions.length; i++) {
        console.log(`Condition ${i}:`, existingConditions[i]);
    }

    // Make sure jQuery is available
    if (typeof jQuery !== 'undefined') {
        // Use jQuery
        $(document).ready(function() {
            // Handle form submission
            $('#tag-form').on('submit', function(e) {
                // Make sure conditions_json is updated
                if (typeof conditions !== 'undefined') {
                    const conditionsJson = document.getElementById('conditions-json');
                    if (conditionsJson) {
                        conditionsJson.value = JSON.stringify(conditions);
                    }
                }
            });

            // Initialize the tag form
            initializeTagForm();

            // Initialize the condition builder
            if (typeof initConditionBuilder === 'function') {
                initConditionBuilder();
            }

            // Load existing conditions if available
            if (typeof loadExistingConditions === 'function') {
                console.log('Calling loadExistingConditions with:', existingConditions);
                loadExistingConditions(existingConditions);
            } else {
                console.error('loadExistingConditions function not found');
            }

            // Initialize pattern testing
            $('#test-pattern-btn').on('click', function() {
                if (typeof testAllConditions === 'function') {
                    testAllConditions();
                }
            });
        });
    } else {
        // Fallback to vanilla JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            console.warn('jQuery not found, using vanilla JavaScript fallback');

            // Initialize the tag form
            initializeTagForm();

            // Initialize the condition builder
            if (typeof initConditionBuilder === 'function') {
                initConditionBuilder();
            }

            // Load existing conditions if available
            if (typeof loadExistingConditions === 'function') {
                console.log('Calling loadExistingConditions with:', existingConditions);
                loadExistingConditions(existingConditions);
            } else {
                console.error('loadExistingConditions function not found');
            }

            // Initialize pattern testing
            const testPatternBtn = document.getElementById('test-pattern-btn');
            if (testPatternBtn) {
                testPatternBtn.addEventListener('click', function() {
                    if (typeof testAllConditions === 'function') {
                        testAllConditions();
                    }
                });
            }
        });
    }

    function initializeTagForm() {
        // Initialize variables
        let currentStep = 1;
        let selectedField = 'bio';
        let selectedFieldType = 'string';
        let selectedTagType = 'keyword';
        let selectedOperator = 'icontains';
        let patternValue = '';

        // Set focus on the name field when the page loads
        document.getElementById('id_name').focus();

        // Update the preview when the tag name changes
        document.getElementById('id_name').addEventListener('input', function() {
            updatePreview();
        });

        // Handle field category selection
        const fieldCategoryRadios = document.querySelectorAll('input[name="field-category"]');
        fieldCategoryRadios.forEach(function(radio) {
            radio.addEventListener('change', function() {
                // Hide all field category groups
                document.querySelectorAll('.field-category-group').forEach(function(group) {
                    group.style.display = 'none';
                });

                // Show the selected field category group
                const selectedCategory = this.value;
                document.getElementById(`${selectedCategory}-fields`).style.display = 'block';

                // Reset field selection
                document.querySelectorAll('.field-option').forEach(function(option) {
                    option.classList.remove('selected');
                });

                // Select the first field in the category
                const firstField = document.querySelector(`#${selectedCategory}-fields .field-option`);
                if (firstField) {
                    firstField.classList.add('selected');
                    selectedField = firstField.getAttribute('data-field');
                    selectedFieldType = firstField.getAttribute('data-field-type');
                    document.getElementById('id_field').value = selectedField;
                }

                // Update operator groups based on field type
                updateOperatorGroups();
                updatePreview();
            });
        });

        // Handle field selection
        const fieldOptions = document.querySelectorAll('.field-option');
        fieldOptions.forEach(function(option) {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                fieldOptions.forEach(function(opt) {
                    opt.classList.remove('selected');
                });
                // Add selected class to clicked option
                this.classList.add('selected');
                selectedField = this.getAttribute('data-field');
                selectedFieldType = this.getAttribute('data-field-type');
                document.getElementById('id_field').value = selectedField;

                // Update operator groups based on field type
                updateOperatorGroups();
                updatePreview();
            });
        });

        // Function to update operator groups based on field type
        function updateOperatorGroups() {
            // Hide all operator groups
            document.querySelectorAll('.operator-group').forEach(function(group) {
                group.style.display = 'none';
            });

            // Show the appropriate operator group based on field type
            let operatorGroupId;
            switch (selectedFieldType) {
                case 'string':
                    operatorGroupId = 'string-operators';
                    break;
                case 'number':
                    operatorGroupId = 'number-operators';
                    break;
                case 'array':
                    operatorGroupId = 'array-operators';
                    break;
                case 'boolean':
                    operatorGroupId = 'boolean-operators';
                    break;
                default:
                    operatorGroupId = 'string-operators';
            }

            document.getElementById(operatorGroupId).style.display = 'block';

            // Reset operator selection
            document.querySelectorAll('.tag-type-option').forEach(function(option) {
                option.classList.remove('selected');
            });

            // Select the first operator in the group
            const firstOperator = document.querySelector(`#${operatorGroupId} .tag-type-option`);
            if (firstOperator) {
                firstOperator.classList.add('selected');
                selectedTagType = firstOperator.getAttribute('data-tag-type');
                selectedOperator = firstOperator.getAttribute('data-operator');
                document.getElementById('id_tag_type').value = selectedTagType;
            }

            // Update pattern input help text based on field type and operator
            updatePatternInputHelp();
        }

        // Handle tag type/operator selection
        const tagTypeOptions = document.querySelectorAll('.tag-type-option');
        tagTypeOptions.forEach(function(option) {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                tagTypeOptions.forEach(function(opt) {
                    opt.classList.remove('selected');
                });
                // Add selected class to clicked option
                this.classList.add('selected');
                selectedTagType = this.getAttribute('data-tag-type');
                selectedOperator = this.getAttribute('data-operator');
                document.getElementById('id_tag_type').value = selectedTagType;

                // Update pattern input help text
                updatePatternInputHelp();
                updatePreview();
            });
        });

        // Function to update pattern input help text (simplified)
        function updatePatternInputHelp() {
            // This function is now simplified since we removed the pattern input block
            // It's kept for compatibility with existing code
        }

        // Handle next step button clicks
        const nextButtons = document.querySelectorAll('.next-step');
        nextButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const nextStep = parseInt(this.getAttribute('data-next'));

                // Validate current step before proceeding
                if (currentStep === 1) {
                    if (!document.getElementById('id_name').value.trim()) {
                        alert('Please enter a tag name before proceeding.');
                        return;
                    }
                }

                // We've removed the pattern input validation since we removed the pattern block

                // Update pattern based on inputs before going to review step
                if (nextStep === 3) {
                    updatePattern();

                    // Call the updateReview function from tag_condition_builder.js
                    if (typeof updateReview === 'function') {
                        updateReview();
                    }
                }

                // Hide current step and show next step
                document.getElementById(`step-${currentStep}`).classList.remove('active');
                document.getElementById(`step-${nextStep}`).classList.add('active');

                // Update step indicator
                document.querySelector(`.step[data-step="${currentStep}"]`).classList.remove('active');
                document.querySelector(`.step[data-step="${currentStep}"]`).classList.add('completed');
                document.querySelector(`.step[data-step="${nextStep}"]`).classList.add('active');

                currentStep = nextStep;

                // Update review page
                if (currentStep === 3) {
                    updateReviewPage();

                    // Call the updateReview function from tag_condition_builder.js again to ensure it's updated
                    if (typeof updateReview === 'function') {
                        updateReview();
                    }
                }
            });
        });

        // Handle previous step button clicks
        const prevButtons = document.querySelectorAll('.prev-step');
        prevButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const prevStep = parseInt(this.getAttribute('data-prev'));

                // Hide current step and show previous step
                document.getElementById(`step-${currentStep}`).classList.remove('active');
                document.getElementById(`step-${prevStep}`).classList.add('active');

                // Update step indicator
                document.querySelector(`.step[data-step="${currentStep}"]`).classList.remove('active');
                document.querySelector(`.step[data-step="${prevStep}"]`).classList.remove('completed');
                document.querySelector(`.step[data-step="${prevStep}"]`).classList.add('active');

                currentStep = prevStep;
            });
        });

        // Function to update the pattern based on inputs
        function updatePattern() {
            try {
                let pattern = '';

                // For boolean fields, set pattern based on operator
                if (selectedFieldType === 'boolean') {
                    pattern = selectedOperator === 'is_true' ? 'true' : 'false';
                } else {
                    // For other field types, use a default value based on the tag name
                    const nameElement = document.getElementById('id_name');
                    pattern = patternValue || (nameElement ? nameElement.value.trim().toLowerCase() : '');
                }

                // Special handling for between operator
                if (selectedOperator === 'between' && !pattern.includes(',')) {
                    // If no comma in pattern for between operator, add a default range
                    pattern = `${pattern},${parseInt(pattern) || 100 * 2}`;
                }

                // Safely update the pattern field
                const patternField = document.getElementById('id_pattern');
                if (patternField) {
                    patternField.value = pattern;
                }

                // Update preview elements if they exist
                const previewMatchText = document.getElementById('preview-match-text');
                if (previewMatchText) {
                    previewMatchText.textContent = pattern;
                }

                const reviewPattern = document.getElementById('review-pattern');
                if (reviewPattern) {
                    reviewPattern.textContent = pattern;
                }
            } catch (error) {
                console.error("Error in updatePattern:", error);
                // Don't throw the error - just log it so the UI doesn't break
            }
        }

        // Function to update the preview
        function updatePreview() {
            try {
                const tagName = document.getElementById('id_name').value.trim() || 'Tag Name';
                const fieldDisplayNames = {
                    'username': 'username',
                    'full_name': 'full name',
                    'bio': 'bio',
                    'followers': 'followers',
                    'following': 'following',
                    'number_of_posts': 'number of posts',
                    'interests': 'interests',
                    'account_type': 'account type',
                    'phone_number': 'phone number',
                    'avoid': 'avoid flag',
                    'is_verified': 'verified status',
                    'links': 'links',
                    'locations': 'locations'
                };

                // Safely update elements - check if they exist first
                const previewTagName = document.getElementById('preview-tag-name');
                if (previewTagName) {
                    previewTagName.textContent = tagName;
                }

                const reviewName = document.getElementById('review-name');
                if (reviewName) {
                    reviewName.textContent = tagName;
                }

                // Update description in review
                const description = document.getElementById('id_description').value.trim() || '-';
                const reviewDescription = document.getElementById('review-description');
                if (reviewDescription) {
                    reviewDescription.textContent = description;
                }

                // Update field in preview and review
                const previewField = document.getElementById('preview-field');
                if (previewField) {
                    previewField.textContent = fieldDisplayNames[selectedField] || selectedField;
                }

                const reviewField = document.getElementById('review-field');
                if (reviewField) {
                    reviewField.textContent = (fieldDisplayNames[selectedField] || selectedField).charAt(0).toUpperCase() + (fieldDisplayNames[selectedField] || selectedField).slice(1);
                }

                // Update tag type in review
                const operatorDisplayNames = {
                    // String operators
                    'icontains': 'Contains (Case Insensitive)',
                    'iexact': 'Exact Match (Case Insensitive)',
                    'istartswith': 'Starts With',
                    'iendswith': 'Ends With',
                    'not_icontains': 'Does Not Contain',
                    'iregex': 'Regular Expression',
                    'in': 'Keyword List',

                    // Number operators
                    'eq': 'Equal To',
                    'gt': 'Greater Than',
                    'gte': 'Greater Than or Equal',
                    'lt': 'Less Than',
                    'lte': 'Less Than or Equal',
                    'between': 'Between',

                    // Array operators
                    'contains': 'Contains',
                    'not_contains': 'Does Not Contain',
                    'length_gt': 'Length Greater Than',
                    'length_lt': 'Length Less Than',

                    // Boolean operators
                    'is_true': 'Is True',
                    'is_false': 'Is False'
                };

                const reviewTagType = document.getElementById('review-tag-type');
                if (reviewTagType) {
                    reviewTagType.textContent = operatorDisplayNames[selectedOperator] || selectedTagType;
                }

                // Global status functionality removed as per requirements

                // Update pattern preview if elements exist
                if (patternValue) {
                    const previewMatchText = document.getElementById('preview-match-text');
                    if (previewMatchText) {
                        previewMatchText.textContent = patternValue;
                    }

                    const reviewPattern = document.getElementById('review-pattern');
                    if (reviewPattern) {
                        reviewPattern.textContent = patternValue;
                    }
                } else {
                    updatePattern();
                }
            } catch (error) {
                console.error("Error in updatePreview:", error);
                // Don't throw the error - just log it so the UI doesn't break
            }
        }

        // Function to update the review page
        function updateReviewPage() {
            updatePreview();
        }

        // Handle form submission
        document.getElementById('tag-form').addEventListener('submit', function(e) {
            console.log('Dynamic tag form submit handler');

            // Make sure hidden fields are properly set
            document.getElementById('id_field').value = selectedField;
            document.getElementById('id_tag_type').value = selectedOperator; // Use operator as tag_type
            updatePattern();

            // Ensure conditions are properly saved
            if (typeof conditions !== 'undefined') {
                console.log('Conditions before submission:', conditions);

                // Update the conditions JSON hidden input
                const conditionsJson = document.getElementById('conditions-json');
                if (conditionsJson) {
                    conditionsJson.value = JSON.stringify(conditions);
                    console.log('Form submission - conditions_json value:', conditionsJson.value);
                } else {
                    console.error('conditions-json element not found, creating it');
                    // Create the conditions_json input if it doesn't exist
                    const conditionsJsonField = document.createElement('input');
                    conditionsJsonField.type = 'hidden';
                    conditionsJsonField.id = 'conditions-json';
                    conditionsJsonField.name = 'conditions_json';
                    conditionsJsonField.value = JSON.stringify(conditions);
                    this.appendChild(conditionsJsonField);
                }

                // Validate conditions before submitting
                if (conditions.length > 0 && !validateConditions()) {
                    console.log('Conditions validation failed');
                    e.preventDefault();
                    return false;
                }
            } else if (currentStep === 3) {
                // If we're on the review step but conditions is undefined, initialize it
                console.log('Conditions is undefined on step 3, initializing empty array');
                const conditionsJson = document.getElementById('conditions-json');
                if (conditionsJson) {
                    conditionsJson.value = '[]';
                } else {
                    console.error('conditions-json element not found, creating it');
                    // Create the conditions_json input if it doesn't exist
                    const conditionsJsonField = document.createElement('input');
                    conditionsJsonField.type = 'hidden';
                    conditionsJsonField.id = 'conditions-json';
                    conditionsJsonField.name = 'conditions_json';
                    conditionsJsonField.value = '[]';
                    this.appendChild(conditionsJsonField);
                }
            }

            // Double-check that the conditions_json field exists and has a value
            const conditionsJson = document.getElementById('conditions-json');
            if (!conditionsJson) {
                console.error('conditions-json element still not found after checks, creating it');
                const conditionsJsonField = document.createElement('input');
                conditionsJsonField.type = 'hidden';
                conditionsJsonField.id = 'conditions-json';
                conditionsJsonField.name = 'conditions_json';
                conditionsJsonField.value = '[]';
                this.appendChild(conditionsJsonField);
            } else if (!conditionsJson.value) {
                console.warn('conditions-json element has no value, setting to empty array');
                conditionsJson.value = '[]';
            }

            // Log form data before submission
            console.log('Form is valid, submitting with data:');
            console.log('- Field:', document.getElementById('id_field').value);
            console.log('- Tag Type:', document.getElementById('id_tag_type').value);
            console.log('- Pattern:', document.getElementById('id_pattern').value);
            console.log('- Conditions JSON:', document.getElementById('conditions-json').value);

            // Add a hidden field with the current timestamp to ensure the form data is not cached
            const timestampField = document.createElement('input');
            timestampField.type = 'hidden';
            timestampField.name = 'submission_timestamp';
            timestampField.value = Date.now();
            this.appendChild(timestampField);

            return true;
        });

        // Initialize operator groups based on default field type
        updateOperatorGroups();

        // Initialize preview
        updatePreview();
    }
</script>
{% endblock extra_js %}
