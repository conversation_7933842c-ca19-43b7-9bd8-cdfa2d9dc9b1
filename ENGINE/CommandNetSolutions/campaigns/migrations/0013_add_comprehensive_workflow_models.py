# Generated by Django 4.2.16 on 2025-06-01 10:36

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("campaigns", "0012_workflowtemplate"),
    ]

    operations = [
        migrations.CreateModel(
            name="Workflow",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(help_text="Name of the workflow", max_length=255),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Description of the workflow"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("pending", "Pending"),
                            ("running", "Running"),
                            ("paused", "Paused"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("stopped", "Stopped"),
                        ],
                        default="draft",
                        help_text="Current status of the workflow",
                        max_length=20,
                    ),
                ),
                (
                    "enabled_actions",
                    models.J<PERSON><PERSON><PERSON>(
                        default=list, help_text="List of enabled action types"
                    ),
                ),
                (
                    "action_parameters",
                    models.<PERSON><PERSON><PERSON><PERSON>(
                        default=dict, help_text="Parameters for each action type"
                    ),
                ),
                (
                    "daily_limit",
                    models.IntegerField(
                        default=50, help_text="Maximum actions per day"
                    ),
                ),
                (
                    "hourly_limit",
                    models.IntegerField(
                        default=10, help_text="Maximum actions per hour"
                    ),
                ),
                (
                    "delay_between_actions",
                    models.IntegerField(
                        default=30, help_text="Delay between actions in seconds"
                    ),
                ),
                (
                    "schedule_type",
                    models.CharField(
                        choices=[
                            ("immediate", "Start Immediately"),
                            ("time_based", "Time-based Schedule"),
                            ("date_based", "Date-based Schedule"),
                        ],
                        default="immediate",
                        help_text="How the workflow should be scheduled",
                        max_length=20,
                    ),
                ),
                (
                    "scheduled_start",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the workflow should start (for scheduled workflows)",
                        null=True,
                    ),
                ),
                (
                    "airflow_dag_id",
                    models.CharField(
                        blank=True,
                        help_text="Generated Airflow DAG ID",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "airflow_run_id",
                    models.CharField(
                        blank=True,
                        help_text="Current Airflow run ID",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "total_accounts",
                    models.IntegerField(
                        default=0, help_text="Total number of accounts to process"
                    ),
                ),
                (
                    "processed_accounts",
                    models.IntegerField(
                        default=0, help_text="Number of accounts processed"
                    ),
                ),
                (
                    "successful_actions",
                    models.IntegerField(
                        default=0, help_text="Number of successful actions"
                    ),
                ),
                (
                    "failed_actions",
                    models.IntegerField(
                        default=0, help_text="Number of failed actions"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "source_campaigns",
                    models.ManyToManyField(
                        help_text="Campaigns whose whitelists will be processed",
                        related_name="workflows",
                        to="campaigns.campaign",
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        blank=True,
                        help_text="Template used to create this workflow",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="campaigns.workflowtemplate",
                    ),
                ),
            ],
            options={
                "verbose_name": "Workflow",
                "verbose_name_plural": "Workflows",
                "db_table": "campaigns_workflow",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ResourceManager",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "max_daily_actions",
                    models.IntegerField(
                        default=500,
                        help_text="Maximum total actions per day across all workflows",
                    ),
                ),
                (
                    "max_hourly_actions",
                    models.IntegerField(
                        default=50,
                        help_text="Maximum total actions per hour across all workflows",
                    ),
                ),
                (
                    "max_concurrent_actions",
                    models.IntegerField(
                        default=5, help_text="Maximum concurrent actions"
                    ),
                ),
                (
                    "priority_mode",
                    models.CharField(
                        choices=[
                            ("cep", "CEP Workflows Priority"),
                            ("dmp", "DMP Priority"),
                            ("balanced", "Balanced Priority"),
                        ],
                        default="cep",
                        help_text="Resource allocation priority mode",
                        max_length=20,
                    ),
                ),
                (
                    "daily_actions_used",
                    models.IntegerField(default=0, help_text="Actions used today"),
                ),
                (
                    "hourly_actions_used",
                    models.IntegerField(default=0, help_text="Actions used this hour"),
                ),
                (
                    "current_concurrent_actions",
                    models.IntegerField(
                        default=0, help_text="Currently running actions"
                    ),
                ),
                (
                    "action_queue_size",
                    models.IntegerField(
                        default=0, help_text="Number of actions in queue"
                    ),
                ),
                (
                    "max_queue_size",
                    models.IntegerField(default=1000, help_text="Maximum queue size"),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="Whether resource manager is active"
                    ),
                ),
                (
                    "last_reset_date",
                    models.DateField(
                        auto_now_add=True,
                        help_text="Last date when daily counters were reset",
                    ),
                ),
                (
                    "last_hourly_reset",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="Last time hourly counters were reset",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "active_workflow",
                    models.OneToOneField(
                        blank=True,
                        help_text="Currently active workflow (one-active-workflow restriction)",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resource_manager",
                        to="campaigns.workflow",
                    ),
                ),
            ],
            options={
                "verbose_name": "Resource Manager",
                "verbose_name_plural": "Resource Managers",
                "db_table": "campaigns_resource_manager",
            },
        ),
        migrations.CreateModel(
            name="WorkflowAction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("follow", "Follow"),
                            ("like", "Like Posts"),
                            ("comment", "Comment on Posts"),
                            ("dm", "Direct Message"),
                            ("story_view", "View Stories"),
                            ("story_reaction", "React to Stories"),
                            ("story_reply", "Reply to Stories"),
                            ("discover", "Discover Accounts"),
                        ],
                        help_text="Type of action to perform",
                        max_length=20,
                    ),
                ),
                (
                    "target_username",
                    models.CharField(
                        help_text="Instagram username to target", max_length=255
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("running", "Running"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("skipped", "Skipped"),
                        ],
                        default="pending",
                        help_text="Current status of the action",
                        max_length=20,
                    ),
                ),
                (
                    "parameters",
                    models.JSONField(
                        default=dict, help_text="Parameters for this specific action"
                    ),
                ),
                (
                    "scheduled_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When this action is scheduled to run",
                        null=True,
                    ),
                ),
                (
                    "started_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When this action started executing",
                        null=True,
                    ),
                ),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, help_text="When this action completed", null=True
                    ),
                ),
                (
                    "result_data",
                    models.JSONField(
                        default=dict,
                        help_text="Results and metadata from action execution",
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, help_text="Error message if action failed"
                    ),
                ),
                (
                    "retry_count",
                    models.IntegerField(
                        default=0,
                        help_text="Number of times this action has been retried",
                    ),
                ),
                (
                    "max_retries",
                    models.IntegerField(
                        default=3, help_text="Maximum number of retries allowed"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "workflow",
                    models.ForeignKey(
                        help_text="Workflow this action belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="actions",
                        to="campaigns.workflow",
                    ),
                ),
            ],
            options={
                "verbose_name": "Workflow Action",
                "verbose_name_plural": "Workflow Actions",
                "db_table": "campaigns_workflow_action",
                "ordering": ["scheduled_at", "created_at"],
                "indexes": [
                    models.Index(
                        fields=["workflow", "status"],
                        name="campaigns_w_workflo_286717_idx",
                    ),
                    models.Index(
                        fields=["action_type", "status"],
                        name="campaigns_w_action__ea1500_idx",
                    ),
                    models.Index(
                        fields=["scheduled_at"], name="campaigns_w_schedul_7ab784_idx"
                    ),
                ],
            },
        ),
    ]
