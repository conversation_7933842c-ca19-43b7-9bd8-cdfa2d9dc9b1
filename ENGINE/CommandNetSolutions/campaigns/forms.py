from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.db import models
import json
import uuid

from .models import Campaign, LocationTarget, UsernameTarget, DynamicTag, CampaignTagRule, TagGroup, CampaignTag


class CampaignForm(forms.ModelForm):
    """
    Form for creating and editing campaigns.
    """
    # Add fields for direct target selection
    location_targets = forms.MultipleChoiceField(
        required=False,
        label="Location Targets",
        help_text="Select one or more locations to target",
        widget=forms.SelectMultiple(attrs={'class': 'location-targets-select', 'style': 'display:none;'})
    )

    location_search = forms.CharField(
        required=False,
        label="Search Locations",
        widget=forms.TextInput(attrs={
            'class': 'form-control location-search-field',
            'placeholder': 'Search for locations...',
            'autocomplete': 'off'
        })
    )

    usernames = forms.CharField(
        required=False,
        label="Usernames",
        help_text="Enter one username per line",
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control username-target-field'})
    )

    # Add toggle for target types
    enable_location_targeting = forms.BooleanField(
        required=False,
        initial=True,
        label="Enable Location Targeting",
        widget=forms.CheckboxInput(attrs={'class': 'target-toggle'})
    )

    enable_username_targeting = forms.BooleanField(
        required=False,
        initial=True,
        label="Enable Username Targeting",
        widget=forms.CheckboxInput(attrs={'class': 'target-toggle'})
    )

    class Meta:
        model = Campaign
        fields = ['name', 'description', 'target_type', 'audience_type']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make sure UUID field is not required for new campaigns
        if 'id' in self.fields:
            self.fields['id'].required = False

        # Populate location choices from the locations.csv file
        self.fields['location_targets'].choices = self._get_location_choices()

        # Hide the target_type field as we'll determine it from the toggles
        self.fields['target_type'].widget = forms.HiddenInput()

        # Set initial values for existing campaign
        if self.instance and self.instance.pk:
            # Set initial location targets
            location_ids = list(self.instance.location_targets.values_list('location_id', flat=True))
            self.fields['location_targets'].initial = location_ids

            # Set initial usernames as JSON
            username_targets = []
            for target in self.instance.username_targets.all():
                username_targets.append({
                    'username': target.username,
                    'audienceType': target.audience_type
                })
            self.fields['usernames'].initial = json.dumps(username_targets)

            # Set targeting toggles based on target type
            if self.instance.target_type == 'location':
                self.fields['enable_location_targeting'].initial = True
                self.fields['enable_username_targeting'].initial = False
            elif self.instance.target_type == 'username':
                self.fields['enable_location_targeting'].initial = False
                self.fields['enable_username_targeting'].initial = True
            else:  # mixed
                self.fields['enable_location_targeting'].initial = True
                self.fields['enable_username_targeting'].initial = True

    def _get_location_choices(self):
        """Get choices list for the location field.
        Include some default choices and load others asynchronously."""
        # Start with an empty list
        choices = []

        # Get existing location targets for this campaign if editing
        existing_location_ids = []
        if self.instance and self.instance.pk:
            existing_location_ids = list(self.instance.location_targets.values_list('location_id', flat=True))

            # Add existing location targets first
            for target in self.instance.location_targets.all():
                location_id = target.location_id
                label = f"{target.city}, {target.country} ({location_id})"
                choices.append((location_id, label))

            # Return only the existing location targets for this campaign
            return choices

        # For new campaigns, we'll just load locations from the CSV file

        # We'll only load a limited number of additional locations from the CSV file
        # to avoid performance issues and UI clutter
        try:
            import os
            import csv
            from django.conf import settings

            locations_file = os.path.join(settings.BASE_DIR, 'campaigns', 'data', 'locations.csv')
            if os.path.exists(locations_file):
                with open(locations_file, 'r', encoding='utf-8') as csvfile:
                    reader = csv.DictReader(csvfile)
                    count = 0
                    max_additional = 10  # Limit to 10 additional locations

                    for row in reader:
                        location_id = row.get('location_id')
                        if (location_id and
                            not any(choice[0] == location_id for choice in choices)):

                            label = f"{row.get('city', '')}, {row.get('country', '')} ({location_id})"
                            choices.append((location_id, label))
                            count += 1

                            if count >= max_additional:
                                break
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error loading locations from CSV: {str(e)}")

        return choices

    def clean(self):
        cleaned_data = super().clean()
        location_targets = cleaned_data.get('location_targets', [])
        usernames_json = cleaned_data.get('usernames', '')
        enable_location_targeting = cleaned_data.get('enable_location_targeting', False)
        enable_username_targeting = cleaned_data.get('enable_username_targeting', False)

        # Log the location targets for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Location targets in clean method (before filtering): {location_targets}")
        logger.info(f"Raw POST data for location_targets: {self.data.getlist('location_targets')}")

        # Always remove any validation errors for location_targets
        # This allows us to add new locations that aren't in the initial choices
        if 'location_targets' in self._errors:
            del self._errors['location_targets']

        # Make sure location_targets is in cleaned_data
        if 'location_targets' not in cleaned_data:
            # Try to get from raw POST data if not in cleaned_data
            raw_location_targets = self.data.getlist('location_targets')
            if raw_location_targets:
                logger.info(f"Using raw POST data for location_targets: {raw_location_targets}")
                cleaned_data['location_targets'] = raw_location_targets
            else:
                cleaned_data['location_targets'] = location_targets

        # Log the final location targets
        logger.info(f"Location targets in clean method (final): {cleaned_data['location_targets']}")

        # Check if we have any targets
        has_location_targets = bool(cleaned_data['location_targets'])
        has_username_targets = bool(usernames_json and usernames_json.strip())

        # Determine target type based on what targets are provided
        if has_location_targets and has_username_targets:
            cleaned_data['target_type'] = 'mixed'
        elif has_location_targets:
            cleaned_data['target_type'] = 'location'
        elif has_username_targets:
            cleaned_data['target_type'] = 'username'
        elif self.instance and self.instance.pk:
            # If editing an existing campaign and no targets are provided,
            # preserve the original target_type
            cleaned_data['target_type'] = self.instance.target_type
        else:
            # Default to location for new campaigns with no targets
            cleaned_data['target_type'] = 'location'

            # No targets provided - only validate if we're submitting the form
            # Don't raise an error if we're just saving a draft
            if self.instance and self.instance.pk and self.instance.status != 'draft':
                # For non-draft campaigns, we need at least one target
                raise ValidationError(_("You must add at least one location or username target."))

        # Always set both targeting flags to true for backward compatibility
        cleaned_data['enable_location_targeting'] = True
        cleaned_data['enable_username_targeting'] = True

        # Process username targets if provided
        if has_username_targets:
            # Parse usernames from JSON
            try:
                # Try to parse as JSON
                username_targets = json.loads(usernames_json)
                if not username_targets:
                    self.add_error('usernames', _("Please enter at least one valid username."))

                # Validate each username target
                for target in username_targets:
                    if not isinstance(target, dict):
                        self.add_error('usernames', _("Invalid username target format."))
                        break

                    if 'username' not in target or not target['username'].strip():
                        self.add_error('usernames', _("Username cannot be empty."))
                        break

                    if 'audienceType' not in target:
                        target['audienceType'] = 'profile'  # Default

                    # Validate audience type
                    valid_audience_types = [choice[0] for choice in Campaign.AUDIENCE_TYPE_CHOICES]
                    if target['audienceType'] not in valid_audience_types:
                        target['audienceType'] = 'profile'  # Default to profile if invalid

                cleaned_data['username_targets'] = username_targets

            except json.JSONDecodeError:
                # If not JSON, try to parse as newline-separated (for backward compatibility)
                try:
                    username_list = [u.strip() for u in usernames_json.split('\n') if u.strip()]
                    if not username_list:
                        self.add_error('usernames', _("Please enter at least one valid username."))

                    # Convert to the new format
                    username_targets = [{'username': u, 'audienceType': 'profile'} for u in username_list]
                    cleaned_data['username_targets'] = username_targets
                except Exception as e:
                    self.add_error('usernames', _("Invalid username format: {}").format(str(e)))

        return cleaned_data


class LocationTargetForm(forms.ModelForm):
    """
    Form for adding location targets to a campaign.
    """
    class Meta:
        model = LocationTarget
        fields = ['country', 'city', 'location_id']


class UsernameTargetForm(forms.ModelForm):
    """
    Form for adding username targets to a campaign.
    """
    class Meta:
        model = UsernameTarget
        fields = ['username']


class BulkUsernameForm(forms.Form):
    """
    Form for adding multiple usernames at once.
    """
    usernames = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 5}),
        help_text=_("Enter one username per line.")
    )

    def clean_usernames(self):
        data = self.cleaned_data['usernames']
        usernames = [u.strip() for u in data.split('\n') if u.strip()]

        if not usernames:
            raise ValidationError(_("Please enter at least one username."))

        return usernames


class LocationSearchForm(forms.Form):
    """
    Form for searching locations.
    """
    search_term = forms.CharField(
        max_length=100,
        required=True,
        widget=forms.TextInput(attrs={'placeholder': 'Search for a country or city'})
    )


class DynamicTagForm(forms.ModelForm):
    """
    Form for creating and editing dynamic tags with multiple conditions.
    """
    # Add hidden fields for the wizard interface
    field = forms.CharField(required=False, widget=forms.HiddenInput())
    pattern = forms.CharField(required=False, widget=forms.HiddenInput())
    tag_type = forms.CharField(required=False, widget=forms.HiddenInput())

    # Add a field for conditions JSON
    conditions_json = forms.CharField(required=False, widget=forms.HiddenInput())

    # Add a field for condition logic (all/any)
    condition_logic = forms.CharField(required=False, widget=forms.HiddenInput(), initial='all')

    class Meta:
        model = DynamicTag
        fields = ['name', 'description', 'category', 'tag_groups', 'field', 'pattern']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
            'tag_groups': forms.CheckboxSelectMultiple(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make tag_group a select field with a "Create New" option
        from .models import TagGroup
        self.fields['tag_group'].queryset = TagGroup.objects.all().order_by('name')
        self.fields['tag_group'].required = False
        self.fields['tag_group'].empty_label = "No Tag Group"

        # Make category a select field
        from .models import TagCategory
        self.fields['category'].queryset = TagCategory.objects.all().order_by('name')
        self.fields['category'].required = False
        self.fields['category'].empty_label = "No Category"

        # Confidence level is deprecated
        # self.fields['confidence_level'].initial = 'medium'

        # Set initial values for existing tags
        if self.instance and self.instance.pk:
            self.fields['field'].initial = self.instance.field
            self.fields['pattern'].initial = self.instance.pattern

            # For existing tags, load conditions from the associated rule or create a default condition
            if hasattr(self.instance, 'field') and hasattr(self.instance, 'pattern') and self.instance.pk:
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"Loading conditions for existing tag: {self.instance.name}, pattern: {self.instance.pattern}")

                # Try to parse the pattern as JSON
                try:
                    # Make sure the pattern is a valid JSON string
                    if not self.instance.pattern or self.instance.pattern.strip() == '':
                        pattern_data = {}
                    else:
                        pattern_data = json.loads(self.instance.pattern)

                    # Check if the pattern contains a rule_id reference
                    if isinstance(pattern_data, dict) and 'rule_id' in pattern_data:
                        # Load conditions from the associated rule
                        from .models import CampaignTagRule
                        rule_id = pattern_data.get('rule_id')
                        logic = pattern_data.get('logic', 'all')

                        logger.info(f"Found rule_id in pattern: {rule_id}, logic: {logic}")

                        try:
                            # Get the rule and its conditions
                            rule = CampaignTagRule.objects.get(id=rule_id)
                            conditions = []

                            # Use CampaignTagCondition to avoid circular imports
                            from .models import CampaignTagCondition

                            # Get conditions using the correct model
                            rule_conditions = CampaignTagCondition.objects.filter(rule=rule)

                            # Convert rule conditions to the format expected by the form
                            for i, condition in enumerate(rule_conditions):
                                condition_data = {
                                    'id': i,
                                    'field_category': self._get_field_category(condition.field),
                                    'field': condition.field,
                                    'field_type': condition.field_type,
                                    'operator': condition.operator,
                                    'value': condition.value,
                                    'required': condition.required
                                }
                                conditions.append(condition_data)

                            logger.info(f"Loaded {len(conditions)} conditions from rule {rule_id}")

                            # Set the conditions JSON and logic
                            if conditions:
                                self.fields['conditions_json'].initial = json.dumps(conditions)
                                self.fields['condition_logic'].initial = logic
                            else:
                                # If no conditions found, create a default one
                                default_condition = {
                                    'id': 0,
                                    'field_category': self._get_field_category(self.instance.field),
                                    'field': self.instance.field,
                                    'field_type': self._get_field_type(self.instance.field),
                                    'operator': getattr(self.instance, 'tag_type', 'icontains'),
                                    'value': self.instance.pattern,
                                    'required': True
                                }
                                self.fields['conditions_json'].initial = json.dumps([default_condition])
                                self.fields['condition_logic'].initial = 'all'
                        except CampaignTagRule.DoesNotExist:
                            logger.warning(f"Rule with ID {rule_id} not found, creating default condition")
                            # Rule not found, create a default condition
                            default_condition = {
                                'id': 0,
                                'field_category': self._get_field_category(self.instance.field),
                                'field': self.instance.field,
                                'field_type': self._get_field_type(self.instance.field),
                                'operator': getattr(self.instance, 'tag_type', 'icontains'),
                                'value': self.instance.pattern,
                                'required': True
                            }
                            self.fields['conditions_json'].initial = json.dumps([default_condition])
                            self.fields['condition_logic'].initial = 'all'
                    elif isinstance(pattern_data, list):
                        # Pattern is already a JSON array of conditions
                        logger.info(f"Pattern is a list of conditions: {pattern_data}")
                        self.fields['conditions_json'].initial = self.instance.pattern
                        self.fields['condition_logic'].initial = 'all'
                    else:
                        # Pattern is a JSON object but not an array or rule reference
                        logger.info(f"Pattern is a JSON object but not a list or rule reference: {pattern_data}")
                        default_condition = {
                            'id': 0,
                            'field_category': self._get_field_category(self.instance.field),
                            'field': self.instance.field,
                            'field_type': self._get_field_type(self.instance.field),
                            'operator': getattr(self.instance, 'tag_type', 'icontains'),
                            'value': self.instance.pattern,
                            'required': True
                        }
                        self.fields['conditions_json'].initial = json.dumps([default_condition])
                        self.fields['condition_logic'].initial = 'all'
                except (json.JSONDecodeError, TypeError) as e:
                    # Pattern is not a JSON string, create a single condition
                    logger.warning(f"Error parsing pattern as JSON: {str(e)}, creating default condition")
                    default_condition = {
                        'id': 0,
                        'field_category': self._get_field_category(self.instance.field),
                        'field': self.instance.field,
                        'field_type': self._get_field_type(self.instance.field),
                        'operator': getattr(self.instance, 'tag_type', 'icontains'),
                        'value': self.instance.pattern,
                        'required': True
                    }
                    self.fields['conditions_json'].initial = json.dumps([default_condition])
                    self.fields['condition_logic'].initial = 'all'

    def _get_field_category(self, field):
        """Get the category for a field."""
        field_categories = {
            'username': 'text',
            'full_name': 'text',
            'bio': 'text',
            'account_type': 'text',
            'phone_number': 'text',
            'followers': 'numeric',
            'following': 'numeric',
            'number_of_posts': 'numeric',
            'interests': 'lists',
            'locations': 'lists',
            'links': 'lists',
            'is_verified': 'boolean',
            'avoid': 'boolean'
        }
        return field_categories.get(field, 'text')

    def _get_field_type(self, field):
        """Get the type for a field."""
        field_types = {
            'username': 'string',
            'full_name': 'string',
            'bio': 'string',
            'account_type': 'string',
            'phone_number': 'string',
            'followers': 'number',
            'following': 'number',
            'number_of_posts': 'number',
            'interests': 'array',
            'locations': 'array',
            'links': 'array',
            'is_verified': 'boolean',
            'avoid': 'boolean'
        }
        return field_types.get(field, 'string')

    def clean(self):
        """
        Validate and process form data with multiple conditions.
        """
        cleaned_data = super().clean()

        # Get the conditions JSON
        conditions_json = cleaned_data.get('conditions_json', '[]')
        condition_logic = cleaned_data.get('condition_logic', 'all')

        # Log the conditions JSON for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"DynamicTagForm clean - conditions_json: {conditions_json}")

        try:
            conditions = json.loads(conditions_json)
            logger.info(f"DynamicTagForm clean - parsed conditions: {conditions}")
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing conditions JSON: {str(e)}")
            conditions = []
            # Don't raise a validation error here, as we'll allow empty conditions
            # and handle it in the form submission validation

        # If we have conditions, use them to set the field and pattern
        if conditions:
            # For backward compatibility, we'll use the first condition's field and pattern
            # for the main tag fields
            first_condition = conditions[0]

            # Set the field and pattern from the first condition
            cleaned_data['field'] = first_condition.get('field', 'bio')
            cleaned_data['pattern'] = first_condition.get('value', '')
            cleaned_data['tag_type'] = first_condition.get('operator', 'icontains')

            logger.info(f"Using first condition for field: {cleaned_data['field']}, pattern: {cleaned_data['pattern']}, tag_type: {cleaned_data['tag_type']}")
        else:
            # If no conditions, use default values
            cleaned_data['field'] = cleaned_data.get('field', 'bio')
            cleaned_data['pattern'] = cleaned_data.get('pattern', '')
            cleaned_data['tag_type'] = cleaned_data.get('tag_type', 'icontains')

            logger.info(f"No conditions found, using defaults - field: {cleaned_data['field']}, pattern: {cleaned_data['pattern']}, tag_type: {cleaned_data['tag_type']}")

        # Store the conditions and logic in the tag's metadata
        # We'll create or update the tag rule and conditions when saving
        cleaned_data['conditions'] = conditions
        cleaned_data['condition_logic'] = condition_logic

        return cleaned_data

    def save(self, commit=True):
        """
        Save the tag and create/update associated tag rule and conditions.
        """
        import logging
        logger = logging.getLogger(__name__)

        # Get the conditions and logic from cleaned_data
        conditions = self.cleaned_data.get('conditions', [])
        condition_logic = self.cleaned_data.get('condition_logic', 'all')

        logger.info(f"DynamicTagForm save - conditions: {conditions}")
        logger.info(f"DynamicTagForm save - condition_logic: {condition_logic}")
        logger.info(f"DynamicTagForm save - cleaned_data: {self.cleaned_data}")

        # Ensure we have a valid pattern value
        if not self.cleaned_data.get('pattern'):
            self.cleaned_data['pattern'] = ''
            logger.warning("Pattern was empty, setting to empty string")

        # Ensure we have a valid field value
        if not self.cleaned_data.get('field'):
            self.cleaned_data['field'] = 'bio'
            logger.warning("Field was empty, setting to 'bio'")

        # Ensure we have a valid tag_type value
        if not self.cleaned_data.get('tag_type'):
            self.cleaned_data['tag_type'] = 'keyword'
            logger.warning("Tag type was empty, setting to 'keyword'")

        try:
            # Save the tag first
            tag = super().save(commit=commit)
            logger.info(f"DynamicTagForm save - tag saved: {tag.id}, name: {tag.name}")

            if commit:
                self.save_conditions(tag)

            return tag
        except Exception as e:
            logger.exception(f"Error saving tag: {str(e)}")
            # Re-raise the exception to show the error to the user
            raise

    def save_conditions(self, tag):
        """
        Save the conditions for a tag.
        This method can be called separately after saving the tag.
        """
        from .models import CampaignTagRule, CampaignTagCondition
        import logging
        import traceback
        logger = logging.getLogger(__name__)

        logger.info(f"=== START save_conditions for tag: {tag.id} ===")

        # Get the conditions and logic from cleaned_data
        conditions = self.cleaned_data.get('conditions', [])
        condition_logic = self.cleaned_data.get('condition_logic', 'all')

        logger.info(f"Initial conditions from cleaned_data: {conditions}")
        logger.info(f"Initial condition_logic from cleaned_data: {condition_logic}")

        # Get conditions from conditions_json if not already parsed
        if not conditions and 'conditions_json' in self.cleaned_data:
            try:
                conditions_json = self.cleaned_data.get('conditions_json', '[]')
                logger.info(f"Parsing conditions from conditions_json in cleaned_data: {conditions_json}")

                if conditions_json and isinstance(conditions_json, str):
                    conditions = json.loads(conditions_json)
                    logger.info(f"Successfully parsed conditions from cleaned_data: {conditions}")
                    self.cleaned_data['conditions'] = conditions
                else:
                    logger.warning(f"conditions_json in cleaned_data is not a valid string: {type(conditions_json)}")
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing conditions_json from cleaned_data: {str(e)}")
                logger.error(f"Invalid JSON: {conditions_json}")
                conditions = []
            except Exception as e:
                logger.error(f"Unexpected error processing conditions_json from cleaned_data: {str(e)}")
                logger.error(traceback.format_exc())
                conditions = []

        # If we still don't have conditions, check if there's a data attribute with conditions_json
        if not conditions and hasattr(self, 'data'):
            try:
                # Try to get from POST data
                conditions_json = self.data.get('conditions_json', '[]')
                logger.info(f"Getting conditions_json from form data: {conditions_json}")

                # Make sure we have a valid JSON string
                if conditions_json and isinstance(conditions_json, str):
                    conditions = json.loads(conditions_json)
                    logger.info(f"Successfully parsed conditions from form data: {conditions}")
                    self.cleaned_data['conditions'] = conditions
                else:
                    logger.warning(f"conditions_json from form data is not a valid string: {type(conditions_json)}")
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing conditions_json from form data: {str(e)}")
                logger.error(f"Invalid JSON: {conditions_json}")
            except Exception as e:
                logger.error(f"Unexpected error processing conditions_json from form data: {str(e)}")
                logger.error(traceback.format_exc())

        # If we still don't have conditions, check if there's a request object with POST data
        if not conditions and hasattr(self, 'instance') and hasattr(self.instance, 'request'):
            try:
                conditions_json = self.instance.request.POST.get('conditions_json', '[]')
                logger.info(f"Getting conditions_json from request POST data: {conditions_json}")

                if conditions_json and isinstance(conditions_json, str):
                    conditions = json.loads(conditions_json)
                    logger.info(f"Successfully parsed conditions from request POST data: {conditions}")
                    self.cleaned_data['conditions'] = conditions
                else:
                    logger.warning(f"conditions_json from request POST data is not a valid string: {type(conditions_json)}")
            except (json.JSONDecodeError, AttributeError) as e:
                logger.error(f"Error getting conditions from request: {str(e)}")
                logger.error(traceback.format_exc())

        # Final check: if we have a request object in the form instance, try to get conditions directly
        if not conditions and hasattr(self, '_request'):
            try:
                conditions_json = self._request.POST.get('conditions_json', '[]')
                logger.info(f"Getting conditions_json from _request POST data: {conditions_json}")

                if conditions_json and isinstance(conditions_json, str):
                    conditions = json.loads(conditions_json)
                    logger.info(f"Successfully parsed conditions from _request POST data: {conditions}")
                    self.cleaned_data['conditions'] = conditions
                else:
                    logger.warning(f"conditions_json from _request POST data is not a valid string: {type(conditions_json)}")
            except (json.JSONDecodeError, AttributeError) as e:
                logger.error(f"Error getting conditions from _request: {str(e)}")
                logger.error(traceback.format_exc())

        logger.info(f"Final conditions for tag {tag.id}: {conditions}")
        logger.info(f"Final condition_logic for tag {tag.id}: {condition_logic}")

        # If conditions is still not a list, initialize it as an empty list
        if not isinstance(conditions, list):
            logger.warning(f"Conditions is not a list: {type(conditions)}. Initializing as empty list.")
            conditions = []

        logger.info(f"=== END save_conditions for tag: {tag.id} ===")

        try:
            # Create or update the tag rule
            rule_name = f"Rule for {tag.name}"
            rule, created = CampaignTagRule.objects.get_or_create(
                name=rule_name,
                defaults={
                    'id': uuid.uuid4(),
                    'tag': tag.name,
                    'description': tag.description or '',
                    'active': True
                }
            )

            logger.info(f"Tag rule {'created' if created else 'updated'}: {rule.id}")

            if not created:
                # Update the rule
                rule.tag = tag.name
                rule.description = tag.description or ''
                rule.save()

                try:
                    # Delete existing conditions using Django's ORM
                    count = CampaignTagCondition.objects.filter(rule=rule).delete()[0]
                    logger.info(f"Deleted {count} existing conditions for rule: {rule.id}")
                except Exception as e:
                    logger.error(f"Error deleting existing conditions: {str(e)}")
                    logger.error(traceback.format_exc())

            # Create conditions for the rule if there are any
            if conditions:
                logger.info(f"Creating {len(conditions)} conditions for rule {rule.id}")

                for i, condition in enumerate(conditions):
                    try:
                        # Log the condition data for debugging
                        logger.info(f"Creating condition {i+1}/{len(conditions)}: {condition}")

                        # Ensure we have valid values for all required fields
                        field = condition.get('field', 'bio')
                        field_type = condition.get('field_type', 'string')
                        operator = condition.get('operator', 'icontains')
                        value = condition.get('value', '')

                        # Validate field, field_type, and operator
                        if not field:
                            logger.warning(f"Empty field in condition {i+1}, using default 'bio'")
                            field = 'bio'

                        if not field_type:
                            logger.warning(f"Empty field_type in condition {i+1}, using default 'string'")
                            field_type = 'string'

                        if not operator:
                            logger.warning(f"Empty operator in condition {i+1}, using default 'icontains'")
                            operator = 'icontains'

                        # Convert value to JSON if it's not already a string
                        if not isinstance(value, str):
                            try:
                                value = json.dumps(value)
                                logger.info(f"Converted non-string value to JSON: {value}")
                            except Exception as e:
                                logger.error(f"Error converting value to JSON: {str(e)}")
                                logger.error(traceback.format_exc())
                                value = str(value)
                                logger.info(f"Converted non-string value to string: {value}")

                        # Create the condition
                        logger.info(f"Creating condition with field={field}, field_type={field_type}, operator={operator}, value={value}")
                        condition_obj = CampaignTagCondition.objects.create(
                            id=uuid.uuid4(),
                            rule=rule,
                            field=field,
                            field_type=field_type,
                            operator=operator,
                            value=value,
                            score=1,
                            required=condition.get('required', True)
                        )
                        logger.info(f"Successfully created condition: {condition_obj.id} for rule: {rule.id}")
                    except Exception as e:
                        logger.error(f"Error creating condition {i+1}: {str(e)}")
                        logger.error(traceback.format_exc())
                        # Continue with the next condition instead of failing completely

            # Store the rule ID and logic in the tag's pattern field
            # This is a hack for backward compatibility
            rule_data = {
                'rule_id': str(rule.id),
                'logic': condition_logic
            }
            tag.pattern = json.dumps(rule_data)
            tag.save(update_fields=['pattern'])
            logger.info(f"Updated tag pattern with rule data: {rule_data}")

            # Verify that the conditions were saved correctly
            saved_conditions = CampaignTagCondition.objects.filter(rule=rule)
            logger.info(f"Verification: Found {saved_conditions.count()} conditions for rule {rule.id}")

            for i, condition in enumerate(saved_conditions):
                logger.info(f"Saved condition {i+1}: field={condition.field}, operator={condition.operator}, value={condition.value}")

            return True
        except Exception as e:
            logger.exception(f"Error saving tag conditions: {str(e)}")
            logger.error(traceback.format_exc())
            # Don't re-raise the exception, as we want the tag to be saved even if conditions fail
            # Just log the error
            return False


class CampaignTagForm(forms.ModelForm):
    """
    Form for assigning tags to campaigns.
    """
    class Meta:
        model = CampaignTag
        fields = ['tag', 'is_required']
        widgets = {
            'tag': forms.Select(attrs={'class': 'form-select'}),
            'is_required': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        campaign = kwargs.pop('campaign', None)
        super().__init__(*args, **kwargs)

        # Filter tags to show all tags not already assigned to this campaign
        if campaign:
            assigned_tag_ids = CampaignTag.objects.filter(campaign=campaign).values_list('tag_id', flat=True)
            self.fields['tag'].queryset = DynamicTag.objects.exclude(id__in=assigned_tag_ids).order_by('name')
        else:
            self.fields['tag'].queryset = DynamicTag.objects.all().order_by('name')


class TagGroupForm(forms.ModelForm):
    """
    Form for creating and editing tag groups.
    """
    tags = forms.ModelMultipleChoiceField(
        queryset=DynamicTag.objects.all(),
        required=False,
        widget=forms.SelectMultiple(attrs={'class': 'form-select', 'size': '8'}),
        help_text="Select multiple tags to include in this group. Hold Ctrl (or Cmd on Mac) to select multiple tags."
    )

    class Meta:
        model = TagGroup
        fields = ['name', 'description', 'color', 'priority', 'tags']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
            'color': forms.TextInput(attrs={'type': 'color', 'class': 'form-control form-control-color'}),
            'priority': forms.HiddenInput(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['name'].widget.attrs.update({'class': 'form-control'})
        self.fields['description'].widget.attrs.update({'class': 'form-control'})
        self.fields['color'].widget.attrs.update({'class': 'form-control form-control-color'})

        # Set default color if not provided
        if not self.instance.color:
            self.fields['color'].initial = '#6c757d'

        # Set the queryset for tags to be ordered by name
        self.fields['tags'].queryset = DynamicTag.objects.all().order_by('name')

        # If we're editing an existing tag group, set initial values for tags
        if self.instance and self.instance.pk:
            self.fields['tags'].initial = self.instance.tags.all()

        # Add help text
        self.fields['name'].help_text = "Enter a descriptive name for this tag group."
        self.fields['description'].help_text = "Provide a brief description of this tag group's purpose."

    def save(self, commit=True):
        tag_group = super().save(commit=commit)

        if commit:
            # Clear existing tags and add the selected ones
            tag_group.tags.clear()
            for tag in self.cleaned_data.get('tags', []):
                tag.tag_group = tag_group
                tag.save()

        return tag_group



