"""
Enhanced Analysis Service for comprehensive account analysis.
"""
import logging
import time
from django.utils import timezone
from django.db import transaction

logger = logging.getLogger(__name__)


class EnhancedAnalysisService:
    """
    Service for enhanced account analysis using multiple analysis methods.
    Integrates basic tagging, machine learning, and natural language processing.
    """

    def __init__(self, repository=None, tagging_system=None, ml_service=None, nlp_service=None):
        """
        Initialize the enhanced analysis service.

        Args:
            repository: Optional repository for data access
            tagging_system: Optional tagging system for basic analysis
            ml_service: Optional machine learning service
            nlp_service: Optional natural language processing service
        """
        self.repository = repository
        self.tagging_system = tagging_system
        self.ml_service = ml_service
        self.nlp_service = nlp_service

        # Import services if not provided
        if not self.tagging_system:
            from instagram.tagging_engine import AutoTaggingSystem
            self.tagging_system = AutoTaggingSystem()

        if not self.ml_service:
            from campaigns.services.ml_service import MLService
            self.ml_service = MLService()

        if not self.nlp_service:
            from campaigns.services.nlp_service import NLPService
            self.nlp_service = NLPService()

    def analyze_campaign(self, campaign_id, options=None):
        """
        Analyze accounts collected by a campaign using enhanced methods.

        Args:
            campaign_id (uuid): Campaign ID
            options (dict): Optional analysis options

        Returns:
            dict: Analysis results
        """
        from campaigns.models import Campaign, CampaignResult, TagAnalysisResult
        from instagram.models import Accounts, WhiteListEntry

        options = options or {}
        batch_size = options.get('batch_size', 100)
        progress_callback = options.get('progress_callback')

        try:
            # Start timing
            start_time = time.time()

            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)

            # Get analysis settings
            try:
                analysis_settings = campaign.analysis_settings
            except:
                # If no analysis settings, use defaults
                from campaigns.models import CampaignAnalysisSettings
                analysis_settings = CampaignAnalysisSettings.objects.create(
                    campaign=campaign,
                    auto_analyze=True,
                    analysis_frequency='immediate',
                    analysis_mode='basic'
                )

            # Get accounts to analyze
            accounts = Accounts.objects.filter(campaign=campaign)

            # Apply filters from analysis settings
            if analysis_settings.min_followers is not None:
                accounts = accounts.filter(followers__gte=analysis_settings.min_followers)

            if analysis_settings.max_followers is not None:
                accounts = accounts.filter(followers__lte=analysis_settings.max_followers)

            # Initialize counters
            total = accounts.count()
            processed = 0
            tagged = 0
            white_listed = 0
            total_confidence = 0.0

            # Process accounts
            for i in range(0, total, batch_size):
                batch = accounts[i:i+batch_size]

                for account in batch:
                    # Initialize results
                    all_tags = []
                    all_tag_results = []
                    total_account_confidence = 0.0
                    tag_count = 0

                    # 1. Basic tagging analysis
                    basic_result = self.tagging_system.process_account(account)
                    all_tags.extend(basic_result.get('tags', []))

                    # 2. Dynamic tag analysis
                    if analysis_settings.enable_dynamic_tagging:
                        dynamic_tags = analysis_settings.dynamic_tags.all()

                        for tag in dynamic_tags:
                            # Determine analysis mode based on tag type and settings
                            analysis_mode = 'basic'

                            if tag.tag_type == 'ml' and analysis_settings.enable_ml_analysis:
                                analysis_mode = 'ml'
                            elif tag.tag_type in ['sentiment', 'category'] and analysis_settings.enable_nlp_analysis:
                                analysis_mode = 'nlp'

                            # Analyze account with appropriate method
                            tag_result = self._analyze_with_tag(account, tag, analysis_mode)

                            # Store result
                            with transaction.atomic():
                                TagAnalysisResult.objects.update_or_create(
                                    account=account,
                                    campaign=campaign,
                                    tag=tag,
                                    defaults={
                                        'matched': tag_result['matched'],
                                        'confidence_score': tag_result['confidence'],
                                        'match_details': tag_result.get('details', {}),
                                        'analysis_mode': analysis_mode
                                    }
                                )

                            # Add tag if matched with sufficient confidence
                            if tag_result['matched'] and tag_result['confidence'] >= analysis_settings.confidence_threshold:
                                tag_name = f"dynamic_{tag.name}"
                                all_tags.append(tag_name)

                                # Update confidence metrics
                                total_account_confidence += tag_result['confidence']
                                tag_count += 1

                            # Add to all results
                            all_tag_results.append({
                                'tag': tag.name,
                                'matched': tag_result['matched'],
                                'confidence': tag_result['confidence'],
                                'mode': analysis_mode
                            })

                    # Calculate average confidence for this account
                    account_avg_confidence = 0.0
                    if tag_count > 0:
                        account_avg_confidence = total_account_confidence / tag_count
                        total_confidence += account_avg_confidence

                    # Update whitelist if needed
                    qualifies = bool(basic_result.get('privileges'))
                    if qualifies:
                        WhiteListEntry.objects.update_or_create(
                            account=account,
                            defaults={
                                'tags': all_tags,
                                'is_auto': True,
                                **basic_result['privileges']
                            }
                        )
                        white_listed += 1
                    else:
                        # Remove from whitelist if it exists
                        WhiteListEntry.objects.filter(account=account).delete()

                    # Update counters
                    processed += 1
                    if all_tags:
                        tagged += 1

                    # Report progress
                    if progress_callback and processed % 10 == 0:
                        progress_callback(processed)

            # Calculate analysis duration
            duration = time.time() - start_time

            # Calculate average confidence
            avg_confidence = 0.0
            if tagged > 0:
                avg_confidence = total_confidence / tagged

            # Update campaign result
            with transaction.atomic():
                result, created = CampaignResult.objects.get_or_create(campaign=campaign)
                result.total_accounts_processed = processed
                result.total_accounts_tagged = tagged
                result.total_accounts_whitelisted = white_listed
                result.analysis_duration = duration
                result.last_processed_at = timezone.now()
                result.save()

            # Update tag metrics
            self._update_tag_metrics(campaign_id)

            return {
                'total': total,
                'processed': processed,
                'tagged': tagged,
                'white_listed': white_listed,
                'avg_confidence': avg_confidence,
                'duration': duration,
                'percentage': (white_listed / total * 100) if total > 0 else 0
            }

        except Exception as e:
            logger.exception(f"Error in enhanced analysis: {str(e)}")
            return {
                'error': str(e)
            }

    def _analyze_with_tag(self, account, tag, mode='basic'):
        """
        Analyze an account with a specific tag using the appropriate method.

        Args:
            account: Account to analyze
            tag: Tag to use for analysis
            mode: Analysis mode ('basic', 'ml', or 'nlp')

        Returns:
            dict: Analysis result
        """
        # Get field value
        field_value = getattr(account, tag.field, None)

        # Skip if field value is None
        if field_value is None:
            return {
                'matched': False,
                'confidence': 0.0,
                'details': {'error': f'Field {tag.field} not found'}
            }

        # Convert to string if it's not already
        if isinstance(field_value, list):
            # For array fields like interests, locations, etc.
            field_value = ' '.join(str(item) for item in field_value if item)
        else:
            field_value = str(field_value)

        # Analyze based on mode
        if mode == 'ml':
            # Use machine learning
            result = self.ml_service.analyze_account(tag, account)
            return result

        elif mode == 'nlp':
            # Use natural language processing
            if tag.tag_type == 'sentiment':
                # Sentiment analysis
                sentiment = self.nlp_service.analyze_sentiment(field_value)
                threshold = float(tag.pattern) if tag.pattern.replace('.', '', 1).isdigit() else 0.0

                matched = False
                confidence = 0.0

                if 'positive' in tag.pattern.lower() and sentiment['sentiment'] == 'positive':
                    matched = True
                    confidence = abs(sentiment['score'])
                elif 'negative' in tag.pattern.lower() and sentiment['sentiment'] == 'negative':
                    matched = True
                    confidence = abs(sentiment['score'])
                elif 'neutral' in tag.pattern.lower() and sentiment['sentiment'] == 'neutral':
                    matched = True
                    confidence = 1.0 - abs(sentiment['score'])
                elif sentiment['score'] >= threshold:
                    matched = True
                    confidence = sentiment['score']

                return {
                    'matched': matched,
                    'confidence': confidence,
                    'details': sentiment
                }

            elif tag.tag_type == 'category':
                # Topic/category analysis
                topics = self.nlp_service.identify_topics(field_value)

                # Check if any of the specified categories match
                categories = [c.strip().lower() for c in tag.pattern.split(',')]
                matched = False
                confidence = 0.0

                if topics['primary_topic'] and topics['primary_topic'].lower() in categories:
                    matched = True
                    topic_score = topics['topics'].get(topics['primary_topic'], 0)
                    confidence = min(topic_score / 10, 1.0)  # Normalize confidence

                return {
                    'matched': matched,
                    'confidence': confidence,
                    'details': topics
                }

            else:
                # Use pattern matching for other types
                result = self.nlp_service.match_pattern(field_value, tag.pattern)
                return {
                    'matched': result['matched'],
                    'confidence': 1.0 if result['matched'] else 0.0,
                    'details': result
                }

        else:
            # Basic analysis (keyword or regex)
            import re

            if tag.tag_type == 'keyword':
                # Split pattern into keywords
                keywords = [k.strip().lower() for k in tag.pattern.split(',')]
                matched = any(keyword in field_value.lower() for keyword in keywords)

                # Calculate confidence based on number of matching keywords
                matching_keywords = [k for k in keywords if k in field_value.lower()]
                confidence = len(matching_keywords) / len(keywords) if keywords else 0.0

                return {
                    'matched': matched,
                    'confidence': confidence,
                    'details': {
                        'matching_keywords': matching_keywords,
                        'total_keywords': len(keywords)
                    }
                }

            elif tag.tag_type == 'regex':
                try:
                    matches = re.findall(tag.pattern, field_value, re.IGNORECASE)
                    return {
                        'matched': bool(matches),
                        'confidence': 1.0 if matches else 0.0,
                        'details': {
                            'matches': matches,
                            'count': len(matches)
                        }
                    }
                except Exception as e:
                    logger.error(f"Error in regex analysis: {str(e)}")
                    return {
                        'matched': False,
                        'confidence': 0.0,
                        'details': {'error': str(e)}
                    }

            else:
                # Unsupported tag type for basic analysis
                return {
                    'matched': False,
                    'confidence': 0.0,
                    'details': {'error': f'Unsupported tag type: {tag.tag_type}'}
                }

    def _update_tag_metrics(self, campaign_id):
        """
        Update tag metrics based on analysis results.

        Args:
            campaign_id (uuid): Campaign ID
        """
        from campaigns.models import TagAnalysisResult, TagMetrics, DynamicTag
        from django.db.models import Count, Avg, F

        try:
            # Get all tags used in this campaign
            tag_results = TagAnalysisResult.objects.filter(campaign_id=campaign_id)
            tag_ids = tag_results.values_list('tag_id', flat=True).distinct()

            for tag_id in tag_ids:
                # Get tag
                tag = DynamicTag.objects.get(id=tag_id)

                # Get tag results
                results = tag_results.filter(tag_id=tag_id)

                # Calculate metrics
                total_count = results.count()
                match_count = results.filter(matched=True).count()

                # Calculate precision, recall, and F1 score
                # Note: This is a simplified calculation that assumes all matches are correct
                # In a real system, you would need human feedback to calculate true precision and recall
                precision = match_count / total_count if total_count > 0 else 0.0
                recall = precision  # Simplified assumption
                f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0

                # Update tag metrics
                with transaction.atomic():
                    metrics, created = TagMetrics.objects.get_or_create(tag=tag)
                    metrics.usage_count += 1
                    metrics.match_count += match_count
                    metrics.precision = precision
                    metrics.recall = recall
                    metrics.f1_score = f1_score
                    metrics.last_evaluated = timezone.now()
                    metrics.save()

        except Exception as e:
            logger.exception(f"Error updating tag metrics: {str(e)}")

    def get_analysis_stats(self, campaign_id):
        """
        Get enhanced analysis statistics for a campaign.

        Args:
            campaign_id (uuid): Campaign ID

        Returns:
            dict: Analysis statistics
        """
        from campaigns.models import Campaign, CampaignResult, TagAnalysisResult
        from instagram.models import Accounts, WhiteListEntry
        from django.db.models import Count, Avg

        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)

            # Get campaign result
            result, created = CampaignResult.objects.get_or_create(campaign=campaign)

            # Get total accounts
            total_accounts = Accounts.objects.filter(campaign=campaign).count()

            # Get whitelisted accounts
            white_listed = WhiteListEntry.objects.filter(
                account__campaign=campaign
            ).count()

            # Get tag analysis results
            tag_results = TagAnalysisResult.objects.filter(campaign=campaign)

            # Get analysis mode distribution
            mode_distribution = tag_results.values('analysis_mode').annotate(
                count=Count('id')
            )

            # Get tag match distribution
            tag_distribution = tag_results.values('tag__name').annotate(
                total=Count('id'),
                matched=Count('id', filter=F('matched')),
                avg_confidence=Avg('confidence_score')
            )

            return {
                'total': total_accounts,
                'processed': result.total_accounts_processed,
                'tagged': result.total_accounts_tagged,
                'white_listed': white_listed,
                'duration': result.analysis_duration,
                'last_processed_at': result.last_processed_at,
                'mode_distribution': list(mode_distribution),
                'tag_distribution': list(tag_distribution),
                'percentage': (white_listed / total_accounts * 100) if total_accounts > 0 else 0
            }

        except Exception as e:
            logger.exception(f"Error getting enhanced analysis stats: {str(e)}")
            return {
                'error': str(e)
            }
