"""
Models for tracking workflow execution and progress.
"""
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models.signals import post_save
from django.dispatch import receiver
import uuid

class WorkflowExecution(models.Model):
    """
    Model to track PyFlow workflow execution.
    """
    WORKFLOW_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    WORKFLOW_TYPE_CHOICES = [
        ('collection', 'Account Collection'),
        ('analysis', 'Account Analysis'),
        ('tagging', 'Account Tagging'),
        ('follow', 'Follow'),
        ('like', 'Like'),
        ('comment', 'Comment'),
        ('dm', 'Direct Message'),
        ('cep', 'CEP'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    campaign = models.ForeignKey('Campaign', on_delete=models.CASCADE, help_text="Campaign this workflow belongs to")
    workflow_name = models.CharField(max_length=255, help_text="Name of the PyFlow workflow file")
    workflow_path = models.CharField(max_length=512, help_text="Path to the PyFlow workflow file")
    workflow_type = models.CharField(max_length=20, choices=WORKFLOW_TYPE_CHOICES)
    status = models.CharField(max_length=20, choices=WORKFLOW_STATUS_CHOICES, default='pending')
    start_time = models.DateTimeField(auto_now_add=True)
    end_time = models.DateTimeField(null=True, blank=True)
    duration = models.FloatField(default=0.0, help_text="Duration in seconds")
    progress = models.FloatField(default=0.0, help_text="Progress percentage (0-100)")
    total_items = models.IntegerField(default=0, help_text="Total number of items to process")
    processed_items = models.IntegerField(default=0, help_text="Number of items processed")
    successful_items = models.IntegerField(default=0, help_text="Number of items processed successfully")
    failed_items = models.IntegerField(default=0, help_text="Number of items that failed processing")
    error_message = models.TextField(blank=True, null=True, help_text="Error message if workflow failed")
    log_file = models.CharField(max_length=512, blank=True, null=True, help_text="Path to the workflow log file")
    parameters = models.JSONField(default=dict, help_text="Parameters passed to the workflow")
    results = models.JSONField(default=dict, help_text="Results of the workflow execution")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.get_workflow_type_display()} for campaign {self.campaign.name if self.campaign else 'Unknown'} ({self.get_status_display()})"

    def update_progress(self, processed_items, successful_items=None, failed_items=None):
        """
        Update workflow progress.

        Args:
            processed_items (int): Number of items processed
            successful_items (int): Number of items processed successfully
            failed_items (int): Number of items that failed processing
        """
        self.processed_items = processed_items

        if successful_items is not None:
            self.successful_items = successful_items

        if failed_items is not None:
            self.failed_items = failed_items

        if self.total_items > 0:
            self.progress = (self.processed_items / self.total_items) * 100

        self.updated_at = timezone.now()
        self.save(update_fields=['processed_items', 'successful_items', 'failed_items', 'progress', 'updated_at'])

    def complete(self, results=None):
        """
        Mark workflow as completed.

        Args:
            results (dict): Results of the workflow execution
        """
        self.status = 'completed'
        self.end_time = timezone.now()
        self.duration = (self.end_time - self.start_time).total_seconds()
        self.progress = 100.0

        if results:
            self.results = results

        self.updated_at = timezone.now()
        self.save()

    def fail(self, error_message=None):
        """
        Mark workflow as failed.

        Args:
            error_message (str): Error message
        """
        self.status = 'failed'
        self.end_time = timezone.now()
        self.duration = (self.end_time - self.start_time).total_seconds()

        if error_message:
            self.error_message = error_message

        self.updated_at = timezone.now()
        self.save()

    def cancel(self):
        """
        Mark workflow as cancelled.
        """
        self.status = 'cancelled'
        self.end_time = timezone.now()
        self.duration = (self.end_time - self.start_time).total_seconds()
        self.updated_at = timezone.now()
        self.save()

    def handle_error(self, error_details):
        """
        Handle workflow error and store error details.

        Args:
            error_details (dict): Dictionary containing error information
        """
        self.status = 'failed'
        self.end_time = timezone.now()
        self.duration = (self.end_time - self.start_time).total_seconds()

        if isinstance(error_details, dict):
            self.error_message = error_details.get('error_message', 'Unknown error')
            # Store full error details in results field
            self.results.update({'error_details': error_details})
        else:
            self.error_message = str(error_details)

        self.updated_at = timezone.now()
        self.save()

    def retry(self):
        """
        Reset workflow for retry.
        """
        # Add retry_count field if it doesn't exist
        if not hasattr(self, 'retry_count'):
            self.retry_count = 0

        self.retry_count = getattr(self, 'retry_count', 0) + 1
        self.status = 'pending'
        self.end_time = None
        self.duration = 0.0
        self.error_message = None
        self.updated_at = timezone.now()
        self.save()

    class Meta:
        db_table = 'campaigns_workflow_execution'
        verbose_name = "Workflow Execution"
        verbose_name_plural = "Workflow Executions"
        ordering = ['-start_time']
        indexes = [
            models.Index(fields=['campaign', 'workflow_type']),
            models.Index(fields=['status']),
            models.Index(fields=['start_time']),
        ]


class WorkflowProgressUpdate(models.Model):
    """
    Model to store workflow progress updates.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workflow_execution = models.ForeignKey(WorkflowExecution, on_delete=models.CASCADE, related_name='progress_updates')
    timestamp = models.DateTimeField(auto_now_add=True)
    processed_items = models.IntegerField(default=0)
    successful_items = models.IntegerField(default=0)
    failed_items = models.IntegerField(default=0)
    progress = models.FloatField(default=0.0)
    message = models.TextField(blank=True, null=True)
    details = models.JSONField(default=dict)

    def __str__(self):
        return f"Progress update for {self.workflow_execution} at {self.timestamp}"

    class Meta:
        db_table = 'campaigns_workflow_progress_update'
        verbose_name = "Workflow Progress Update"
        verbose_name_plural = "Workflow Progress Updates"
        ordering = ['-timestamp']


class Workflow(models.Model):
    """
    New comprehensive workflow model that processes campaign whitelists.
    This will eventually replace CEPWorkflow.
    """
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('stopped', 'Stopped'),
    ]

    SCHEDULE_TYPE_CHOICES = [
        ('immediate', 'Start Immediately'),
        ('time_based', 'Time-based Schedule'),
        ('date_based', 'Date-based Schedule'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(
        max_length=255,
        help_text="Name of the workflow"
    )
    description = models.TextField(
        blank=True,
        help_text="Description of the workflow"
    )

    # Source campaigns for whitelist processing
    source_campaigns = models.ManyToManyField(
        'Campaign',
        related_name='workflows',
        help_text="Campaigns whose whitelists will be processed"
    )

    # Template used for this workflow
    template = models.ForeignKey(
        'WorkflowTemplate',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Template used to create this workflow"
    )

    # Workflow configuration
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='draft',
        help_text="Current status of the workflow"
    )

    # Action configuration
    enabled_actions = models.JSONField(
        default=list,
        help_text="List of enabled action types"
    )
    action_parameters = models.JSONField(
        default=dict,
        help_text="Parameters for each action type"
    )

    # Limits and scheduling
    daily_limit = models.IntegerField(
        default=50,
        help_text="Maximum actions per day"
    )
    hourly_limit = models.IntegerField(
        default=10,
        help_text="Maximum actions per hour"
    )
    delay_between_actions = models.IntegerField(
        default=30,
        help_text="Delay between actions in seconds"
    )

    # Scheduling
    schedule_type = models.CharField(
        max_length=20,
        choices=SCHEDULE_TYPE_CHOICES,
        default='immediate',
        help_text="How the workflow should be scheduled"
    )
    scheduled_start = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the workflow should start (for scheduled workflows)"
    )

    # Execution tracking
    airflow_dag_id = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="Generated Airflow DAG ID"
    )
    airflow_run_id = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="Current Airflow run ID"
    )

    # Progress tracking
    total_accounts = models.IntegerField(
        default=0,
        help_text="Total number of accounts to process"
    )
    processed_accounts = models.IntegerField(
        default=0,
        help_text="Number of accounts processed"
    )
    successful_actions = models.IntegerField(
        default=0,
        help_text="Number of successful actions"
    )
    failed_actions = models.IntegerField(
        default=0,
        help_text="Number of failed actions"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'campaigns_workflow'
        verbose_name = "Workflow"
        verbose_name_plural = "Workflows"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_status_display()})"

    @property
    def progress_percentage(self):
        """Calculate progress percentage."""
        if self.total_accounts == 0:
            return 0.0
        return (self.processed_accounts / self.total_accounts) * 100

    @property
    def success_rate(self):
        """Calculate success rate of actions."""
        total_actions = self.successful_actions + self.failed_actions
        if total_actions == 0:
            return 0.0
        return (self.successful_actions / total_actions) * 100

    def can_start(self):
        """Check if workflow can be started."""
        return self.status in ['draft', 'pending', 'paused']

    def can_pause(self):
        """Check if workflow can be paused."""
        return self.status == 'running'

    def can_stop(self):
        """Check if workflow can be stopped."""
        return self.status in ['pending', 'running', 'paused']


class WorkflowTemplate(models.Model):
    """
    Model to define predefined workflow templates with configurable actions.
    """
    ACTION_TYPE_CHOICES = [
        ('follow', 'Follow'),
        ('like', 'Like Posts'),
        ('comment', 'Comment on Posts'),
        ('dm', 'Direct Message'),
        ('story_view', 'View Stories'),
        ('story_reaction', 'React to Stories'),
        ('story_reply', 'Reply to Stories'),
        ('discover', 'Discover Accounts'),
    ]

    CATEGORY_CHOICES = [
        ('growth', 'Growth Focused'),
        ('engagement', 'Engagement Heavy'),
        ('conservative', 'Conservative Outreach'),
        ('discovery', 'Account Discovery'),
        ('custom', 'Custom Template'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(
        max_length=255,
        help_text="Name of the workflow template"
    )
    description = models.TextField(
        help_text="Description of the workflow template"
    )
    category = models.CharField(
        max_length=20,
        choices=CATEGORY_CHOICES,
        default='custom',
        help_text="Category of the workflow template"
    )

    # Action configuration - JSON field storing action settings
    available_actions = models.JSONField(
        default=dict,
        help_text="Available actions and their configurations"
    )

    # Default limits
    default_daily_limit = models.IntegerField(
        default=50,
        help_text="Default maximum actions per day"
    )
    default_hourly_limit = models.IntegerField(
        default=10,
        help_text="Default maximum actions per hour"
    )
    default_delay_between_actions = models.IntegerField(
        default=30,
        help_text="Default delay between actions in seconds"
    )

    # DAG configuration
    dag_structure = models.JSONField(
        default=dict,
        help_text="DAG structure defining action sequence and dependencies"
    )

    is_predefined = models.BooleanField(
        default=False,
        help_text="Whether this is a predefined system template"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this template is available for use"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'campaigns_workflow_template'
        verbose_name = "Workflow Template"
        verbose_name_plural = "Workflow Templates"
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_category_display()})"

    def get_text_actions(self):
        """Get actions that require text input (DM, comment, story reply)."""
        text_actions = []
        for action_type, config in self.available_actions.items():
            if action_type in ['dm', 'comment', 'story_reply']:
                text_actions.append(action_type)
        return text_actions


class WorkflowAction(models.Model):
    """
    Model to track individual actions within a workflow.
    """
    ACTION_TYPE_CHOICES = [
        ('follow', 'Follow'),
        ('like', 'Like Posts'),
        ('comment', 'Comment on Posts'),
        ('dm', 'Direct Message'),
        ('story_view', 'View Stories'),
        ('story_reaction', 'React to Stories'),
        ('story_reply', 'Reply to Stories'),
        ('discover', 'Discover Accounts'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('skipped', 'Skipped'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workflow = models.ForeignKey(
        Workflow,
        on_delete=models.CASCADE,
        related_name='actions',
        help_text="Workflow this action belongs to"
    )

    action_type = models.CharField(
        max_length=20,
        choices=ACTION_TYPE_CHOICES,
        help_text="Type of action to perform"
    )

    target_username = models.CharField(
        max_length=255,
        help_text="Instagram username to target"
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text="Current status of the action"
    )

    # Action parameters
    parameters = models.JSONField(
        default=dict,
        help_text="Parameters for this specific action"
    )

    # Execution tracking
    scheduled_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this action is scheduled to run"
    )
    started_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this action started executing"
    )
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this action completed"
    )

    # Results
    result_data = models.JSONField(
        default=dict,
        help_text="Results and metadata from action execution"
    )
    error_message = models.TextField(
        blank=True,
        help_text="Error message if action failed"
    )

    # Retry tracking
    retry_count = models.IntegerField(
        default=0,
        help_text="Number of times this action has been retried"
    )
    max_retries = models.IntegerField(
        default=3,
        help_text="Maximum number of retries allowed"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'campaigns_workflow_action'
        verbose_name = "Workflow Action"
        verbose_name_plural = "Workflow Actions"
        ordering = ['scheduled_at', 'created_at']
        indexes = [
            models.Index(fields=['workflow', 'status']),
            models.Index(fields=['action_type', 'status']),
            models.Index(fields=['scheduled_at']),
        ]

    def __str__(self):
        return f"{self.get_action_type_display()} for @{self.target_username} ({self.get_status_display()})"

    @property
    def duration(self):
        """Calculate action duration in seconds."""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None

    def can_retry(self):
        """Check if action can be retried."""
        return self.status == 'failed' and self.retry_count < self.max_retries


class ResourceManager(models.Model):
    """
    Model to manage workflow resources and enforce limits.
    """
    PRIORITY_MODE_CHOICES = [
        ('cep', 'CEP Workflows Priority'),
        ('dmp', 'DMP Priority'),
        ('balanced', 'Balanced Priority'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Active workflow management
    active_workflow = models.OneToOneField(
        Workflow,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resource_manager',
        help_text="Currently active workflow (one-active-workflow restriction)"
    )

    # Resource limits
    max_daily_actions = models.IntegerField(
        default=500,
        help_text="Maximum total actions per day across all workflows"
    )
    max_hourly_actions = models.IntegerField(
        default=50,
        help_text="Maximum total actions per hour across all workflows"
    )
    max_concurrent_actions = models.IntegerField(
        default=5,
        help_text="Maximum concurrent actions"
    )

    # Priority settings
    priority_mode = models.CharField(
        max_length=20,
        choices=PRIORITY_MODE_CHOICES,
        default='cep',
        help_text="Resource allocation priority mode"
    )

    # Current usage tracking
    daily_actions_used = models.IntegerField(
        default=0,
        help_text="Actions used today"
    )
    hourly_actions_used = models.IntegerField(
        default=0,
        help_text="Actions used this hour"
    )
    current_concurrent_actions = models.IntegerField(
        default=0,
        help_text="Currently running actions"
    )

    # Queue management
    action_queue_size = models.IntegerField(
        default=0,
        help_text="Number of actions in queue"
    )
    max_queue_size = models.IntegerField(
        default=1000,
        help_text="Maximum queue size"
    )

    # Status
    is_active = models.BooleanField(
        default=True,
        help_text="Whether resource manager is active"
    )
    last_reset_date = models.DateField(
        auto_now_add=True,
        help_text="Last date when daily counters were reset"
    )
    last_hourly_reset = models.DateTimeField(
        auto_now_add=True,
        help_text="Last time hourly counters were reset"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'campaigns_resource_manager'
        verbose_name = "Resource Manager"
        verbose_name_plural = "Resource Managers"

    def __str__(self):
        return f"Resource Manager ({self.get_priority_mode_display()})"

    @property
    def daily_usage_percentage(self):
        """Calculate daily usage percentage."""
        if self.max_daily_actions == 0:
            return 0.0
        return (self.daily_actions_used / self.max_daily_actions) * 100

    @property
    def hourly_usage_percentage(self):
        """Calculate hourly usage percentage."""
        if self.max_hourly_actions == 0:
            return 0.0
        return (self.hourly_actions_used / self.max_hourly_actions) * 100

    def can_execute_action(self):
        """Check if a new action can be executed."""
        return (
            self.is_active and
            self.daily_actions_used < self.max_daily_actions and
            self.hourly_actions_used < self.max_hourly_actions and
            self.current_concurrent_actions < self.max_concurrent_actions
        )

    def can_queue_action(self):
        """Check if a new action can be queued."""
        return self.action_queue_size < self.max_queue_size

    def get_non_text_actions(self):
        """Get actions that don't require text input."""
        non_text_actions = []
        for action, config in self.available_actions.items():
            if action not in ['dm', 'comment', 'story_reply'] and config.get('enabled', False):
                non_text_actions.append(action)
        return non_text_actions
