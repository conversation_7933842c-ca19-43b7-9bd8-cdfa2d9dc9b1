"""
Export service for campaign data.
Handles proper data export without CEP-related fields.
"""
import csv
import logging
from io import StringIO
from django.http import HttpResponse
from django.db.models import Q, Prefetch
from campaigns.models import Campaign, CampaignTag
from instagram.models import Accounts

logger = logging.getLogger(__name__)


class CampaignExportService:
    """Service for exporting campaign data in various formats."""

    def __init__(self, campaign):
        """Initialize the export service with a campaign."""
        self.campaign = campaign

    def export_whitelist_csv(self):
        """
        Export campaign whitelist as CSV with proper account data and tags.
        Excludes CEP-related columns.
        """
        logger.info(f"Starting whitelist CSV export for campaign {self.campaign.id}")
        
        # Get whitelisted accounts based on campaign tag assignments
        whitelisted_accounts = self._get_whitelisted_accounts()
        
        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="campaign_{self.campaign.id}_whitelist.csv"'
        
        writer = csv.writer(response)
        
        # Write headers - comprehensive account data without CEP fields
        headers = [
            'Username',
            'Full Name', 
            'Bio',
            'Followers',
            'Following',
            'Posts',
            'Account Type',
            'Verified',
            'Phone Number',
            'Interests',
            'Locations',
            'Links',
            'Assigned Tags',
            'Required Tags',
            'Collection Date'
        ]
        writer.writerow(headers)
        
        # Write data rows
        for account_data in whitelisted_accounts:
            account = account_data['account']
            tags_info = account_data['tags_info']
            
            writer.writerow([
                account.username,
                account.full_name or '',
                account.bio or '',
                account.followers or 0,
                account.following or 0,
                account.number_of_posts or 0,
                account.account_type or '',
                'Yes' if account.is_verified else 'No',
                account.phone_number or '',
                ', '.join(account.interests) if account.interests else '',
                ', '.join(account.locations) if account.locations else '',
                ', '.join(account.links) if account.links else '',
                ', '.join(tags_info['all_tags']),
                ', '.join(tags_info['required_tags']),
                account.collection_date.strftime('%Y-%m-%d %H:%M:%S') if account.collection_date else ''
            ])
        
        logger.info(f"Exported {len(whitelisted_accounts)} whitelisted accounts to CSV")
        return response

    def export_whitelist_excel(self):
        """
        Export campaign whitelist as Excel with proper account data and tags.
        Excludes CEP-related columns.
        """
        try:
            import xlsxwriter
            from io import BytesIO
        except ImportError:
            logger.error("xlsxwriter not available for Excel export")
            return None
        
        logger.info(f"Starting whitelist Excel export for campaign {self.campaign.id}")
        
        # Get whitelisted accounts
        whitelisted_accounts = self._get_whitelisted_accounts()
        
        # Create Excel workbook
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet('Whitelist')
        
        # Add formats
        bold = workbook.add_format({'bold': True})
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4472C4',
            'font_color': 'white',
            'border': 1
        })
        
        # Write headers
        headers = [
            'Username', 'Full Name', 'Bio', 'Followers', 'Following', 'Posts',
            'Account Type', 'Verified', 'Phone Number', 'Interests', 'Locations',
            'Links', 'Assigned Tags', 'Required Tags', 'Collection Date'
        ]
        
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)
        
        # Write data rows
        for row, account_data in enumerate(whitelisted_accounts, 1):
            account = account_data['account']
            tags_info = account_data['tags_info']
            
            worksheet.write(row, 0, account.username)
            worksheet.write(row, 1, account.full_name or '')
            worksheet.write(row, 2, account.bio or '')
            worksheet.write(row, 3, account.followers or 0)
            worksheet.write(row, 4, account.following or 0)
            worksheet.write(row, 5, account.number_of_posts or 0)
            worksheet.write(row, 6, account.account_type or '')
            worksheet.write(row, 7, 'Yes' if account.is_verified else 'No')
            worksheet.write(row, 8, account.phone_number or '')
            worksheet.write(row, 9, ', '.join(account.interests) if account.interests else '')
            worksheet.write(row, 10, ', '.join(account.locations) if account.locations else '')
            worksheet.write(row, 11, ', '.join(account.links) if account.links else '')
            worksheet.write(row, 12, ', '.join(tags_info['all_tags']))
            worksheet.write(row, 13, ', '.join(tags_info['required_tags']))
            worksheet.write(row, 14, account.collection_date.strftime('%Y-%m-%d %H:%M:%S') if account.collection_date else '')
        
        # Adjust column widths
        worksheet.set_column(0, 0, 20)  # Username
        worksheet.set_column(1, 1, 25)  # Full Name
        worksheet.set_column(2, 2, 40)  # Bio
        worksheet.set_column(3, 5, 12)  # Followers, Following, Posts
        worksheet.set_column(6, 8, 15)  # Account Type, Verified, Phone
        worksheet.set_column(9, 11, 30)  # Interests, Locations, Links
        worksheet.set_column(12, 13, 25)  # Tags
        worksheet.set_column(14, 14, 20)  # Collection Date
        
        workbook.close()
        
        # Create response
        output.seek(0)
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="campaign_{self.campaign.id}_whitelist.xlsx"'
        
        logger.info(f"Exported {len(whitelisted_accounts)} whitelisted accounts to Excel")
        return response

    def _get_whitelisted_accounts(self):
        """
        Get accounts that should be in the whitelist based on campaign tag assignments.
        Returns accounts that match required tags or have any assigned tags.
        """
        logger.info(f"Getting whitelisted accounts for campaign {self.campaign.id}")
        
        # Get all campaign tag assignments
        campaign_tags = CampaignTag.objects.filter(
            campaign=self.campaign
        ).select_related('tag').prefetch_related('tag__category')
        
        if not campaign_tags.exists():
            logger.warning(f"No campaign tags found for campaign {self.campaign.id}")
            return []
        
        # Get required and optional tags
        required_tags = [ct.tag for ct in campaign_tags if ct.is_required]
        all_tags = [ct.tag for ct in campaign_tags]
        
        logger.info(f"Found {len(required_tags)} required tags and {len(all_tags)} total tags")
        
        # Get accounts for this campaign
        accounts = Accounts.objects.filter(
            campaign_id=str(self.campaign.id)
        ).order_by('username')
        
        logger.info(f"Found {accounts.count()} accounts for campaign")
        
        whitelisted_accounts = []
        
        for account in accounts:
            # Check if account matches any tags (this would be done by tag analysis)
            # For now, we'll use a simplified approach based on existing tag assignments
            
            # Get tags that would match this account (simplified)
            matched_tags = self._get_account_matched_tags(account, all_tags)
            
            # Check if account should be whitelisted
            should_whitelist = False
            
            if required_tags:
                # If there are required tags, account must match at least one
                should_whitelist = any(tag in matched_tags for tag in required_tags)
            else:
                # If no required tags, whitelist if account matches any tag
                should_whitelist = len(matched_tags) > 0
            
            if should_whitelist:
                # Prepare tags info
                matched_tag_names = [tag.name for tag in matched_tags]
                required_matched_tags = [tag.name for tag in matched_tags if tag in required_tags]
                
                account_data = {
                    'account': account,
                    'tags_info': {
                        'all_tags': matched_tag_names,
                        'required_tags': required_matched_tags
                    }
                }
                whitelisted_accounts.append(account_data)
        
        logger.info(f"Found {len(whitelisted_accounts)} whitelisted accounts")
        return whitelisted_accounts

    def _get_account_matched_tags(self, account, tags):
        """
        Determine which tags match an account based on tag criteria.
        This is a simplified version - in production, this would use the tag analysis service.
        """
        matched_tags = []
        
        for tag in tags:
            # Simple keyword matching for demonstration
            # In production, this would use the proper tag analysis service
            if tag.tag_type == 'keyword' and tag.field == 'bio':
                if account.bio and tag.pattern.lower() in account.bio.lower():
                    matched_tags.append(tag)
            elif tag.tag_type == 'keyword' and tag.field == 'interests':
                if account.interests:
                    interests_text = ' '.join(account.interests).lower()
                    if tag.pattern.lower() in interests_text:
                        matched_tags.append(tag)
        
        return matched_tags

    def get_whitelist_stats(self):
        """Get statistics about the whitelist."""
        whitelisted_accounts = self._get_whitelisted_accounts()
        total_accounts = Accounts.objects.filter(campaign_id=str(self.campaign.id)).count()
        
        return {
            'total_accounts': total_accounts,
            'whitelisted_accounts': len(whitelisted_accounts),
            'conversion_rate': (len(whitelisted_accounts) / total_accounts * 100) if total_accounts > 0 else 0
        }
