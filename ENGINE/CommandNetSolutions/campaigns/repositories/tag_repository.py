"""
Repository for tag data access.
"""
from django.db.models import Q, Count
from campaigns.models import DynamicTag


class TagRepository:
    """
    Repository for tag data access.
    Abstracts database operations and provides caching.
    """

    def __init__(self, cache_service=None):
        """
        Initialize repository.

        Args:
            cache_service (CacheService): Optional cache service
        """
        self.cache_service = cache_service

    def get_by_id(self, tag_id):
        """
        Get tag by ID with caching.

        Args:
            tag_id (uuid): Tag ID

        Returns:
            DynamicTag: Tag instance

        Raises:
            DynamicTag.DoesNotExist: If tag not found
        """
        cache_key = f"tag:{tag_id}"

        # Try to get from cache first
        if self.cache_service:
            cached = self.cache_service.get(cache_key)
            if cached:
                return cached

        # Get from database
        tag = DynamicTag.objects.get(id=tag_id)

        # Store in cache
        if self.cache_service:
            self.cache_service.set(cache_key, tag)

        return tag

    def get_all(self, filters=None, order_by=None, with_usage=False):
        """
        Get all tags with optional filtering and ordering.

        Args:
            filters (dict): Optional filters
            order_by (str): Optional ordering field
            with_usage (bool): Whether to include usage statistics

        Returns:
            QuerySet: Tag queryset
        """
        # Start with all tags
        queryset = DynamicTag.objects.all()

        # Apply filters
        if filters:
            # Note: is_global filter removed as per requirements

            if 'tag_type' in filters:
                queryset = queryset.filter(tag_type=filters['tag_type'])

            if 'field' in filters:
                queryset = queryset.filter(field=filters['field'])

            if 'search' in filters:
                search = filters['search']
                queryset = queryset.filter(
                    Q(name__icontains=search) |
                    Q(description__icontains=search) |
                    Q(pattern__icontains=search)
                )

            if 'campaign_id' in filters:
                # Get tags for specific campaign (simplified - no global filtering)
                campaign_id = filters['campaign_id']
                queryset = queryset.filter(
                    Q(campaigns__campaign__id=campaign_id)
                ).distinct()

        # Apply ordering
        if order_by:
            if order_by.startswith('-'):
                # Descending order
                field = order_by[1:]
                queryset = queryset.order_by(f"-{field}")
            else:
                # Ascending order
                queryset = queryset.order_by(order_by)
        else:
            # Default ordering
            queryset = queryset.order_by('name')

        # Add usage statistics if requested
        if with_usage:
            queryset = queryset.annotate(
                campaign_count=Count('campaigns', distinct=True)
            )

        return queryset

    def save(self, tag):
        """
        Save tag and update cache.

        Args:
            tag (DynamicTag): Tag instance

        Returns:
            DynamicTag: Saved tag instance
        """
        tag.save()

        # Update cache
        if self.cache_service:
            cache_key = f"tag:{tag.id}"
            self.cache_service.set(cache_key, tag)

        return tag

    def delete(self, tag_id):
        """
        Delete tag and clear cache.

        Args:
            tag_id (uuid): Tag ID

        Raises:
            DynamicTag.DoesNotExist: If tag not found
        """
        # Get tag
        tag = DynamicTag.objects.get(id=tag_id)

        # Delete tag
        tag.delete()

        # Clear cache
        if self.cache_service:
            cache_key = f"tag:{tag_id}"
            self.cache_service.delete(cache_key)
