"""
Redis Service for workflow execution management and caching.

This service provides:
1. Connection management with health checks
2. Workflow execution tracking
3. Resource locking for concurrent workflows
4. Caching for campaign data
5. Session management for workflow progress
"""
import json
import logging
import redis
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class RedisConnectionError(Exception):
    """Custom exception for Redis connection errors."""
    pass


class RedisService:
    """
    Service for managing Redis connections and workflow-related operations.
    """
    
    def __init__(self):
        """Initialize the Redis service with connection pooling."""
        self._connection_pool = None
        self._client = None
        self._initialize_connection()
    
    def _initialize_connection(self):
        """Initialize Redis connection with proper error handling."""
        try:
            # Create connection pool
            self._connection_pool = redis.ConnectionPool(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                **settings.REDIS_CONNECTION_POOL_KWARGS
            )
            
            # Create Redis client
            self._client = redis.Redis(connection_pool=self._connection_pool)
            
            # Test connection
            self._client.ping()
            logger.info("Redis connection initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis connection: {str(e)}")
            raise RedisConnectionError(f"Redis connection failed: {str(e)}")
    
    def get_client(self) -> redis.Redis:
        """
        Get Redis client with health check.
        
        Returns:
            redis.Redis: Redis client instance
            
        Raises:
            RedisConnectionError: If connection is not available
        """
        try:
            # Test connection
            self._client.ping()
            return self._client
        except Exception as e:
            logger.warning(f"Redis connection lost, attempting to reconnect: {str(e)}")
            try:
                self._initialize_connection()
                return self._client
            except Exception as reconnect_error:
                logger.error(f"Failed to reconnect to Redis: {str(reconnect_error)}")
                raise RedisConnectionError(f"Redis reconnection failed: {str(reconnect_error)}")
    
    @contextmanager
    def get_lock(self, lock_name: str, timeout: int = 300, blocking_timeout: int = 10):
        """
        Context manager for Redis distributed locks.
        
        Args:
            lock_name (str): Name of the lock
            timeout (int): Lock timeout in seconds
            blocking_timeout (int): Time to wait for lock acquisition
            
        Yields:
            redis.lock.Lock: Redis lock object
        """
        client = self.get_client()
        lock = client.lock(
            f"lock:{lock_name}",
            timeout=timeout,
            blocking_timeout=blocking_timeout
        )
        
        try:
            acquired = lock.acquire()
            if not acquired:
                raise RedisConnectionError(f"Failed to acquire lock: {lock_name}")
            
            logger.debug(f"Acquired lock: {lock_name}")
            yield lock
            
        finally:
            try:
                lock.release()
                logger.debug(f"Released lock: {lock_name}")
            except Exception as e:
                logger.warning(f"Failed to release lock {lock_name}: {str(e)}")
    
    def set_workflow_status(self, workflow_id: str, status: Dict[str, Any], ttl: int = 3600):
        """
        Set workflow execution status in Redis.
        
        Args:
            workflow_id (str): Workflow execution ID
            status (dict): Status information
            ttl (int): Time to live in seconds
        """
        try:
            client = self.get_client()
            key = f"workflow:status:{workflow_id}"
            
            status_data = {
                **status,
                'updated_at': timezone.now().isoformat(),
                'ttl': ttl
            }
            
            client.setex(key, ttl, json.dumps(status_data))
            logger.debug(f"Set workflow status for {workflow_id}")
            
        except Exception as e:
            logger.error(f"Failed to set workflow status for {workflow_id}: {str(e)}")
            raise RedisConnectionError(f"Failed to set workflow status: {str(e)}")
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """
        Get workflow execution status from Redis.
        
        Args:
            workflow_id (str): Workflow execution ID
            
        Returns:
            dict: Status information or None if not found
        """
        try:
            client = self.get_client()
            key = f"workflow:status:{workflow_id}"
            
            status_data = client.get(key)
            if status_data:
                return json.loads(status_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get workflow status for {workflow_id}: {str(e)}")
            return None
    
    def delete_workflow_status(self, workflow_id: str):
        """
        Delete workflow execution status from Redis.
        
        Args:
            workflow_id (str): Workflow execution ID
        """
        try:
            client = self.get_client()
            key = f"workflow:status:{workflow_id}"
            client.delete(key)
            logger.debug(f"Deleted workflow status for {workflow_id}")
            
        except Exception as e:
            logger.error(f"Failed to delete workflow status for {workflow_id}: {str(e)}")
    
    def get_active_workflows(self, campaign_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get list of active workflows.
        
        Args:
            campaign_id (str, optional): Filter by campaign ID
            
        Returns:
            list: List of active workflow information
        """
        try:
            client = self.get_client()
            pattern = "workflow:status:*"
            
            active_workflows = []
            
            for key in client.scan_iter(match=pattern):
                try:
                    status_data = client.get(key)
                    if status_data:
                        workflow_status = json.loads(status_data)
                        
                        # Filter by campaign if specified
                        if campaign_id and workflow_status.get('campaign_id') != campaign_id:
                            continue
                        
                        # Only include running workflows
                        if workflow_status.get('status') == 'running':
                            active_workflows.append(workflow_status)
                            
                except Exception as e:
                    logger.warning(f"Failed to parse workflow status from key {key}: {str(e)}")
                    continue
            
            return active_workflows
            
        except Exception as e:
            logger.error(f"Failed to get active workflows: {str(e)}")
            return []
    
    def set_campaign_cache(self, campaign_id: str, data: Dict[str, Any], ttl: int = 300):
        """
        Cache campaign data in Redis.
        
        Args:
            campaign_id (str): Campaign ID
            data (dict): Data to cache
            ttl (int): Time to live in seconds
        """
        try:
            cache_key = f"campaign:{campaign_id}"
            cache.set(cache_key, data, timeout=ttl)
            logger.debug(f"Cached campaign data for {campaign_id}")
            
        except Exception as e:
            logger.error(f"Failed to cache campaign data for {campaign_id}: {str(e)}")
    
    def get_campaign_cache(self, campaign_id: str) -> Optional[Dict[str, Any]]:
        """
        Get cached campaign data from Redis.
        
        Args:
            campaign_id (str): Campaign ID
            
        Returns:
            dict: Cached data or None if not found
        """
        try:
            cache_key = f"campaign:{campaign_id}"
            return cache.get(cache_key)
            
        except Exception as e:
            logger.error(f"Failed to get cached campaign data for {campaign_id}: {str(e)}")
            return None
    
    def delete_campaign_cache(self, campaign_id: str):
        """
        Delete cached campaign data from Redis.
        
        Args:
            campaign_id (str): Campaign ID
        """
        try:
            cache_key = f"campaign:{campaign_id}"
            cache.delete(cache_key)
            logger.debug(f"Deleted campaign cache for {campaign_id}")
            
        except Exception as e:
            logger.error(f"Failed to delete campaign cache for {campaign_id}: {str(e)}")
    
    def increment_counter(self, counter_name: str, amount: int = 1, ttl: int = 86400) -> int:
        """
        Increment a counter in Redis.
        
        Args:
            counter_name (str): Name of the counter
            amount (int): Amount to increment
            ttl (int): Time to live in seconds
            
        Returns:
            int: New counter value
        """
        try:
            client = self.get_client()
            key = f"counter:{counter_name}"
            
            # Use pipeline for atomic operations
            pipe = client.pipeline()
            pipe.incrby(key, amount)
            pipe.expire(key, ttl)
            results = pipe.execute()
            
            new_value = results[0]
            logger.debug(f"Incremented counter {counter_name} to {new_value}")
            return new_value
            
        except Exception as e:
            logger.error(f"Failed to increment counter {counter_name}: {str(e)}")
            raise RedisConnectionError(f"Failed to increment counter: {str(e)}")
    
    def get_counter(self, counter_name: str) -> int:
        """
        Get counter value from Redis.
        
        Args:
            counter_name (str): Name of the counter
            
        Returns:
            int: Counter value (0 if not found)
        """
        try:
            client = self.get_client()
            key = f"counter:{counter_name}"
            
            value = client.get(key)
            return int(value) if value else 0
            
        except Exception as e:
            logger.error(f"Failed to get counter {counter_name}: {str(e)}")
            return 0
    
    def cleanup_expired_data(self):
        """
        Clean up expired workflow data and counters.
        """
        try:
            client = self.get_client()
            
            # Clean up expired workflow statuses
            workflow_pattern = "workflow:status:*"
            expired_count = 0
            
            for key in client.scan_iter(match=workflow_pattern):
                try:
                    ttl = client.ttl(key)
                    if ttl == -1:  # No expiration set
                        # Check if workflow is old
                        status_data = client.get(key)
                        if status_data:
                            workflow_status = json.loads(status_data)
                            updated_at = datetime.fromisoformat(workflow_status.get('updated_at', ''))
                            
                            # Delete if older than 24 hours
                            if timezone.now() - updated_at > timedelta(hours=24):
                                client.delete(key)
                                expired_count += 1
                                
                except Exception as e:
                    logger.warning(f"Failed to check expiration for key {key}: {str(e)}")
                    continue
            
            if expired_count > 0:
                logger.info(f"Cleaned up {expired_count} expired workflow statuses")
                
        except Exception as e:
            logger.error(f"Failed to cleanup expired data: {str(e)}")
    
    def get_connection_info(self) -> Dict[str, Any]:
        """
        Get Redis connection information.
        
        Returns:
            dict: Connection information
        """
        try:
            client = self.get_client()
            info = client.info()
            
            return {
                'host': settings.REDIS_HOST,
                'port': settings.REDIS_PORT,
                'db': settings.REDIS_DB,
                'connected_clients': info.get('connected_clients', 0),
                'used_memory_human': info.get('used_memory_human', 'unknown'),
                'redis_version': info.get('redis_version', 'unknown'),
                'uptime_in_seconds': info.get('uptime_in_seconds', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
            }
            
        except Exception as e:
            logger.error(f"Failed to get Redis connection info: {str(e)}")
            return {
                'error': str(e),
                'host': settings.REDIS_HOST,
                'port': settings.REDIS_PORT,
                'db': settings.REDIS_DB,
            }
