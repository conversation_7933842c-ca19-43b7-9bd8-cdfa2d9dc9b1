"""
Base test classes and utilities for the campaigns app test suite.

This module provides common functionality for all test cases including:
- Base test classes with common setup/teardown
- Mock configurations for external services
- Test data utilities
- Common assertions and helpers
"""

import uuid
from unittest.mock import patch, MagicMock
from django.test import TestCase, TransactionTestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from django.db import transaction
from django.conf import settings

from campaigns.models import (
    Campaign, DynamicTag, TagGroup, TagCategory, CampaignTag,
    LocationTarget, UsernameTarget, WorkflowExecution
)


class BaseTestCase(TestCase):
    """
    Base test case class with common setup and utilities.

    Provides:
    - Test user creation
    - Common test data setup
    - Mock configurations for external services
    - Helper methods for assertions
    """

    @classmethod
    def setUpClass(cls):
        """Set up class-level test data."""
        super().setUpClass()

        # Note: External service mocking is available via MockExternalServices context manager
        # when campaigns.utils module is implemented

    @classmethod
    def tearDownClass(cls):
        """Clean up class-level mocks."""
        super().tearDownClass()

    def setUp(self):
        """Set up test data for each test method."""
        super().setUp()

        # Create test client
        self.client = Client()

        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword123',
            first_name='Test',
            last_name='User'
        )

        # Create admin user
        self.admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpassword123'
        )

        # Login test user by default
        self.client.login(username='testuser', password='testpassword123')

        # Create test tag category
        self.tag_category = TagCategory.objects.create(
            name='Test Category',
            description='Test category for testing',
            color='#007bff',
            priority=1
        )

        # Create test tag group
        self.tag_group = TagGroup.objects.create(
            name='Test Group',
            description='Test group for testing',
            color='#28a745',
            is_global=True,
            creator=self.user
        )

        # Create test dynamic tag
        self.dynamic_tag = DynamicTag.objects.create(
            name='Test Tag',
            description='Test tag for testing',
            category=self.tag_category,
            tag_type='keyword',
            pattern='test',
            field='bio',
            is_global=True,
            weight=1.0
        )
        self.dynamic_tag.tag_groups.add(self.tag_group)

        # Create test campaign
        self.campaign = Campaign.objects.create(
            name='Test Campaign',
            description='Test campaign for testing',
            status='draft',
            target_type='location',
            audience_type='followers',
            creator=self.user
        )

    def tearDown(self):
        """Clean up after each test method."""
        # Clear any cached data
        if hasattr(self, '_cached_data'):
            delattr(self, '_cached_data')
        super().tearDown()

    def create_test_campaign(self, **kwargs):
        """Helper method to create a test campaign with default values."""
        defaults = {
            'name': f'Test Campaign {uuid.uuid4().hex[:8]}',
            'description': 'Test campaign description',
            'status': 'draft',
            'target_type': 'location',
            'audience_type': 'followers',
            'creator': self.user
        }
        defaults.update(kwargs)
        return Campaign.objects.create(**defaults)

    def create_test_tag(self, **kwargs):
        """Helper method to create a test dynamic tag with default values."""
        defaults = {
            'name': f'Test Tag {uuid.uuid4().hex[:8]}',
            'description': 'Test tag description',
            'category': self.tag_category,
            'tag_type': 'keyword',
            'pattern': 'test',
            'field': 'bio',
            'is_global': True,
            'weight': 1.0
        }
        defaults.update(kwargs)
        tag = DynamicTag.objects.create(**defaults)
        if 'tag_groups' not in kwargs:
            tag.tag_groups.add(self.tag_group)
        return tag

    def create_test_tag_group(self, **kwargs):
        """Helper method to create a test tag group with default values."""
        defaults = {
            'name': f'Test Group {uuid.uuid4().hex[:8]}',
            'description': 'Test group description',
            'color': '#007bff',
            'is_global': True,
            'creator': self.user
        }
        defaults.update(kwargs)
        return TagGroup.objects.create(**defaults)

    def assertModelFieldsEqual(self, model_instance, expected_values):
        """Assert that model instance fields match expected values."""
        for field_name, expected_value in expected_values.items():
            actual_value = getattr(model_instance, field_name)
            self.assertEqual(
                actual_value,
                expected_value,
                f"Field '{field_name}' expected {expected_value}, got {actual_value}"
            )

    def assertResponseContainsText(self, response, text, msg=None):
        """Assert that response contains specific text."""
        content = response.content.decode('utf-8')
        self.assertIn(text, content, msg or f"Response does not contain '{text}'")

    def assertResponseNotContainsText(self, response, text, msg=None):
        """Assert that response does not contain specific text."""
        content = response.content.decode('utf-8')
        self.assertNotIn(text, content, msg or f"Response contains '{text}'")


class BaseTransactionTestCase(TransactionTestCase):
    """
    Base transaction test case for tests that require database transactions.

    Use this for tests that involve:
    - Database transactions and rollbacks
    - Concurrent access testing
    - External service integration with real database commits
    """

    def setUp(self):
        """Set up test data for transaction tests."""
        super().setUp()

        # Create test user
        self.user = User.objects.create_user(
            username='transactionuser',
            email='<EMAIL>',
            password='testpassword123'
        )

        # Create test client
        self.client = Client()
        self.client.login(username='transactionuser', password='testpassword123')

    def create_test_workflow_execution(self, campaign=None, **kwargs):
        """Helper method to create a test workflow execution."""
        if campaign is None:
            campaign = Campaign.objects.create(
                name='Test Campaign for Workflow',
                creator=self.user
            )

        defaults = {
            'campaign': campaign,
            'workflow_type': 'account_collection',
            'status': 'pending',
            'started_at': timezone.now(),
            'airflow_dag_id': 'test_dag',
            'airflow_run_id': f'test_run_{uuid.uuid4().hex[:8]}'
        }
        defaults.update(kwargs)
        return WorkflowExecution.objects.create(**defaults)


class MockExternalServices:
    """
    Context manager for mocking external services in tests.

    Usage:
        with MockExternalServices() as mocks:
            # Test code here
            mocks.redis.ping.assert_called_once()
    """

    def __init__(self):
        self.redis_patcher = None
        self.airflow_patcher = None
        self.redis = None
        self.airflow = None

    def __enter__(self):
        # Mock Redis (when utils module exists)
        try:
            self.redis_patcher = patch('campaigns.utils.redis_client')
            self.redis = self.redis_patcher.start()
            self.redis.ping.return_value = True
            self.redis.get.return_value = None
            self.redis.set.return_value = True
        except ImportError:
            # Utils module doesn't exist yet, create mock object
            self.redis = MagicMock()
            self.redis.ping.return_value = True
            self.redis.get.return_value = None
            self.redis.set.return_value = True

        # Mock Airflow (when utils module exists)
        try:
            self.airflow_patcher = patch('campaigns.utils.airflow_client')
            self.airflow = self.airflow_patcher.start()
            self.airflow.trigger_dag.return_value = {'dag_run_id': 'test_run_123'}
            self.airflow.get_dag_run.return_value = {'state': 'running'}
        except ImportError:
            # Utils module doesn't exist yet, create mock object
            self.airflow = MagicMock()
            self.airflow.trigger_dag.return_value = {'dag_run_id': 'test_run_123'}
            self.airflow.get_dag_run.return_value = {'state': 'running'}

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.redis_patcher:
            self.redis_patcher.stop()
        if self.airflow_patcher:
            self.airflow_patcher.stop()


def skip_if_no_external_services(test_func):
    """
    Decorator to skip tests if external services are not available.

    Usage:
        @skip_if_no_external_services
        def test_redis_integration(self):
            # Test code that requires Redis
    """
    def wrapper(*args, **kwargs):
        try:
            # Try to connect to Redis
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
        except:
            import unittest
            raise unittest.SkipTest("External services not available")

        return test_func(*args, **kwargs)

    return wrapper
