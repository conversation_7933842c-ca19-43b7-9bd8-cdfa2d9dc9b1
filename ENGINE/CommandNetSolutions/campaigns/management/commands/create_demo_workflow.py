"""
Management command to create a demo workflow for testing.
"""
from django.core.management.base import BaseCommand
from campaigns.models import Campaign
from campaigns.models.cep import CEPWorkflow
from campaigns.models.workflow import WorkflowTemplate


class Command(BaseCommand):
    help = 'Create a demo workflow for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--campaign-name',
            type=str,
            default='Test Workflow Campaign',
            help='Name of the campaign to use for the demo workflow'
        )

    def handle(self, *args, **options):
        campaign_name = options['campaign_name']
        
        try:
            # Find a completed campaign
            campaign = Campaign.objects.filter(
                name__icontains=campaign_name,
                status__in=['completed', 'stopped']
            ).first()
            
            if not campaign:
                # Try to find any completed campaign
                campaign = Campaign.objects.filter(
                    status__in=['completed', 'stopped']
                ).first()
                
                if not campaign:
                    self.stdout.write(
                        self.style.ERROR('No completed campaigns found. Please complete a campaign first.')
                    )
                    return
            
            # Get a workflow template
            template = WorkflowTemplate.objects.filter(
                name='Conservative Outreach',
                is_active=True
            ).first()
            
            if not template:
                template = WorkflowTemplate.objects.filter(is_active=True).first()
                
            if not template:
                self.stdout.write(
                    self.style.ERROR('No workflow templates found. Please run setup_workflow_templates first.')
                )
                return
            
            # Check if workflow already exists for this campaign
            existing_workflow = CEPWorkflow.objects.filter(campaign=campaign).first()
            if existing_workflow:
                self.stdout.write(
                    self.style.WARNING(f'Workflow already exists for campaign {campaign.name}: {existing_workflow.name}')
                )
                return
            
            # Create demo workflow
            workflow = CEPWorkflow.objects.create(
                campaign=campaign,
                name=f'Demo Workflow - {template.name}',
                description=f'Demo workflow using {template.name} template for testing the workflow system.',
                selected_workflows=['follow', 'like'],
                daily_limit=template.default_daily_limit,
                delay_between_actions=template.default_delay_between_actions,
                status='pending',
                whitelist_count=25  # Mock whitelist count
            )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Created demo workflow: {workflow.name} for campaign: {campaign.name}'
                )
            )
            self.stdout.write(
                self.style.SUCCESS(
                    f'Workflow ID: {workflow.id}'
                )
            )
            self.stdout.write(
                self.style.SUCCESS(
                    f'You can view it at: /campaigns/workflows/{workflow.id}/'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating demo workflow: {str(e)}')
            )
