{% extends 'campaigns/base.html' %}
{% load static %}

{% block title %}Create CEP Workflow{% endblock %}

{% block extra_css %}
<style>
    .cep-wizard-container {
        max-width: 900px;
        margin: 0 auto;
    }
    .wizard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 8px 8px 0 0;
        padding: 25px;
    }
    .wizard-body {
        background-color: #fff;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 30px;
    }
    .step-indicator {
        display: flex;
        margin-bottom: 30px;
        justify-content: center;
    }
    .step {
        flex: 1;
        text-align: center;
        padding: 15px;
        position: relative;
        max-width: 200px;
    }
    .step.active {
        color: #667eea;
        font-weight: 600;
    }
    .step.completed {
        color: #28a745;
    }
    .step:not(:last-child):after {
        content: '';
        position: absolute;
        top: 50%;
        right: -50%;
        width: 100%;
        height: 2px;
        background-color: #dee2e6;
        z-index: -1;
    }
    .step.completed:not(:last-child):after {
        background-color: #28a745;
    }
    .step-number {
        display: inline-block;
        width: 40px;
        height: 40px;
        line-height: 40px;
        border-radius: 50%;
        background-color: #f8f9fa;
        border: 2px solid #dee2e6;
        margin-bottom: 8px;
        font-weight: 600;
    }
    .step.active .step-number {
        background-color: #667eea;
        color: white;
        border-color: #667eea;
    }
    .step.completed .step-number {
        background-color: #28a745;
        color: white;
        border-color: #28a745;
    }
    .wizard-step {
        display: none;
    }
    .wizard-step.active {
        display: block;
    }
    .workflow-option {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .workflow-option:hover {
        border-color: #667eea;
        background-color: #f8f9ff;
    }
    .workflow-option.selected {
        border-color: #667eea;
        background-color: #f8f9ff;
    }
    .workflow-option input[type="checkbox"] {
        margin-right: 12px;
        transform: scale(1.2);
    }
    .workflow-description {
        color: #6c757d;
        font-size: 14px;
        margin-top: 8px;
    }
    .campaign-card {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .campaign-card:hover {
        border-color: #667eea;
        background-color: #f8f9ff;
    }
    .campaign-card.selected {
        border-color: #667eea;
        background-color: #f8f9ff;
    }
    .campaign-stats {
        display: flex;
        gap: 20px;
        margin-top: 10px;
    }
    .stat-item {
        font-size: 14px;
        color: #6c757d;
    }
    .info-box {
        background-color: #e7f3ff;
        border-left: 4px solid #0d6efd;
        padding: 15px;
        margin: 20px 0;
        border-radius: 4px;
    }
    .warning-box {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 15px;
        margin: 20px 0;
        border-radius: 4px;
    }
    .navigation-buttons {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #dee2e6;
    }
</style>
{% endblock %}

{% block campaign_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title">Create CEP Workflow</h1>
        <p class="page-subtitle">Set up a Customer Engagement Process to interact with your campaign whitelist</p>
    </div>
    <div>
        <a href="{% url 'campaigns:workflow_dashboard' %}" class="btn btn-outline-primary btn-icon back-button">
            <i class="fas fa-arrow-left me-1"></i> <span>Back to Dashboard</span>
        </a>
    </div>
</div>

<div class="cep-wizard-container">
    <div class="wizard-header">
        <div class="d-flex align-items-center">
            <i class="fas fa-magic me-3" style="font-size: 2rem;"></i>
            <div>
                <h3 class="mb-0">CEP Workflow Wizard</h3>
                <p class="mb-0 mt-2 opacity-75">Follow these steps to create your Customer Engagement Process</p>
            </div>
        </div>
    </div>

    <div class="wizard-body">
        <!-- Step Indicator -->
        <div class="step-indicator">
            <div class="step active" data-step="1">
                <div class="step-number">1</div>
                <div>Select Campaign</div>
            </div>
            <div class="step" data-step="2">
                <div class="step-number">2</div>
                <div>Choose Workflows</div>
            </div>
            <div class="step" data-step="3">
                <div class="step-number">3</div>
                <div>Configure Actions</div>
            </div>
            <div class="step" data-step="4">
                <div class="step-number">4</div>
                <div>Review & Create</div>
            </div>
        </div>

        <form method="post" id="cep-wizard-form">
            {% csrf_token %}

            <!-- Step 1: Select Campaign -->
            <div class="wizard-step active" id="step-1">
                <h4 class="mb-3">Select Campaign Whitelist</h4>
                <p class="text-muted mb-4">Choose a campaign with a generated whitelist to use for engagement actions.</p>

                <div class="info-box">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>What happens after whitelist generation?</strong>
                    <p class="mb-0 mt-2">Once your campaign has collected accounts and generated a whitelist through tag analysis, you can create a CEP (Customer Engagement Process) workflow to automatically engage with those accounts using various Instagram actions like follows, likes, comments, and direct messages.</p>
                </div>

                {% if available_campaigns %}
                <div id="campaign-selection">
                    {% for campaign in available_campaigns %}
                    <div class="campaign-card" data-campaign-id="{{ campaign.id }}">
                        <div class="d-flex align-items-start">
                            <input type="radio" name="campaign" value="{{ campaign.id }}" class="me-3 mt-1">
                            <div class="flex-grow-1">
                                <h5 class="mb-1">{{ campaign.name }}</h5>
                                <p class="text-muted mb-2">{{ campaign.description|default:"No description provided" }}</p>
                                <div class="campaign-stats">
                                    <div class="stat-item">
                                        <i class="fas fa-list me-1"></i>
                                        {{ campaign.whitelist_count }} accounts in whitelist
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-calendar me-1"></i>
                                        Created {{ campaign.created_at|date:"M d, Y" }}
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-check-circle me-1 text-success"></i>
                                        {{ campaign.status|title }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="warning-box">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>No campaigns available</strong>
                    <p class="mb-0 mt-2">You need at least one completed campaign with a generated whitelist to create a CEP workflow. Please complete a campaign first.</p>
                </div>
                {% endif %}

                <div class="navigation-buttons">
                    <div></div>
                    <button type="button" class="btn btn-primary next-step" data-next="2" {% if not available_campaigns %}disabled{% endif %}>
                        Next: Choose Workflows <i class="fas fa-arrow-right ms-1"></i>
                    </button>
                </div>
            </div>

            <!-- Step 2: Choose Workflows -->
            <div class="wizard-step" id="step-2">
                <h4 class="mb-3">Choose Instagram Workflows</h4>
                <p class="text-muted mb-4">Select the types of Instagram actions you want to perform on your whitelist accounts.</p>

                <div class="info-box">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Available Workflows</strong>
                    <p class="mb-0 mt-2">These workflows are based on the PyFlow automation files in your ENGINE/INSTA/WORKFLOW/ directory. Each workflow type performs specific Instagram actions.</p>
                </div>

                <div id="workflow-selection">
                    <div class="workflow-option" data-workflow="follow">
                        <label class="d-flex align-items-start">
                            <input type="checkbox" name="workflows" value="follow">
                            <div>
                                <strong>Follow Actions</strong>
                                <div class="workflow-description">Automatically follow accounts from your whitelist to increase your follower base and engagement.</div>
                            </div>
                        </label>
                    </div>

                    <div class="workflow-option" data-workflow="like">
                        <label class="d-flex align-items-start">
                            <input type="checkbox" name="workflows" value="like">
                            <div>
                                <strong>Post Likes</strong>
                                <div class="workflow-description">Like recent posts from whitelist accounts to increase engagement and visibility.</div>
                            </div>
                        </label>
                    </div>

                    <div class="workflow-option" data-workflow="comment">
                        <label class="d-flex align-items-start">
                            <input type="checkbox" name="workflows" value="comment">
                            <div>
                                <strong>Comments</strong>
                                <div class="workflow-description">Leave thoughtful comments on posts from whitelist accounts to build relationships.</div>
                            </div>
                        </label>
                    </div>

                    <div class="workflow-option" data-workflow="dm">
                        <label class="d-flex align-items-start">
                            <input type="checkbox" name="workflows" value="dm">
                            <div>
                                <strong>Direct Messages</strong>
                                <div class="workflow-description">Send personalized direct messages to whitelist accounts for direct outreach.</div>
                            </div>
                        </label>
                    </div>

                    <div class="workflow-option" data-workflow="discover">
                        <label class="d-flex align-items-start">
                            <input type="checkbox" name="workflows" value="discover">
                            <div>
                                <strong>Discover Actions</strong>
                                <div class="workflow-description">Explore and interact with content from whitelist accounts' networks.</div>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="navigation-buttons">
                    <button type="button" class="btn btn-outline-secondary prev-step" data-prev="1">
                        <i class="fas fa-arrow-left me-1"></i> Previous
                    </button>
                    <button type="button" class="btn btn-primary next-step" data-next="3">
                        Next: Configure Actions <i class="fas fa-arrow-right ms-1"></i>
                    </button>
                </div>
            </div>

            <!-- Step 3: Configure Actions -->
            <div class="wizard-step" id="step-3">
                <h4 class="mb-3">Configure Action Settings</h4>
                <p class="text-muted mb-4">Set up the details for your selected workflows and engagement parameters.</p>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="workflow_name" class="form-label">Workflow Name</label>
                            <input type="text" class="form-control" id="workflow_name" name="workflow_name" placeholder="e.g., Fitness Campaign Engagement" required>
                            <div class="form-text">Give your CEP workflow a descriptive name</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="workflow_description" class="form-label">Description (Optional)</label>
                            <textarea class="form-control" id="workflow_description" name="workflow_description" rows="3" placeholder="Brief description of this workflow's purpose"></textarea>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="daily_limit" class="form-label">Daily Action Limit</label>
                            <input type="number" class="form-control" id="daily_limit" name="daily_limit" value="50" min="1" max="200">
                            <div class="form-text">Maximum number of actions per day (recommended: 50-100)</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="delay_between_actions" class="form-label">Delay Between Actions (seconds)</label>
                            <input type="number" class="form-control" id="delay_between_actions" name="delay_between_actions" value="30" min="10" max="300">
                            <div class="form-text">Time to wait between actions (recommended: 30-60 seconds)</div>
                        </div>
                    </div>
                </div>

                <div id="workflow-specific-settings">
                    <!-- Dynamic settings based on selected workflows -->
                </div>

                <div class="navigation-buttons">
                    <button type="button" class="btn btn-outline-secondary prev-step" data-prev="2">
                        <i class="fas fa-arrow-left me-1"></i> Previous
                    </button>
                    <button type="button" class="btn btn-primary next-step" data-next="4">
                        Next: Review & Create <i class="fas fa-arrow-right ms-1"></i>
                    </button>
                </div>
            </div>

            <!-- Step 4: Review & Create -->
            <div class="wizard-step" id="step-4">
                <h4 class="mb-3">Review & Create Workflow</h4>
                <p class="text-muted mb-4">Review your CEP workflow configuration before creating it.</p>

                <div class="row">
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Workflow Summary</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">Campaign:</div>
                                    <div class="col-md-8" id="review-campaign">-</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">Workflow Name:</div>
                                    <div class="col-md-8" id="review-name">-</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">Selected Actions:</div>
                                    <div class="col-md-8" id="review-workflows">-</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">Daily Limit:</div>
                                    <div class="col-md-8" id="review-daily-limit">-</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">Action Delay:</div>
                                    <div class="col-md-8" id="review-delay">-</div>
                                </div>
                            </div>
                        </div>

                        <div class="info-box">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>CEP Workflow Execution</strong>
                            <p class="mb-2 mt-2">Once created, your CEP workflow will:</p>
                            <ul class="mb-0">
                                <li>Process accounts from your campaign whitelist sequentially</li>
                                <li>Execute the selected Instagram actions with proper delays</li>
                                <li>Respect daily limits to maintain account safety</li>
                                <li>Provide real-time progress tracking and analytics</li>
                                <li>Allow you to pause, resume, or stop the workflow at any time</li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    Safety Features
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <small>Rate limiting protection</small>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <small>Human-like delays</small>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <small>Daily action limits</small>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <small>Error handling & recovery</small>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <small>Real-time monitoring</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="navigation-buttons">
                    <button type="button" class="btn btn-outline-secondary prev-step" data-prev="3">
                        <i class="fas fa-arrow-left me-1"></i> Previous
                    </button>
                    <div>
                        <a href="{% url 'campaigns:workflow_dashboard' %}" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-magic me-1"></i> Create CEP Workflow
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 1;
    const totalSteps = 4;

    // Campaign selection
    document.querySelectorAll('.campaign-card').forEach(card => {
        card.addEventListener('click', function() {
            // Remove selection from all cards
            document.querySelectorAll('.campaign-card').forEach(c => c.classList.remove('selected'));
            // Add selection to clicked card
            this.classList.add('selected');
            // Check the radio button
            this.querySelector('input[type="radio"]').checked = true;
        });
    });

    // Workflow selection
    document.querySelectorAll('.workflow-option').forEach(option => {
        option.addEventListener('click', function(e) {
            if (e.target.type !== 'checkbox') {
                const checkbox = this.querySelector('input[type="checkbox"]');
                checkbox.checked = !checkbox.checked;
            }
            this.classList.toggle('selected', this.querySelector('input[type="checkbox"]').checked);
        });
    });

    // Step navigation
    document.querySelectorAll('.next-step').forEach(button => {
        button.addEventListener('click', function() {
            const nextStep = parseInt(this.getAttribute('data-next'));
            if (validateStep(currentStep)) {
                goToStep(nextStep);
            }
        });
    });

    document.querySelectorAll('.prev-step').forEach(button => {
        button.addEventListener('click', function() {
            const prevStep = parseInt(this.getAttribute('data-prev'));
            goToStep(prevStep);
        });
    });

    function validateStep(step) {
        switch(step) {
            case 1:
                const selectedCampaign = document.querySelector('input[name="campaign"]:checked');
                if (!selectedCampaign) {
                    alert('Please select a campaign to continue.');
                    return false;
                }
                break;
            case 2:
                const selectedWorkflows = document.querySelectorAll('input[name="workflows"]:checked');
                if (selectedWorkflows.length === 0) {
                    alert('Please select at least one workflow to continue.');
                    return false;
                }
                break;
            case 3:
                const workflowName = document.getElementById('workflow_name').value.trim();
                if (!workflowName) {
                    alert('Please enter a workflow name to continue.');
                    return false;
                }
                const dailyLimit = parseInt(document.getElementById('daily_limit').value);
                if (dailyLimit < 1 || dailyLimit > 200) {
                    alert('Daily limit must be between 1 and 200.');
                    return false;
                }
                break;
        }
        return true;
    }

    function goToStep(step) {
        // Hide current step
        document.getElementById(`step-${currentStep}`).classList.remove('active');
        document.querySelector(`.step[data-step="${currentStep}"]`).classList.remove('active');
        document.querySelector(`.step[data-step="${currentStep}"]`).classList.add('completed');

        // Show new step
        document.getElementById(`step-${step}`).classList.add('active');
        document.querySelector(`.step[data-step="${step}"]`).classList.add('active');

        // Update review if going to step 4
        if (step === 4) {
            updateReview();
        }

        currentStep = step;
    }

    function updateReview() {
        // Update campaign
        const selectedCampaign = document.querySelector('input[name="campaign"]:checked');
        if (selectedCampaign) {
            const campaignCard = selectedCampaign.closest('.campaign-card');
            const campaignName = campaignCard.querySelector('h5').textContent;
            document.getElementById('review-campaign').textContent = campaignName;
        }

        // Update workflow name
        const workflowName = document.getElementById('workflow_name').value || '-';
        document.getElementById('review-name').textContent = workflowName;

        // Update selected workflows
        const selectedWorkflows = document.querySelectorAll('input[name="workflows"]:checked');
        const workflowNames = Array.from(selectedWorkflows).map(w => {
            const label = w.closest('.workflow-option').querySelector('strong').textContent;
            return label;
        });
        document.getElementById('review-workflows').textContent = workflowNames.join(', ') || '-';

        // Update daily limit
        const dailyLimit = document.getElementById('daily_limit').value;
        document.getElementById('review-daily-limit').textContent = dailyLimit + ' actions per day';

        // Update delay
        const delay = document.getElementById('delay_between_actions').value;
        document.getElementById('review-delay').textContent = delay + ' seconds';
    }
});
</script>
{% endblock %}
