"""
Forms for campaign analysis settings.
"""
from django import forms
from django.utils.translation import gettext_lazy as _
from campaigns.models import CampaignAnalysisSettings, DynamicTag, CampaignAnalysisTag


class CampaignAnalysisSettingsForm(forms.ModelForm):
    """
    Form for creating and editing campaign analysis settings.
    """
    # Add fields for tag selection
    tags = forms.ModelMultipleChoiceField(
        queryset=DynamicTag.objects.all(),
        required=False,
        label=_("Analysis Tags"),
        help_text=_("Select tags to use for analysis"),
        widget=forms.SelectMultiple(attrs={
            'class': 'form-control tag-select',
            'data-placeholder': 'Select tags...'
        })
    )

    # Add fields for required tags
    required_tags = forms.ModelMultipleChoiceField(
        queryset=DynamicTag.objects.all(),
        required=False,
        label=_("Required Tags"),
        help_text=_("Accounts must match these tags to be included in the whitelist"),
        widget=forms.SelectMultiple(attrs={
            'class': 'form-control required-tag-select',
            'data-placeholder': 'Select required tags...'
        })
    )

    class Meta:
        model = CampaignAnalysisSettings
        fields = [
            'auto_analyze', 'analysis_frequency', 'analysis_mode',
            'min_followers', 'max_followers', 'enable_tagging'
        ]
        widgets = {
            'min_followers': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'placeholder': 'No minimum'
            }),
            'max_followers': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'placeholder': 'No maximum'
            }),
        }
        help_texts = {
            'auto_analyze': _("Automatically analyze accounts after collection"),
            'analysis_frequency': _("How often to analyze accounts"),
            'analysis_mode': _("Determines the depth and methods used for analysis"),
            'min_followers': _("Minimum number of followers for analysis (leave blank for no minimum)"),
            'max_followers': _("Maximum number of followers for analysis (leave blank for no maximum)"),
            'enable_tagging': _("Enable automatic tagging of accounts"),
        }

    def __init__(self, *args, **kwargs):
        """
        Initialize the form.
        """
        self.campaign = kwargs.pop('campaign', None)
        super().__init__(*args, **kwargs)

        # If we have an instance, populate the tags field
        if self.instance and self.instance.pk:
            # Get all tags associated with this analysis settings
            analysis_tags = CampaignAnalysisTag.objects.filter(
                analysis_settings=self.instance
            )

            # Set initial values for tags field
            self.fields['tags'].initial = [
                tag.tag.id for tag in analysis_tags
            ]

            # Set initial values for required tags field
            self.fields['required_tags'].initial = [
                tag.tag.id for tag in analysis_tags if tag.is_required
            ]

        # Filter tags to show all available tags (simplified - no global filtering)
        if self.campaign:
            self.fields['tags'].queryset = DynamicTag.objects.all().distinct()
            self.fields['required_tags'].queryset = self.fields['tags'].queryset

    def save(self, commit=True):
        """
        Save the form.
        """
        # Set the campaign if it's not already set
        if self.campaign and not self.instance.campaign_id:
            self.instance.campaign = self.campaign

        # Save the analysis settings
        instance = super().save(commit)

        if commit:
            # Get the selected tags
            selected_tags = self.cleaned_data.get('tags', [])
            required_tags = self.cleaned_data.get('required_tags', [])

            # Delete existing analysis tags
            CampaignAnalysisTag.objects.filter(analysis_settings=instance).delete()

            # Create new analysis tags
            for tag in selected_tags:
                CampaignAnalysisTag.objects.create(
                    analysis_settings=instance,
                    tag=tag,
                    is_required=tag in required_tags
                )

        return instance
