"""
Health Check Service for monitoring external service connections.

This service provides comprehensive health checks for:
1. Database connections
2. Redis connections
3. Airflow API connectivity
4. PyFlow service availability
5. File system access
"""
import logging
import time
import redis
import psycopg2
from datetime import datetime, timedelta
from django.conf import settings
from django.db import connection
from django.core.cache import cache
from django.utils import timezone
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class HealthCheckService:
    """
    Service for performing health checks on external dependencies.
    """
    
    def __init__(self):
        """Initialize the health check service."""
        self.checks = {
            'database': self._check_database,
            'redis': self._check_redis,
            'airflow': self._check_airflow,
            'pyflow': self._check_pyflow,
            'filesystem': self._check_filesystem,
        }
    
    def check_all_services(self) -> Dict[str, Any]:
        """
        Perform health checks on all services.
        
        Returns:
            dict: Health check results for all services
        """
        results = {
            'timestamp': timezone.now().isoformat(),
            'overall_status': 'healthy',
            'services': {}
        }
        
        for service_name, check_func in self.checks.items():
            try:
                start_time = time.time()
                service_result = check_func()
                end_time = time.time()
                
                service_result['response_time_ms'] = round((end_time - start_time) * 1000, 2)
                results['services'][service_name] = service_result
                
                # Update overall status if any service is unhealthy
                if not service_result.get('healthy', False):
                    results['overall_status'] = 'unhealthy'
                    
            except Exception as e:
                logger.exception(f"Health check failed for {service_name}: {str(e)}")
                results['services'][service_name] = {
                    'healthy': False,
                    'error': str(e),
                    'response_time_ms': None
                }
                results['overall_status'] = 'unhealthy'
        
        return results
    
    def _check_database(self) -> Dict[str, Any]:
        """
        Check database connectivity and performance.
        
        Returns:
            dict: Database health check results
        """
        try:
            # Test basic connectivity
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            # Test connection pool status
            db_settings = settings.DATABASES['default']
            
            # Test a simple query
            from campaigns.models import Campaign
            campaign_count = Campaign.objects.count()
            
            return {
                'healthy': True,
                'database_name': db_settings['NAME'],
                'host': db_settings['HOST'],
                'port': db_settings['PORT'],
                'campaign_count': campaign_count,
                'connection_max_age': db_settings.get('CONN_MAX_AGE', 0),
                'message': 'Database connection successful'
            }
            
        except Exception as e:
            logger.error(f"Database health check failed: {str(e)}")
            return {
                'healthy': False,
                'error': str(e),
                'message': 'Database connection failed'
            }
    
    def _check_redis(self) -> Dict[str, Any]:
        """
        Check Redis connectivity and performance.
        
        Returns:
            dict: Redis health check results
        """
        try:
            # Test Django cache
            test_key = 'health_check_test'
            test_value = f'test_{int(time.time())}'
            
            cache.set(test_key, test_value, timeout=60)
            retrieved_value = cache.get(test_key)
            
            if retrieved_value != test_value:
                raise Exception("Cache set/get test failed")
            
            # Clean up test key
            cache.delete(test_key)
            
            # Get Redis info
            redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                socket_timeout=5
            )
            
            redis_info = redis_client.info()
            
            return {
                'healthy': True,
                'host': settings.REDIS_HOST,
                'port': settings.REDIS_PORT,
                'db': settings.REDIS_DB,
                'connected_clients': redis_info.get('connected_clients', 0),
                'used_memory_human': redis_info.get('used_memory_human', 'unknown'),
                'redis_version': redis_info.get('redis_version', 'unknown'),
                'message': 'Redis connection successful'
            }
            
        except Exception as e:
            logger.error(f"Redis health check failed: {str(e)}")
            return {
                'healthy': False,
                'error': str(e),
                'message': 'Redis connection failed'
            }
    
    def _check_airflow(self) -> Dict[str, Any]:
        """
        Check Airflow API connectivity.
        
        Returns:
            dict: Airflow health check results
        """
        try:
            from campaigns.services.airflow_service import AirflowService
            
            airflow_service = AirflowService()
            
            # Test basic API connectivity by getting DAG list
            response = airflow_service._make_request('GET', 'dags', params={'limit': 1})
            
            if not response['success']:
                raise Exception(f"Airflow API request failed: {response.get('error', 'Unknown error')}")
            
            return {
                'healthy': True,
                'api_url': settings.AIRFLOW_API_URL,
                'username': settings.AIRFLOW_USERNAME,
                'timeout': settings.AIRFLOW_API_TIMEOUT,
                'max_retries': settings.AIRFLOW_API_MAX_RETRIES,
                'message': 'Airflow API connection successful'
            }
            
        except Exception as e:
            logger.error(f"Airflow health check failed: {str(e)}")
            return {
                'healthy': False,
                'error': str(e),
                'message': 'Airflow API connection failed'
            }
    
    def _check_pyflow(self) -> Dict[str, Any]:
        """
        Check PyFlow service availability.
        
        Returns:
            dict: PyFlow health check results
        """
        try:
            import os
            from campaigns.services.pyflow_service import PyFlowService, PYFLOW_ENGINE_DIR, PYFLOW_WORKFLOW_DIR
            
            # Check if PyFlow directories exist
            if not os.path.exists(PYFLOW_ENGINE_DIR):
                raise Exception(f"PyFlow engine directory not found: {PYFLOW_ENGINE_DIR}")
            
            if not os.path.exists(PYFLOW_WORKFLOW_DIR):
                raise Exception(f"PyFlow workflow directory not found: {PYFLOW_WORKFLOW_DIR}")
            
            # Test PyFlow service initialization
            pyflow_service = PyFlowService()
            
            # List existing workflows to test service functionality
            workflows = pyflow_service.list_workflows()
            
            return {
                'healthy': True,
                'engine_dir': PYFLOW_ENGINE_DIR,
                'workflow_dir': PYFLOW_WORKFLOW_DIR,
                'workflow_count': len(workflows),
                'use_dummy': settings.USE_DUMMY_PYFLOW,
                'max_concurrent': settings.PYFLOW_MAX_CONCURRENT_WORKFLOWS,
                'timeout': settings.PYFLOW_WORKFLOW_TIMEOUT,
                'message': 'PyFlow service available'
            }
            
        except Exception as e:
            logger.error(f"PyFlow health check failed: {str(e)}")
            return {
                'healthy': False,
                'error': str(e),
                'message': 'PyFlow service unavailable'
            }
    
    def _check_filesystem(self) -> Dict[str, Any]:
        """
        Check filesystem access for critical directories.
        
        Returns:
            dict: Filesystem health check results
        """
        try:
            import os
            import tempfile
            
            # Check critical directories
            directories_to_check = [
                settings.BASE_DIR,
                os.path.join(settings.BASE_DIR, 'logs'),
                settings.MEDIA_ROOT,
                settings.STATIC_ROOT,
            ]
            
            directory_status = {}
            
            for directory in directories_to_check:
                try:
                    # Check if directory exists and is writable
                    if os.path.exists(directory):
                        # Test write access
                        test_file = os.path.join(directory, f'health_check_{int(time.time())}.tmp')
                        with open(test_file, 'w') as f:
                            f.write('health check test')
                        os.remove(test_file)
                        
                        directory_status[directory] = {
                            'exists': True,
                            'writable': True,
                            'size_mb': round(sum(os.path.getsize(os.path.join(dirpath, filename))
                                               for dirpath, dirnames, filenames in os.walk(directory)
                                               for filename in filenames) / (1024 * 1024), 2)
                        }
                    else:
                        directory_status[directory] = {
                            'exists': False,
                            'writable': False,
                            'size_mb': 0
                        }
                        
                except Exception as e:
                    directory_status[directory] = {
                        'exists': os.path.exists(directory),
                        'writable': False,
                        'error': str(e),
                        'size_mb': 0
                    }
            
            # Check if any critical directories are not accessible
            critical_issues = [
                dir_path for dir_path, status in directory_status.items()
                if not status.get('writable', False)
            ]
            
            return {
                'healthy': len(critical_issues) == 0,
                'directories': directory_status,
                'critical_issues': critical_issues,
                'message': 'Filesystem access successful' if len(critical_issues) == 0 else f'Issues with {len(critical_issues)} directories'
            }
            
        except Exception as e:
            logger.error(f"Filesystem health check failed: {str(e)}")
            return {
                'healthy': False,
                'error': str(e),
                'message': 'Filesystem check failed'
            }
    
    def check_service(self, service_name: str) -> Dict[str, Any]:
        """
        Check a specific service.
        
        Args:
            service_name (str): Name of the service to check
            
        Returns:
            dict: Health check results for the specified service
        """
        if service_name not in self.checks:
            return {
                'healthy': False,
                'error': f'Unknown service: {service_name}',
                'available_services': list(self.checks.keys())
            }
        
        try:
            start_time = time.time()
            result = self.checks[service_name]()
            end_time = time.time()
            
            result['response_time_ms'] = round((end_time - start_time) * 1000, 2)
            return result
            
        except Exception as e:
            logger.exception(f"Health check failed for {service_name}: {str(e)}")
            return {
                'healthy': False,
                'error': str(e),
                'response_time_ms': None
            }
