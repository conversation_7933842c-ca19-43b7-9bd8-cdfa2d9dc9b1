{% load static %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}Campaigns{% endblock %} - CommandNetSolutions</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'campaigns/css/campaigns.css' %}">
    <link rel="stylesheet" href="{% static 'campaigns/css/campaign_styles.css' %}">
    <link rel="stylesheet" href="{% static 'campaigns/css/button-fixes.css' %}">
    <link rel="stylesheet" href="{% static 'campaigns/css/detail-button-fixes.css' %}">
    <link rel="stylesheet" href="{% static 'campaigns/css/campaign-header-buttons.css' %}">
    <link rel="stylesheet" href="{% static 'campaigns/css/tag_groups.css' %}">
    <link rel="stylesheet" href="{% static 'campaigns/css/action-buttons-fix.css' %}">
    <link rel="stylesheet" href="{% static 'campaigns/css/export-dropdown-fix.css' %}">
    <link rel="stylesheet" href="{% static 'campaigns/css/filter-search-fix.css' %}">
    <link rel="stylesheet" href="{% static 'campaigns/css/target_type_badges.css' %}">
    <link rel="stylesheet" href="{% static 'campaigns/css/badge_override.css' %}">
    <link rel="stylesheet" href="{% static 'campaigns/css/simulation.css' %}">
    <link rel="stylesheet" href="{% static 'campaigns/css/cep_workflows.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body class="campaign-page">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'campaigns:dashboard' %}">CommandNetSolutions</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'campaigns:dashboard' %}">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'campaigns:campaign_list' %}">Campaigns</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="tagsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Tags
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="tagsDropdown">
                            <li><a class="dropdown-item" href="{% url 'campaigns:dynamic_tag_list' %}">All Tags</a></li>
                            <li><a class="dropdown-item" href="{% url 'campaigns:tag_group_list' %}">Tag Groups</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'campaigns:dynamic_tag_create' %}">Create New Tag</a></li>
                            <li><a class="dropdown-item" href="{% url 'campaigns:tag_group_create' %}">Create New Tag Group</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'campaigns:workflow_dashboard' %}">Workflows</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'campaigns:resource_manager_dashboard' %}">Resource Manager</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="campaigns-container">
        <div class="container py-4">
            {% if messages %}
            <div class="messages-container mb-4">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    <i class="fas {% if message.tags == 'success' %}fa-check-circle{% elif message.tags == 'error' %}fa-exclamation-circle{% elif message.tags == 'warning' %}fa-exclamation-triangle{% else %}fa-info-circle{% endif %} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            {% block campaign_content %}{% endblock %}
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom JS -->
    <script src="{% static 'campaigns/js/campaigns.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
