"""
Comprehensive test suite for the CommandNet Solutions campaigns app.

This package contains unit tests, integration tests, and functional tests
for all components of the campaigns application including:

- Models (Campaign, DynamicTag, TagGroup, etc.)
- Views (CRUD operations, API endpoints)
- Forms (validation, field handling)
- External service integrations (Airflow, Redis)
- Business logic and workflows

Test Organization:
- test_models.py: Model validation, relationships, constraints
- test_views.py: View functionality, permissions, responses
- test_forms.py: Form validation, field handling
- test_integrations.py: External service mocking and integration
- test_workflows.py: End-to-end workflow testing
- factories.py: Test data factories for consistent test setup
- base.py: Base test classes and utilities

Usage:
    # Run all tests
    python manage.py test campaigns.tests

    # Run specific test module
    python manage.py test campaigns.tests.test_models

    # Run with coverage
    coverage run --source='.' manage.py test campaigns.tests
    coverage report
"""

# Import test utilities for easy access
from .base import BaseTestCase, BaseTransactionTestCase
from .factories import (
    CampaignFactory, DynamicTagFactory, TagGroupFactory,
    TagCategoryFactory, UserFactory
)

__all__ = [
    'BaseTestCase',
    'BaseTransactionTestCase',
    'CampaignFactory',
    'DynamicTagFactory',
    'TagGroupFactory',
    'TagCategoryFactory',
    'UserFactory',
]