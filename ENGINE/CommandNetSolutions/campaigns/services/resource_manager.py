"""
Resource Manager Service for managing system resources and workflow execution limits.

This service provides:
1. Memory usage monitoring
2. CPU usage tracking
3. Disk space monitoring
4. Concurrent workflow management
5. Resource allocation and cleanup
"""
import os
import sys
import logging
import threading
import time
import shutil
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache

# Try to import psutil, fallback to basic monitoring if not available
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

logger = logging.getLogger(__name__)


class ResourceManager:
    """
    Service for managing system resources and workflow execution limits.
    """

    def __init__(self):
        """Initialize the resource manager."""
        self.max_memory_usage = getattr(settings, 'MAX_MEMORY_USAGE_PERCENT', 80)
        self.max_cpu_usage = getattr(settings, 'MAX_CPU_USAGE_PERCENT', 80)
        self.min_disk_space_gb = getattr(settings, 'MIN_DISK_SPACE_GB', 5)
        self.max_concurrent_workflows = getattr(settings, 'PYFLOW_MAX_CONCURRENT_WORKFLOWS', 5)

        # Resource monitoring
        self._monitoring_active = False
        self._monitoring_thread = None
        self._monitoring_interval = 30  # seconds

        # Resource usage history
        self._resource_history = []
        self._max_history_size = 100

        # Lock for thread safety
        self._lock = threading.Lock()

    def start_monitoring(self):
        """Start resource monitoring in background thread."""
        if self._monitoring_active:
            logger.warning("Resource monitoring is already active")
            return

        self._monitoring_active = True
        self._monitoring_thread = threading.Thread(
            target=self._monitor_resources,
            daemon=True
        )
        self._monitoring_thread.start()
        logger.info("Resource monitoring started")

    def stop_monitoring(self):
        """Stop resource monitoring."""
        self._monitoring_active = False
        if self._monitoring_thread:
            self._monitoring_thread.join(timeout=5)
        logger.info("Resource monitoring stopped")

    def _monitor_resources(self):
        """Monitor system resources in background."""
        while self._monitoring_active:
            try:
                resource_data = self.get_resource_usage()

                with self._lock:
                    self._resource_history.append({
                        'timestamp': timezone.now(),
                        'data': resource_data
                    })

                    # Keep only recent history
                    if len(self._resource_history) > self._max_history_size:
                        self._resource_history = self._resource_history[-self._max_history_size:]

                # Cache current resource data
                cache.set('system_resources', resource_data, timeout=60)

                # Check for resource alerts
                self._check_resource_alerts(resource_data)

                time.sleep(self._monitoring_interval)

            except Exception as e:
                logger.exception(f"Error in resource monitoring: {str(e)}")
                time.sleep(self._monitoring_interval)

    def get_resource_usage(self) -> Dict[str, Any]:
        """
        Get current system resource usage.

        Returns:
            dict: Resource usage information
        """
        try:
            if PSUTIL_AVAILABLE:
                # Memory usage
                memory = psutil.virtual_memory()
                memory_usage = {
                    'total_gb': round(memory.total / (1024**3), 2),
                    'available_gb': round(memory.available / (1024**3), 2),
                    'used_gb': round(memory.used / (1024**3), 2),
                    'percent': memory.percent
                }

                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=1)
                cpu_count = psutil.cpu_count()

                # Disk usage
                disk = psutil.disk_usage('/')
                disk_usage = {
                    'total_gb': round(disk.total / (1024**3), 2),
                    'free_gb': round(disk.free / (1024**3), 2),
                    'used_gb': round(disk.used / (1024**3), 2),
                    'percent': round((disk.used / disk.total) * 100, 2)
                }

                # Process information
                current_process = psutil.Process()
                process_info = {
                    'pid': current_process.pid,
                    'memory_mb': round(current_process.memory_info().rss / (1024**2), 2),
                    'cpu_percent': current_process.cpu_percent(),
                    'num_threads': current_process.num_threads(),
                    'open_files': len(current_process.open_files()),
                    'connections': len(current_process.net_connections())
                }

                return {
                    'timestamp': timezone.now().isoformat(),
                    'memory': memory_usage,
                    'cpu': {
                        'percent': cpu_percent,
                        'count': cpu_count
                    },
                    'disk': disk_usage,
                    'process': process_info,
                    'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None,
                    'psutil_available': True
                }
            else:
                # Fallback to basic system information
                disk_usage = shutil.disk_usage('/')
                disk_usage_info = {
                    'total_gb': round(disk_usage.total / (1024**3), 2),
                    'free_gb': round(disk_usage.free / (1024**3), 2),
                    'used_gb': round((disk_usage.total - disk_usage.free) / (1024**3), 2),
                    'percent': round(((disk_usage.total - disk_usage.free) / disk_usage.total) * 100, 2)
                }

                return {
                    'timestamp': timezone.now().isoformat(),
                    'memory': {'percent': 0, 'note': 'psutil not available'},
                    'cpu': {'percent': 0, 'count': os.cpu_count() or 1},
                    'disk': disk_usage_info,
                    'process': {'pid': os.getpid()},
                    'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None,
                    'psutil_available': False
                }

        except Exception as e:
            logger.error(f"Failed to get resource usage: {str(e)}")
            return {
                'timestamp': timezone.now().isoformat(),
                'error': str(e),
                'psutil_available': PSUTIL_AVAILABLE
            }

    def check_resources_available(self) -> Dict[str, Any]:
        """
        Check if system resources are available for new workflow execution.

        Returns:
            dict: Resource availability status
        """
        try:
            resource_data = self.get_resource_usage()

            # Check memory usage
            memory_available = resource_data.get('memory', {}).get('percent', 100) < self.max_memory_usage

            # Check CPU usage
            cpu_available = resource_data.get('cpu', {}).get('percent', 100) < self.max_cpu_usage

            # Check disk space
            disk_free_gb = resource_data.get('disk', {}).get('free_gb', 0)
            disk_available = disk_free_gb > self.min_disk_space_gb

            # Check concurrent workflows
            from campaigns.models.workflow import WorkflowExecution
            active_workflows = WorkflowExecution.objects.filter(status='running').count()
            workflows_available = active_workflows < self.max_concurrent_workflows

            # Overall availability
            all_available = memory_available and cpu_available and disk_available and workflows_available

            return {
                'available': all_available,
                'memory_available': memory_available,
                'cpu_available': cpu_available,
                'disk_available': disk_available,
                'workflows_available': workflows_available,
                'active_workflows': active_workflows,
                'max_workflows': self.max_concurrent_workflows,
                'resource_data': resource_data,
                'limits': {
                    'max_memory_percent': self.max_memory_usage,
                    'max_cpu_percent': self.max_cpu_usage,
                    'min_disk_space_gb': self.min_disk_space_gb
                }
            }

        except Exception as e:
            logger.error(f"Failed to check resource availability: {str(e)}")
            return {
                'available': False,
                'error': str(e)
            }

    def _check_resource_alerts(self, resource_data: Dict[str, Any]):
        """
        Check for resource usage alerts and log warnings.

        Args:
            resource_data (dict): Current resource usage data
        """
        try:
            # Memory alert
            memory_percent = resource_data.get('memory', {}).get('percent', 0)
            if memory_percent > self.max_memory_usage:
                logger.warning(f"High memory usage: {memory_percent}% (limit: {self.max_memory_usage}%)")

            # CPU alert
            cpu_percent = resource_data.get('cpu', {}).get('percent', 0)
            if cpu_percent > self.max_cpu_usage:
                logger.warning(f"High CPU usage: {cpu_percent}% (limit: {self.max_cpu_usage}%)")

            # Disk space alert
            disk_free_gb = resource_data.get('disk', {}).get('free_gb', 0)
            if disk_free_gb < self.min_disk_space_gb:
                logger.warning(f"Low disk space: {disk_free_gb}GB (minimum: {self.min_disk_space_gb}GB)")

        except Exception as e:
            logger.error(f"Failed to check resource alerts: {str(e)}")

    def get_resource_history(self, hours: int = 1) -> List[Dict[str, Any]]:
        """
        Get resource usage history for the specified time period.

        Args:
            hours (int): Number of hours of history to return

        Returns:
            list: Resource usage history
        """
        try:
            cutoff_time = timezone.now() - timedelta(hours=hours)

            with self._lock:
                filtered_history = [
                    entry for entry in self._resource_history
                    if entry['timestamp'] > cutoff_time
                ]

            return filtered_history

        except Exception as e:
            logger.error(f"Failed to get resource history: {str(e)}")
            return []

    def cleanup_resources(self):
        """
        Perform resource cleanup operations.
        """
        try:
            # Clear old cache entries
            cache.clear()

            # Clean up temporary files
            import tempfile
            temp_dir = tempfile.gettempdir()

            # Remove old temporary files (older than 24 hours)
            cutoff_time = time.time() - (24 * 60 * 60)

            for filename in os.listdir(temp_dir):
                if filename.startswith('tmp') or filename.startswith('pyflow_'):
                    filepath = os.path.join(temp_dir, filename)
                    try:
                        if os.path.getmtime(filepath) < cutoff_time:
                            os.remove(filepath)
                    except Exception:
                        continue

            # Clear resource history
            with self._lock:
                self._resource_history = []

            logger.info("Resource cleanup completed")

        except Exception as e:
            logger.error(f"Failed to cleanup resources: {str(e)}")

    def get_system_info(self) -> Dict[str, Any]:
        """
        Get comprehensive system information.

        Returns:
            dict: System information
        """
        try:
            if PSUTIL_AVAILABLE:
                hardware_info = {
                    'cpu_count': psutil.cpu_count(),
                    'cpu_freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
                    'memory_total_gb': round(psutil.virtual_memory().total / (1024**3), 2),
                    'disk_total_gb': round(psutil.disk_usage('/').total / (1024**3), 2),
                }
            else:
                # Fallback hardware info
                disk_usage = shutil.disk_usage('/')
                hardware_info = {
                    'cpu_count': os.cpu_count() or 1,
                    'cpu_freq': None,
                    'memory_total_gb': 'unknown (psutil not available)',
                    'disk_total_gb': round(disk_usage.total / (1024**3), 2),
                }

            return {
                'platform': {
                    'system': os.name,
                    'platform': 'linux' if os.name == 'posix' else 'unknown',
                    'python_version': sys.version,
                },
                'hardware': hardware_info,
                'limits': {
                    'max_memory_percent': self.max_memory_usage,
                    'max_cpu_percent': self.max_cpu_usage,
                    'min_disk_space_gb': self.min_disk_space_gb,
                    'max_concurrent_workflows': self.max_concurrent_workflows,
                },
                'monitoring': {
                    'active': self._monitoring_active,
                    'interval_seconds': self._monitoring_interval,
                    'history_size': len(self._resource_history),
                    'psutil_available': PSUTIL_AVAILABLE,
                }
            }

        except Exception as e:
            logger.error(f"Failed to get system info: {str(e)}")
            return {
                'error': str(e),
                'psutil_available': PSUTIL_AVAILABLE
            }
