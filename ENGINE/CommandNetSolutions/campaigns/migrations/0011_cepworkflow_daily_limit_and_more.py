# Generated by Django 4.2.16 on 2025-05-31 15:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("campaigns", "0010_remove_global_tag_functionality"),
    ]

    operations = [
        migrations.AddField(
            model_name="cepworkflow",
            name="daily_limit",
            field=models.IntegerField(
                default=50, help_text="Maximum number of actions per day"
            ),
        ),
        migrations.AddField(
            model_name="cepworkflow",
            name="delay_between_actions",
            field=models.IntegerField(
                default=30, help_text="Delay between actions in seconds"
            ),
        ),
        migrations.AddField(
            model_name="cepworkflow",
            name="description",
            field=models.TextField(
                blank=True, help_text="Description of the CEP workflow"
            ),
        ),
        migrations.AddField(
            model_name="cepworkflow",
            name="name",
            field=models.Char<PERSON>ield(
                default="Legacy CEP Workflow",
                help_text="Name of the CEP workflow",
                max_length=255,
            ),
            preserve_default=False,
        ),
        migrations.Add<PERSON><PERSON>(
            model_name="cepworkflow",
            name="selected_workflows",
            field=models.J<PERSON><PERSON><PERSON>(
                default=list,
                help_text="List of selected workflow types (follow, like, comment, dm, discover)",
            ),
        ),
        migrations.AlterField(
            model_name="cepworkflow",
            name="subscription_tier",
            field=models.CharField(
                blank=True,
                choices=[("bronze", "Bronze"), ("silver", "Silver"), ("gold", "Gold")],
                default="bronze",
                help_text="Legacy subscription tier field (deprecated)",
                max_length=10,
            ),
        ),
    ]
