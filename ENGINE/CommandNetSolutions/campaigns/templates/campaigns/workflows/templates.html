{% extends 'campaigns/base.html' %}
{% load static %}

{% block title %}Workflow Templates{% endblock %}

{% block extra_css %}
<link href="{% static 'campaigns/css/workflow-dashboard.css' %}" rel="stylesheet">
{% endblock %}

{% block campaign_content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Workflow Templates</h1>
            <p class="text-muted">Predefined workflow configurations for different engagement strategies</p>
        </div>
        <div>
            <a href="{% url 'campaigns:workflow_dashboard' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Templates Grid -->
    {% if templates %}
    <div class="row">
        {% for template in templates %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card h-100 template-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <h5 class="mb-0">{{ template.name }}</h5>
                        <span class="badge template-category-{{ template.category }}">
                            {{ template.get_category_display }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">{{ template.description }}</p>
                    
                    <!-- Available Actions -->
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Available Actions:</h6>
                        <div class="d-flex flex-wrap gap-1">
                            {% for action_type, action_config in template.available_actions.items %}
                                <span class="action-badge enabled">{{ action_type|title }}</span>
                            {% empty %}
                                <span class="text-muted">No actions configured</span>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <!-- Default Limits -->
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="workflow-metric">
                                <div class="workflow-metric-value">{{ template.default_daily_limit }}</div>
                                <div class="workflow-metric-label">Daily Limit</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="workflow-metric">
                                <div class="workflow-metric-value">{{ template.default_hourly_limit }}</div>
                                <div class="workflow-metric-label">Hourly Limit</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="workflow-metric">
                                <div class="workflow-metric-value">{{ template.default_delay_between_actions }}s</div>
                                <div class="workflow-metric-label">Delay</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            {% if template.is_predefined %}
                                <i class="fas fa-shield-alt me-1"></i>System Template
                            {% else %}
                                <i class="fas fa-user me-1"></i>Custom Template
                            {% endif %}
                        </small>
                        <a href="{% url 'campaigns:workflow_template_detail' template.id %}" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye me-1"></i>View Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="fas fa-layer-group"></i>
        </div>
        <h4>No Workflow Templates Available</h4>
        <p class="text-muted">There are currently no workflow templates configured.</p>
        <a href="{% url 'campaigns:workflow_dashboard' %}" class="btn btn-primary">
            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to template cards
    const templateCards = document.querySelectorAll('.template-card');
    
    templateCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
{% endblock %}
