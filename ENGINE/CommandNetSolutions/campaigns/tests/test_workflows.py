"""
End-to-end workflow tests for the campaigns app.

This module tests complete workflows from start to finish including:
- Campaign creation to execution workflow
- Tag analysis and filtering workflows
- Account collection and processing
- Whitelist generation and export
- Error handling and recovery scenarios
- Performance and bulk operation testing
"""

import json
import uuid
from unittest.mock import patch, MagicMock
from django.test import TestCase, TransactionTestCase
from django.utils import timezone
from django.db import transaction
from django.contrib.auth.models import User

from campaigns.models import (
    Campaign, DynamicTag, TagGroup, TagCategory, CampaignTag,
    LocationTarget, UsernameTarget, WorkflowExecution, WorkflowProgressUpdate,
    TagAnalysisResult, CampaignResult
)
from campaigns.tests.base import BaseTestCase, BaseTransactionTestCase, MockExternalServices
from campaigns.tests.factories import (
    CampaignFactory, DynamicTagFactory, TagGroupFactory, TagCategoryFactory,
    UserFactory, CampaignTagFactory, LocationTargetFactory, UsernameTargetFactory,
    WorkflowExecutionFactory, CampaignResultFactory, TagAnalysisResultFactory
)


class CampaignCreationWorkflowTest(BaseTestCase):
    """Test the complete campaign creation workflow."""

    def test_complete_campaign_creation_workflow(self):
        """Test creating a campaign with all components."""
        # Step 1: Create campaign
        campaign = CampaignFactory.create(
            name='Complete Test Campaign',
            description='Full workflow test',
            status='draft',
            creator=self.user
        )

        # Step 2: Add location targets
        location_targets = [
            LocationTargetFactory.create(
                campaign=campaign,
                location_id=f'loc_{i}',
                city=f'City {i}',
                country='Test Country'
            ) for i in range(3)
        ]

        # Step 3: Add username targets
        username_targets = [
            UsernameTargetFactory.create(
                campaign=campaign,
                username=f'user_{i}',
                audience_type='followers'
            ) for i in range(2)
        ]

        # Step 4: Create and assign tags
        fitness_category = TagCategoryFactory.create_fitness()
        fitness_group = TagGroupFactory.create_influencer_group()

        fitness_tag = DynamicTagFactory.create(
            name='Fitness Enthusiast',
            category=fitness_category,
            tag_groups=[fitness_group],
            pattern='fitness|gym|workout',
            field='bio',
            tag_type='regex'
        )

        campaign_tag = CampaignTagFactory.create(
            campaign=campaign,
            tag=fitness_tag,
            is_required=True
        )

        # Step 5: Verify all components are properly linked
        self.assertEqual(campaign.location_targets.count(), 3)
        self.assertEqual(campaign.username_targets.count(), 2)
        self.assertEqual(campaign.campaign_tags.count(), 1)

        # Verify relationships
        self.assertIn(campaign_tag, campaign.campaign_tags.all())
        self.assertEqual(campaign_tag.tag, fitness_tag)
        self.assertTrue(campaign_tag.is_required)

        # Verify targets
        for target in location_targets:
            self.assertEqual(target.campaign, campaign)

        for target in username_targets:
            self.assertEqual(target.campaign, campaign)

    def test_campaign_validation_workflow(self):
        """Test campaign validation before launch."""
        campaign = CampaignFactory.create(status='draft')

        # Campaign without targets should not be valid for launch
        self.assertFalse(campaign.can_launch())

        # Add targets
        LocationTargetFactory.create(campaign=campaign)

        # Campaign with targets should be valid for launch
        self.assertTrue(campaign.can_launch())

        # Add tags
        tag = DynamicTagFactory.create()
        CampaignTagFactory.create(campaign=campaign, tag=tag)

        # Campaign with targets and tags should still be valid
        self.assertTrue(campaign.can_launch())


class TagAnalysisWorkflowTest(BaseTestCase):
    """Test tag analysis and filtering workflows."""

    @patch('campaigns.utils.analyze_account_with_tags')
    def test_tag_analysis_workflow(self, mock_analyze):
        """Test the tag analysis workflow for accounts."""
        # Setup
        campaign = CampaignFactory.create()

        # Create tags with different patterns
        bio_tag = DynamicTagFactory.create(
            name='Bio Keyword Tag',
            pattern='entrepreneur',
            field='bio',
            tag_type='keyword'
        )

        follower_tag = DynamicTagFactory.create(
            name='Follower Count Tag',
            pattern='{"min": 1000, "max": 10000}',
            field='follower_count',
            tag_type='range'
        )

        # Assign tags to campaign
        CampaignTagFactory.create(campaign=campaign, tag=bio_tag, is_required=True)
        CampaignTagFactory.create(campaign=campaign, tag=follower_tag, is_required=False)

        # Mock account data
        mock_account_data = {
            'account_id': 'test_account_123',
            'username': 'test_user',
            'bio': 'Entrepreneur and business owner',
            'follower_count': 5000,
            'following_count': 500,
            'is_verified': False
        }

        # Mock analysis results
        mock_analyze.return_value = {
            'bio_keyword_tag': {'matched': True, 'confidence': 0.95},
            'follower_count_tag': {'matched': True, 'confidence': 1.0}
        }

        # Run analysis
        from campaigns.utils import analyze_account_for_campaign
        results = analyze_account_for_campaign(campaign.id, mock_account_data)

        # Verify analysis was called
        mock_analyze.assert_called_once()

        # Verify results
        self.assertTrue(results['bio_keyword_tag']['matched'])
        self.assertTrue(results['follower_count_tag']['matched'])

        # Create analysis result records
        for tag_name, result in results.items():
            tag = DynamicTag.objects.get(name__icontains=tag_name.replace('_', ' '))
            TagAnalysisResultFactory.create(
                campaign=campaign,
                tag=tag,
                account_id=mock_account_data['account_id'],
                matched=result['matched'],
                confidence_score=result['confidence']
            )

        # Verify analysis results were stored
        analysis_results = TagAnalysisResult.objects.filter(campaign=campaign)
        self.assertEqual(analysis_results.count(), 2)

        # Verify all required tags matched
        required_tags_matched = analysis_results.filter(
            tag__campaigntag__is_required=True,
            matched=True
        ).count()
        required_tags_total = campaign.campaign_tags.filter(is_required=True).count()

        self.assertEqual(required_tags_matched, required_tags_total)

    def test_tag_filtering_workflow(self):
        """Test filtering accounts based on tag analysis results."""
        campaign = CampaignFactory.create()

        # Create tags
        required_tag = DynamicTagFactory.create(name='Required Tag')
        optional_tag = DynamicTagFactory.create(name='Optional Tag')

        # Assign tags to campaign
        CampaignTagFactory.create(campaign=campaign, tag=required_tag, is_required=True)
        CampaignTagFactory.create(campaign=campaign, tag=optional_tag, is_required=False)

        # Create analysis results for different accounts
        accounts = [
            {'id': 'acc_1', 'required_match': True, 'optional_match': True},
            {'id': 'acc_2', 'required_match': True, 'optional_match': False},
            {'id': 'acc_3', 'required_match': False, 'optional_match': True},
            {'id': 'acc_4', 'required_match': False, 'optional_match': False},
        ]

        for account in accounts:
            # Required tag result
            TagAnalysisResultFactory.create(
                campaign=campaign,
                tag=required_tag,
                account_id=account['id'],
                matched=account['required_match'],
                confidence_score=0.9 if account['required_match'] else 0.1
            )

            # Optional tag result
            TagAnalysisResultFactory.create(
                campaign=campaign,
                tag=optional_tag,
                account_id=account['id'],
                matched=account['optional_match'],
                confidence_score=0.8 if account['optional_match'] else 0.2
            )

        # Filter accounts that match all required tags
        from campaigns.utils import get_qualified_accounts
        qualified_accounts = get_qualified_accounts(campaign.id)

        # Should only include accounts that match required tags (acc_1 and acc_2)
        qualified_account_ids = [acc['account_id'] for acc in qualified_accounts]
        self.assertIn('acc_1', qualified_account_ids)
        self.assertIn('acc_2', qualified_account_ids)
        self.assertNotIn('acc_3', qualified_account_ids)
        self.assertNotIn('acc_4', qualified_account_ids)


class WorkflowExecutionTest(BaseTransactionTestCase):
    """Test workflow execution and monitoring."""

    @patch('campaigns.utils.airflow_client')
    def test_workflow_execution_lifecycle(self, mock_airflow):
        """Test complete workflow execution lifecycle."""
        # Setup mocks
        mock_airflow.trigger_dag.return_value = {'dag_run_id': 'test_run_123'}
        mock_airflow.get_dag_run.side_effect = [
            {'state': 'running', 'start_date': '2023-01-01T10:00:00Z'},
            {'state': 'success', 'end_date': '2023-01-01T11:00:00Z'}
        ]

        campaign = CampaignFactory.create(status='draft')

        # Start workflow
        workflow = WorkflowExecutionFactory.create(
            campaign=campaign,
            workflow_type='account_collection',
            status='pending',
            total_steps=100
        )

        # Simulate workflow progression
        progress_updates = [
            (10, 'Initializing collection'),
            (25, 'Collecting location data'),
            (50, 'Processing accounts'),
            (75, 'Analyzing accounts'),
            (100, 'Finalizing results')
        ]

        for step, message in progress_updates:
            # Update workflow progress (using the actual method signature)
            workflow.update_progress(step, step, 0)  # processed_items, successful_items, failed_items

            # Create progress update record
            WorkflowProgressUpdate.objects.create(
                workflow_execution=workflow,
                processed_items=step,
                successful_items=step,
                failed_items=0,
                progress=step,
                message=message,
                details={'step': step, 'total': 100}
            )

        # Verify workflow completion
        workflow.refresh_from_db()
        self.assertEqual(workflow.processed_items, 100)
        self.assertEqual(workflow.progress, 100.0)

        # Verify progress updates
        progress_records = WorkflowProgressUpdate.objects.filter(workflow_execution=workflow)
        self.assertEqual(progress_records.count(), 5)

        # Verify final status
        final_update = progress_records.last()
        self.assertEqual(final_update.message, 'Finalizing results')

    def test_workflow_error_handling(self):
        """Test workflow error handling and recovery."""
        campaign = CampaignFactory.create()

        workflow = WorkflowExecutionFactory.create(
            campaign=campaign,
            workflow_type='account_collection',
            status='running',
            total_items=100,
            processed_items=50
        )

        # Simulate error during workflow
        error_details = {
            'error_type': 'ConnectionError',
            'error_message': 'Failed to connect to Instagram API',
            'step': 'account_collection',
            'retry_count': 1
        }

        workflow.handle_error(error_details)

        # Verify error was recorded
        workflow.refresh_from_db()
        self.assertEqual(workflow.status, 'failed')
        self.assertIsNotNone(workflow.error_message)
        self.assertEqual(workflow.error_message, 'Failed to connect to Instagram API')

        # Test workflow retry
        workflow.retry()

        workflow.refresh_from_db()
        self.assertEqual(workflow.status, 'pending')
        # Note: retry_count field may not exist in current model, so we check if it exists
        if hasattr(workflow, 'retry_count'):
            self.assertEqual(workflow.retry_count, 1)


class BulkOperationWorkflowTest(BaseTestCase):
    """Test bulk operations and performance scenarios."""

    def test_bulk_tag_assignment(self):
        """Test bulk assignment of tags to campaigns."""
        # Create multiple campaigns
        campaigns = [CampaignFactory.create() for _ in range(10)]

        # Create multiple tags
        tags = [DynamicTagFactory.create() for _ in range(5)]

        # Bulk assign tags to campaigns
        campaign_tags = []
        for campaign in campaigns:
            for tag in tags[:3]:  # Assign first 3 tags to each campaign
                campaign_tags.append(
                    CampaignTag(campaign=campaign, tag=tag, is_required=False)
                )

        # Bulk create
        CampaignTag.objects.bulk_create(campaign_tags)

        # Verify assignments
        total_assignments = CampaignTag.objects.count()
        expected_assignments = len(campaigns) * 3  # 10 campaigns * 3 tags each
        self.assertEqual(total_assignments, expected_assignments)

        # Verify each campaign has 3 tags
        for campaign in campaigns:
            self.assertEqual(campaign.campaign_tags.count(), 3)

    def test_bulk_analysis_result_creation(self):
        """Test bulk creation of tag analysis results."""
        campaign = CampaignFactory.create()
        tags = [DynamicTagFactory.create() for _ in range(3)]

        # Assign tags to campaign
        for tag in tags:
            CampaignTagFactory.create(campaign=campaign, tag=tag)

        # Create bulk analysis results
        analysis_results = []
        account_ids = [f'acc_{i}' for i in range(100)]

        for account_id in account_ids:
            for tag in tags:
                analysis_results.append(
                    TagAnalysisResult(
                        campaign=campaign,
                        tag=tag,
                        account_id=account_id,
                        matched=True,
                        confidence_score=0.8,
                        match_details={'method': 'bulk_test'}
                    )
                )

        # Bulk create
        TagAnalysisResult.objects.bulk_create(analysis_results)

        # Verify results
        total_results = TagAnalysisResult.objects.filter(campaign=campaign).count()
        expected_results = len(account_ids) * len(tags)  # 100 accounts * 3 tags
        self.assertEqual(total_results, expected_results)

    def test_performance_with_large_dataset(self):
        """Test performance with large datasets."""
        import time

        campaign = CampaignFactory.create()

        # Create large number of tags
        start_time = time.time()
        tags = []
        for i in range(50):
            tags.append(DynamicTag(
                name=f'Performance Tag {i}',
                pattern=f'pattern_{i}',
                field='bio',
                tag_type='keyword',
                is_global=True
            ))

        DynamicTag.objects.bulk_create(tags)
        creation_time = time.time() - start_time

        # Verify creation was reasonably fast (should be under 1 second)
        self.assertLess(creation_time, 1.0)

        # Test querying performance
        start_time = time.time()
        global_tags = DynamicTag.objects.filter(is_global=True)
        tag_count = global_tags.count()
        query_time = time.time() - start_time

        # Verify query was fast and returned expected results
        self.assertLess(query_time, 0.1)
        self.assertGreaterEqual(tag_count, 50)


class EndToEndWorkflowTest(BaseTestCase):
    """Test complete end-to-end workflows."""

    @patch('campaigns.utils.trigger_campaign_workflow')
    @patch('campaigns.utils.get_workflow_status')
    def test_complete_campaign_execution_workflow(self, mock_get_status, mock_trigger):
        """Test complete campaign execution from creation to completion."""
        # Setup mocks
        mock_trigger.return_value = {'dag_run_id': 'test_run_123'}
        mock_get_status.return_value = {'state': 'success'}

        # Step 1: Create campaign with all components
        campaign = CampaignFactory.create(
            name='End-to-End Test Campaign',
            status='draft',
            creator=self.user
        )

        # Add targets
        LocationTargetFactory.create(campaign=campaign, location_id='nyc_001')
        UsernameTargetFactory.create(campaign=campaign, username='test_influencer')

        # Add tags
        tag = DynamicTagFactory.create(name='Influencer Tag')
        CampaignTagFactory.create(campaign=campaign, tag=tag, is_required=True)

        # Step 2: Launch campaign
        from campaigns.utils import launch_campaign
        workflow = launch_campaign(campaign.id)

        # Verify workflow was created and triggered
        self.assertIsNotNone(workflow)
        self.assertEqual(workflow.campaign, campaign)
        mock_trigger.assert_called_once()

        # Step 3: Simulate account collection completion
        CampaignResultFactory.create(
            campaign=campaign,
            total_accounts_collected=150,
            total_accounts_analyzed=145,
            total_accounts_whitelisted=35
        )

        # Step 4: Simulate tag analysis results
        for i in range(145):
            TagAnalysisResultFactory.create(
                campaign=campaign,
                tag=tag,
                account_id=f'collected_acc_{i}',
                matched=(i % 4 == 0),  # 25% match rate
                confidence_score=0.85 if (i % 4 == 0) else 0.15
            )

        # Step 5: Verify final results
        campaign_result = CampaignResult.objects.get(campaign=campaign)
        self.assertEqual(campaign_result.total_accounts_collected, 150)
        self.assertEqual(campaign_result.total_accounts_analyzed, 145)
        self.assertEqual(campaign_result.total_accounts_whitelisted, 35)

        # Verify tag analysis results
        matched_results = TagAnalysisResult.objects.filter(
            campaign=campaign,
            matched=True
        ).count()

        # Should have approximately 36 matches (145 * 0.25)
        self.assertGreaterEqual(matched_results, 30)
        self.assertLessEqual(matched_results, 40)
