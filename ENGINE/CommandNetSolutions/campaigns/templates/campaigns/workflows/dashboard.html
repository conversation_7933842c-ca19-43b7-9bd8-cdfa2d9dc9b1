{% extends 'campaigns/base.html' %}
{% load static %}

{% block title %}Workflow Dashboard{% endblock %}

{% block extra_css %}
<link href="{% static 'campaigns/css/workflow-dashboard.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Workflow Dashboard</h1>
            <p class="text-muted">Manage and monitor your workflow executions</p>
        </div>
        <div>
            <a href="{% url 'campaigns:workflow_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create New Workflow
            </a>
        </div>
    </div>

    <!-- Active Workflow Section -->
    {% if active_workflow %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-play-circle me-2"></i>Active Workflow
                        </h5>
                        <span class="badge bg-light text-primary">{{ active_workflow.get_status_display }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="fw-bold">{{ active_workflow.name }}</h6>
                            <p class="text-muted mb-2">{{ active_workflow.description }}</p>
                            <div class="d-flex align-items-center mb-3">
                                <span class="me-3">
                                    <i class="fas fa-project-diagram text-muted me-1"></i>
                                    <strong>Campaign:</strong> {{ active_workflow.campaign.name }}
                                </span>
                                <span class="me-3">
                                    <i class="fas fa-users text-muted me-1"></i>
                                    <strong>Whitelist:</strong> {{ active_workflow.whitelist_count }} accounts
                                </span>
                                <span>
                                    <i class="fas fa-clock text-muted me-1"></i>
                                    <strong>Started:</strong> {{ active_workflow.started_at|date:"M d, Y H:i" }}
                                </span>
                            </div>
                            
                            <!-- Progress Bar -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small class="text-muted">Overall Progress</small>
                                    <small class="text-muted">{{ active_workflow.overall_progress|floatformat:1 }}%</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ active_workflow.overall_progress }}%"></div>
                                </div>
                            </div>
                            
                            <!-- Selected Actions -->
                            <div class="mb-3">
                                <small class="text-muted d-block mb-1">Selected Actions:</small>
                                <div class="d-flex flex-wrap gap-1">
                                    {% for action in active_workflow.selected_workflows %}
                                        <span class="badge bg-secondary">{{ action|title }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group-vertical w-100" role="group">
                                {% if active_workflow.status == 'pending' %}
                                    <form method="post" action="{% url 'campaigns:workflow_start' active_workflow.id %}" class="mb-2">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-play me-2"></i>Start Workflow
                                        </button>
                                    </form>
                                {% elif active_workflow.status == 'running' %}
                                    <form method="post" action="{% url 'campaigns:workflow_pause' active_workflow.id %}" class="mb-2">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-warning w-100">
                                            <i class="fas fa-pause me-2"></i>Pause Workflow
                                        </button>
                                    </form>
                                {% elif active_workflow.status == 'paused' %}
                                    <form method="post" action="{% url 'campaigns:workflow_resume' active_workflow.id %}" class="mb-2">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-play me-2"></i>Resume Workflow
                                        </button>
                                    </form>
                                {% endif %}
                                
                                {% if active_workflow.status in 'pending,running,paused' %}
                                    <form method="post" action="{% url 'campaigns:workflow_stop' active_workflow.id %}" class="mb-2">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-danger w-100" 
                                                onclick="return confirm('Are you sure you want to stop this workflow?')">
                                            <i class="fas fa-stop me-2"></i>Stop Workflow
                                        </button>
                                    </form>
                                {% endif %}
                                
                                <a href="{% url 'campaigns:workflow_detail' active_workflow.id %}" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-eye me-2"></i>View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Main Content Row -->
    <div class="row">
        <!-- Available Campaigns -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-project-diagram me-2"></i>Available Campaigns
                    </h5>
                    <small class="text-muted">Campaigns ready for workflow creation</small>
                </div>
                <div class="card-body">
                    {% if available_campaigns %}
                        <div class="list-group list-group-flush">
                            {% for campaign in available_campaigns %}
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <h6 class="mb-1">{{ campaign.name }}</h6>
                                        <small class="text-muted">
                                            <i class="fas fa-users me-1"></i>{{ campaign.whitelist_count|default:0 }} accounts
                                            <span class="ms-2">
                                                <i class="fas fa-calendar me-1"></i>{{ campaign.created_at|date:"M d, Y" }}
                                            </span>
                                        </small>
                                    </div>
                                    <a href="{% url 'campaigns:workflow_create' %}?campaign={{ campaign.id }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-plus me-1"></i>Create Workflow
                                    </a>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No campaigns available for workflow creation</p>
                            <a href="{% url 'campaigns:campaign_list' %}" class="btn btn-outline-primary">
                                <i class="fas fa-plus me-2"></i>Create Campaign
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Workflow Templates -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">
                                <i class="fas fa-layer-group me-2"></i>Workflow Templates
                            </h5>
                            <small class="text-muted">Predefined workflow configurations</small>
                        </div>
                        <a href="{% url 'campaigns:workflow_templates' %}" class="btn btn-sm btn-outline-secondary">
                            View All
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if workflow_templates %}
                        <div class="row">
                            {% for template in workflow_templates|slice:":4" %}
                                <div class="col-md-6 mb-3">
                                    <div class="card border-0 bg-light h-100">
                                        <div class="card-body p-3">
                                            <h6 class="card-title mb-2">{{ template.name }}</h6>
                                            <p class="card-text small text-muted mb-2">{{ template.description|truncatechars:80 }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="badge bg-secondary">{{ template.get_category_display }}</span>
                                                <small class="text-muted">{{ template.default_daily_limit }}/day</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No workflow templates available</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Workflows -->
    {% if completed_workflows %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Recent Workflows
                    </h5>
                    <small class="text-muted">Recently completed workflow executions</small>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Workflow Name</th>
                                    <th>Campaign</th>
                                    <th>Status</th>
                                    <th>Progress</th>
                                    <th>Completed</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for workflow in completed_workflows %}
                                    <tr>
                                        <td>
                                            <strong>{{ workflow.name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ workflow.description|truncatechars:50 }}</small>
                                        </td>
                                        <td>{{ workflow.campaign.name }}</td>
                                        <td>
                                            {% if workflow.status == 'completed' %}
                                                <span class="badge bg-success">{{ workflow.get_status_display }}</span>
                                            {% elif workflow.status == 'failed' %}
                                                <span class="badge bg-danger">{{ workflow.get_status_display }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ workflow.get_status_display }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 6px; width: 80px;">
                                                <div class="progress-bar bg-success" role="progressbar" 
                                                     style="width: {{ workflow.overall_progress }}%"></div>
                                            </div>
                                            <small class="text-muted">{{ workflow.overall_progress|floatformat:1 }}%</small>
                                        </td>
                                        <td>
                                            {% if workflow.completed_at %}
                                                {{ workflow.completed_at|date:"M d, Y H:i" }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'campaigns:workflow_detail' workflow.id %}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh active workflow status every 30 seconds
    {% if active_workflow %}
    setInterval(function() {
        fetch('{% url "campaigns:api_workflow_status" active_workflow.id %}')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update progress bar
                    const progressBar = document.querySelector('.progress-bar');
                    const progressText = document.querySelector('.progress + .mb-3 small:last-child');
                    
                    if (progressBar && progressText) {
                        progressBar.style.width = data.workflow.progress + '%';
                        progressText.textContent = data.workflow.progress.toFixed(1) + '%';
                    }
                    
                    // Reload page if status changed significantly
                    if (data.workflow.status !== '{{ active_workflow.status }}') {
                        location.reload();
                    }
                }
            })
            .catch(error => console.error('Error fetching workflow status:', error));
    }, 30000);
    {% endif %}
</script>
{% endblock %}
