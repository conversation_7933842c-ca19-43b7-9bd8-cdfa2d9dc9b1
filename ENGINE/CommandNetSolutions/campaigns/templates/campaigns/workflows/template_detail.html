{% extends 'campaigns/base.html' %}
{% load static %}

{% block title %}{{ template.name }} - Template Details{% endblock %}

{% block extra_css %}
<link href="{% static 'campaigns/css/workflow-dashboard.css' %}" rel="stylesheet">
{% endblock %}

{% block campaign_content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ template.name }}</h1>
            <p class="text-muted">{{ template.description }}</p>
        </div>
        <div>
            <a href="{% url 'campaigns:workflow_templates' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Templates
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Template Information -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Template Configuration</h5>
                        <span class="badge template-category-{{ template.category }}">
                            {{ template.get_category_display }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Basic Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Template Name</label>
                                <div>{{ template.name }}</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Category</label>
                                <div>{{ template.get_category_display }}</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Type</label>
                                <div>
                                    {% if template.is_predefined %}
                                        <span class="badge bg-primary">System Template</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Custom Template</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    {% if template.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Created</label>
                                <div>{{ template.created_at|date:"M d, Y H:i" }}</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Last Updated</label>
                                <div>{{ template.updated_at|date:"M d, Y H:i" }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Description -->
                    <div class="mb-4">
                        <label class="form-label text-muted">Description</label>
                        <div class="p-3 bg-light rounded">{{ template.description }}</div>
                    </div>
                    
                    <!-- Available Actions -->
                    <div class="mb-4">
                        <label class="form-label text-muted">Available Actions</label>
                        {% if template.available_actions %}
                        <div class="row">
                            {% for action_type, action_config in template.available_actions.items %}
                            <div class="col-md-6 mb-3">
                                <div class="card border-0 bg-light">
                                    <div class="card-body p-3">
                                        <h6 class="card-title mb-2">
                                            <span class="action-badge enabled">{{ action_type|title }}</span>
                                        </h6>
                                        {% if action_config.description %}
                                        <p class="card-text small text-muted mb-2">{{ action_config.description }}</p>
                                        {% endif %}
                                        {% if action_config.parameters %}
                                        <div class="small">
                                            <strong>Parameters:</strong>
                                            <ul class="mb-0 mt-1">
                                                {% for param, value in action_config.parameters.items %}
                                                <li>{{ param }}: {{ value }}</li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-muted">No actions configured for this template.</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Default Limits -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Default Limits</h5>
                </div>
                <div class="card-body">
                    <div class="workflow-stats-grid">
                        <div class="workflow-metric">
                            <div class="workflow-metric-value">{{ template.default_daily_limit }}</div>
                            <div class="workflow-metric-label">Daily Limit</div>
                        </div>
                        
                        <div class="workflow-metric">
                            <div class="workflow-metric-value">{{ template.default_hourly_limit }}</div>
                            <div class="workflow-metric-label">Hourly Limit</div>
                        </div>
                        
                        <div class="workflow-metric">
                            <div class="workflow-metric-value">{{ template.default_delay_between_actions }}s</div>
                            <div class="workflow-metric-label">Action Delay</div>
                        </div>
                    </div>
                    
                    {% if template.is_active %}
                    <div class="mt-4">
                        <a href="{% url 'campaigns:workflow_create' %}?template={{ template.id }}" 
                           class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>Create Workflow
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- DAG Structure (if available) -->
            {% if template.dag_structure %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">DAG Structure</h5>
                </div>
                <div class="card-body">
                    <div class="small">
                        <pre class="bg-light p-2 rounded">{{ template.dag_structure|pprint }}</pre>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any interactive functionality here
    console.log('Template detail page loaded');
});
</script>
{% endblock %}
