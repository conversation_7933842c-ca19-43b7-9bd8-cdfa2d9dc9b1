# Campaigns App Test Suite

This directory contains a comprehensive test suite for the CommandNet Solutions campaigns app. The test suite covers all aspects of the application including models, views, forms, external service integrations, and end-to-end workflows.

## Test Organization

### Core Test Modules

- **`test_models.py`** - Model validation, relationships, and constraints
- **`test_views.py`** - View functionality, permissions, and responses  
- **`test_forms.py`** - Form validation, field handling, and error cases
- **`test_integrations.py`** - External service mocking and integration
- **`test_workflows.py`** - End-to-end workflow testing

### Support Modules

- **`base.py`** - Base test classes and utilities
- **`factories.py`** - Test data factories for consistent setup
- **`test_runner.py`** - Custom test runner with enhanced reporting
- **`__init__.py`** - Package initialization and exports

## Key Features Tested

### 1. Tag System Testing ✅

- **DynamicTag Model**: CRUD operations, validation, field constraints
- **TagGroup Model**: Many-to-many relationships with tags, hierarchy
- **TagCategory Model**: Hierarchical relationships, validation
- **CampaignTag Model**: Campaign-tag associations, unique constraints
- **Tag Filtering**: Global/system flags, tag group membership
- **System Tag Protection**: Deletion prevention for system tags
- **Tag Metrics**: Calculation and storage validation

**Recent Fix Verified**: The `tag_groups` field correction (was `tag_group`) is thoroughly tested in `CampaignTagFormTest.test_campaign_tag_form_tag_queryset_filtering()`.

### 2. Campaign Management Testing ✅

- **Campaign Lifecycle**: Create, update, delete, status transitions
- **Target Management**: LocationTarget and UsernameTarget operations
- **Validation Rules**: Business logic and constraint validation
- **CampaignDeleteView**: `form_valid()` method implementation (recent fix)
- **Export Functionality**: CSV and Excel format generation
- **Bulk Operations**: Multi-select and batch processing

**Recent Fix Verified**: The `CampaignDeleteView.form_valid()` method is tested in `CampaignViewTest.test_campaign_delete_view_post()`.

### 3. Tag Group Management Testing ✅

- **CRUD Operations**: Create, read, update, delete functionality
- **Many-to-Many Relationships**: Tag-group associations
- **Hierarchy Support**: Parent-child relationships
- **Access Controls**: Global vs. private tag group permissions
- **Deletion Impact**: Effect on associated tags

### 4. Forms and Views Testing ✅

- **CampaignTagForm**: Correct `tag_groups` field usage validation
- **All CRUD Views**: List, detail, create, update, delete operations
- **Tag Management**: Form validation and error handling
- **HTMX Endpoints**: Dynamic tag operations testing
- **API Endpoints**: Location search and tag validation

### 5. Resource Manager Integration Testing ✅

- **Workflow Execution**: Tracking and status updates
- **Campaign-Workflow**: Association management
- **Resource Allocation**: Queue management and limits
- **Campaign Operations**: Pause, resume, stop functionality
- **Progress Monitoring**: Real-time status tracking

### 6. External Service Integration Testing ✅

- **Airflow Integration**: DAG triggering and status monitoring
- **Redis Communication**: Caching and queue management
- **Database Transactions**: Atomic operations and rollback
- **Error Handling**: Connection failures and retry logic
- **Health Checks**: Service availability monitoring

### 7. Data Model Relationships Testing ✅

- **Cascade Deletion**: Proper cleanup across related models
- **Foreign Key Constraints**: Referential integrity validation
- **Many-to-Many Operations**: Association management
- **Model Validation**: Custom save/delete method testing

## Running Tests

### Run All Tests
```bash
# Run complete test suite
python manage.py test campaigns.tests

# Run with custom test runner
python manage.py test campaigns.tests --testrunner=campaigns.tests.test_runner.CampaignsTestRunner

# Run with verbose output
python manage.py test campaigns.tests --verbosity=2
```

### Run Specific Test Modules
```bash
# Run only model tests
python manage.py test campaigns.tests.test_models

# Run only view tests
python manage.py test campaigns.tests.test_views

# Run only form tests
python manage.py test campaigns.tests.test_forms

# Run only integration tests
python manage.py test campaigns.tests.test_integrations

# Run only workflow tests
python manage.py test campaigns.tests.test_workflows
```

### Run Specific Test Classes
```bash
# Run campaign model tests
python manage.py test campaigns.tests.test_models.CampaignModelTest

# Run tag form tests
python manage.py test campaigns.tests.test_forms.CampaignTagFormTest

# Run delete view tests
python manage.py test campaigns.tests.test_views.CampaignViewTest.test_campaign_delete_view_post
```

### Performance and Coverage Testing
```bash
# Run performance tests
python campaigns/tests/test_runner.py performance

# Run integration tests
python campaigns/tests/test_runner.py integration

# Generate coverage report
python campaigns/tests/test_runner.py coverage
coverage run --source='.' manage.py test campaigns.tests
coverage report
coverage html
```

## Test Data Management

### Using Factories
```python
from campaigns.tests.factories import CampaignFactory, DynamicTagFactory

# Create test campaign
campaign = CampaignFactory.create(name='Test Campaign')

# Create test tag with specific attributes
tag = DynamicTagFactory.create(
    name='Fitness Tag',
    tag_type='regex',
    pattern='fitness|gym|workout'
)
```

### Using Base Test Classes
```python
from campaigns.tests.base import BaseTestCase

class MyTestCase(BaseTestCase):
    def test_something(self):
        # self.user, self.campaign, self.dynamic_tag are available
        # Mock external services are automatically configured
        pass
```

## Mocking External Services

### Automatic Mocking
```python
from campaigns.tests.base import BaseTestCase

class MyTestCase(BaseTestCase):
    # Redis and Airflow are automatically mocked
    def test_with_mocks(self):
        # Test code here - external services are mocked
        pass
```

### Manual Mocking
```python
from campaigns.tests.base import MockExternalServices

def test_with_manual_mocks(self):
    with MockExternalServices() as mocks:
        # Test code here
        mocks.redis.ping.assert_called_once()
        mocks.airflow.trigger_dag.assert_called_once()
```

## Test Scenarios and Expected Outcomes

### Critical Bug Fixes Verified

1. **FieldError Fix**: `tag_group` → `tag_groups` field correction
   - **Test**: `CampaignTagFormTest.test_campaign_tag_form_tag_queryset_filtering()`
   - **Verifies**: Form uses correct many-to-many field name
   - **Expected**: No FieldError when initializing CampaignTagForm

2. **DeleteView Fix**: `delete()` → `form_valid()` method migration
   - **Test**: `CampaignViewTest.test_campaign_delete_view_form_valid_method_exists()`
   - **Verifies**: CampaignDeleteView has form_valid method
   - **Expected**: No Django deprecation warnings

3. **Static Files**: CommandNetSolutions.js availability
   - **Test**: Verified through test runner setup
   - **Expected**: No 404 errors for static JavaScript files

### Performance Expectations

- **Model Creation**: < 0.1s for single objects, < 1.0s for bulk operations
- **Database Queries**: < 10 queries for simple operations, < 50 for complex workflows
- **View Responses**: < 0.5s for standard pages, < 2.0s for complex reports
- **External Service Calls**: Properly mocked, no real network calls during tests

### Coverage Goals

- **Models**: 100% line coverage
- **Views**: 95% line coverage (excluding error handling edge cases)
- **Forms**: 100% line coverage
- **Utils**: 90% line coverage (excluding external service error handling)
- **Overall**: 95% line coverage target

## Continuous Integration

### GitHub Actions Integration
```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.8
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run tests
        run: python manage.py test campaigns.tests --verbosity=2
      - name: Generate coverage
        run: |
          coverage run --source='.' manage.py test campaigns.tests
          coverage xml
      - name: Upload coverage
        uses: codecov/codecov-action@v1
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure Django settings are properly configured
2. **Database Errors**: Run migrations before testing
3. **Mock Failures**: Check that external service mocks are properly configured
4. **Performance Issues**: Use `--debug-mode` to identify slow queries

### Debug Mode
```bash
# Run tests with debug output
python manage.py test campaigns.tests --debug-mode --verbosity=3
```

### Test Data Cleanup
```python
from campaigns.tests.test_runner import TestDataManager

# Clean up test data
TestDataManager.clean_test_data()

# Create fresh fixtures
fixtures = TestDataManager.create_test_fixtures()
```

## Contributing

When adding new tests:

1. Follow the existing naming conventions
2. Use appropriate base classes (`BaseTestCase` or `BaseTransactionTestCase`)
3. Use factories for test data creation
4. Mock external services appropriately
5. Include both success and failure scenarios
6. Add performance assertions for critical operations
7. Update this documentation for new test categories

## Recent Updates

- ✅ Fixed `tag_groups` field usage in CampaignTagForm tests
- ✅ Added CampaignDeleteView `form_valid()` method tests
- ✅ Comprehensive external service mocking
- ✅ Performance testing framework
- ✅ Coverage reporting integration
- ✅ End-to-end workflow testing
