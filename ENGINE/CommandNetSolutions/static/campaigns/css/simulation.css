/* Campaign Simulation Styles */

/* Dashboard Layout */
.simulation-dashboard {
  padding: 20px 0;
}

.simulation-header {
  margin-bottom: 30px;
}

.simulation-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 10px;
}

.simulation-subtitle {
  font-size: 1.1rem;
  color: var(--gray-color);
  margin-bottom: 0;
}

/* Card Styles */
.simulation-card {
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 25px;
  border: none;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.simulation-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.simulation-card .card-header {
  padding: 15px 20px;
  font-weight: 600;
  font-size: 1.1rem;
  border-bottom: none;
}

.simulation-card .card-header.bg-primary,
.simulation-card .card-header.bg-success,
.simulation-card .card-header.bg-info,
.simulation-card .card-header.bg-warning,
.simulation-card .card-header.bg-danger,
.simulation-card .card-header.bg-secondary,
.simulation-card .card-header.bg-dark {
  color: white;
}

.simulation-card .card-body {
  padding: 20px;
}

/* Chart Containers */
.chart-container {
  height: 300px;
  position: relative;
  margin-bottom: 15px;
}

/* Metrics Display */
.metric-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.metric-item {
  text-align: center;
  padding: 15px;
  flex: 1;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.02);
  margin: 0 5px;
}

.metric-value {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--dark-color);
  line-height: 1.2;
}

.metric-label {
  font-size: 0.9rem;
  color: var(--gray-color);
  margin-top: 5px;
}

/* Workflow Progress */
.workflow-item {
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.workflow-item:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

.workflow-item:last-child {
  border-bottom: none;
}

.workflow-title {
  font-weight: 600;
  margin-bottom: 10px;
}

.workflow-progress {
  height: 20px;
  border-radius: 50px;
  margin-bottom: 10px;
  background-color: #e9ecef;
  overflow: hidden;
}

.workflow-progress .progress-bar {
  border-radius: 50px;
  transition: width 0.5s ease;
}

.workflow-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: var(--gray-color);
}

/* Account Lists */
.account-list {
  max-height: 350px;
  overflow-y: auto;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.01);
}

.account-item {
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.account-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.account-item:last-child {
  border-bottom: none;
}

.account-username {
  font-weight: 600;
  margin-bottom: 5px;
}

.account-bio {
  font-size: 0.9rem;
  color: var(--gray-color);
  margin-bottom: 8px;
}

.account-stats {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: var(--gray-color);
}

/* Tag Badges */
.tag-badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 50px;
  font-size: 0.8rem;
  margin-right: 5px;
  margin-bottom: 5px;
}

/* Run Simulation Form */
.simulation-form {
  max-width: 800px;
  margin: 0 auto;
}

.simulation-form .form-group {
  margin-bottom: 25px;
}

.simulation-form .form-label {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 8px;
}

.simulation-form .form-control {
  border-radius: 8px;
  padding: 12px 15px;
  border: 1px solid #dee2e6;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.simulation-form .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.simulation-form .help-text {
  font-size: 0.85rem;
  color: var(--gray-color);
  margin-top: 5px;
}

.simulation-form .form-check {
  margin-bottom: 10px;
}

.simulation-form .form-check-label {
  font-weight: 500;
}

.simulation-form .btn-submit {
  padding: 12px 30px;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .simulation-title {
    font-size: 1.8rem;
  }

  .metric-value {
    font-size: 1.8rem;
  }

  .chart-container {
    height: 250px;
  }

  .simulation-form {
    padding: 0 15px;
  }
}

/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.5s ease forwards;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  background-color: rgba(0, 0, 0, 0.01);
  border-radius: 8px;
}

.empty-state-icon {
  font-size: 3rem;
  color: var(--gray-color);
  margin-bottom: 15px;
}

.empty-state-text {
  font-size: 1.1rem;
  color: var(--gray-color);
  margin-bottom: 15px;
}

/* Whitelist Analytics Styles */
.metric-card {
  padding: 20px;
  border-radius: 10px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.metric-description {
  font-size: 0.8rem;
  color: var(--gray-color);
  margin-top: 5px;
  font-style: italic;
}

/* Privilege Statistics */
.privilege-stats {
  margin-top: 15px;
}

.privilege-stat {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.privilege-stat:last-child {
  border-bottom: none;
}

.privilege-name {
  font-weight: 500;
  margin-left: 8px;
  flex: 1;
}

.privilege-count {
  font-weight: 600;
}

/* Quality Breakdown */
.quality-breakdown {
  margin-top: 15px;
}

.quality-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 8px 0;
}

.quality-label {
  font-weight: 500;
  font-size: 0.9rem;
  flex: 1;
}

.quality-value {
  font-weight: 600;
  color: var(--primary-color);
  margin-right: 10px;
}

.quality-bar {
  width: 60px;
  height: 6px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.quality-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--success-color));
  border-radius: 3px;
  transition: width 0.5s ease;
}

/* Enhanced Metric Values */
.metric-value.text-success {
  color: #28a745 !important;
}

.metric-value.text-info {
  color: #17a2b8 !important;
}

.metric-value.text-warning {
  color: #ffc107 !important;
}

.metric-value.text-primary {
  color: #007bff !important;
}

/* Whitelist Overview Cards */
.whitelist-overview .metric-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Responsive Whitelist Analytics */
@media (max-width: 768px) {
  .privilege-stat {
    flex-direction: column;
    align-items: flex-start;
    padding: 12px 0;
  }

  .privilege-name {
    margin-left: 0;
    margin-bottom: 5px;
  }

  .quality-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .quality-bar {
    width: 100%;
    margin-top: 5px;
  }
}
