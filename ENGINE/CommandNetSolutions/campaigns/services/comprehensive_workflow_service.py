"""
Comprehensive workflow service for managing the new workflow system.
"""
import logging
from typing import List, Dict, Any, Optional
from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError

from campaigns.models import Campaign
from campaigns.models.workflow import Workflow, WorkflowTemplate, WorkflowAction, ResourceManager
from campaigns.models.cep import CEPWorkflow
from instagram.models import WhiteListEntry, Accounts

logger = logging.getLogger(__name__)


class ComprehensiveWorkflowService:
    """
    Service for managing comprehensive workflows that process campaign whitelists.
    """

    def __init__(self):
        self.resource_manager = self._get_or_create_resource_manager()

    def _get_or_create_resource_manager(self) -> ResourceManager:
        """Get or create the singleton resource manager."""
        resource_manager, created = ResourceManager.objects.get_or_create(
            defaults={
                'max_daily_actions': 500,
                'max_hourly_actions': 50,
                'max_concurrent_actions': 5,
                'priority_mode': 'cep',
                'is_active': True
            }
        )
        if created:
            logger.info("Created new resource manager")
        return resource_manager

    def create_workflow(
        self,
        name: str,
        description: str,
        source_campaign_ids: List[str],
        template_id: str,
        enabled_actions: List[str],
        action_parameters: Dict[str, Any],
        daily_limit: int,
        hourly_limit: int,
        delay_between_actions: int,
        schedule_type: str = 'immediate',
        scheduled_start: Optional[timezone.datetime] = None
    ) -> Workflow:
        """
        Create a new comprehensive workflow.
        
        Args:
            name: Workflow name
            description: Workflow description
            source_campaign_ids: List of campaign IDs to process
            template_id: Template to use
            enabled_actions: List of enabled action types
            action_parameters: Parameters for each action type
            daily_limit: Maximum actions per day
            hourly_limit: Maximum actions per hour
            delay_between_actions: Delay between actions in seconds
            schedule_type: How to schedule the workflow
            scheduled_start: When to start (for scheduled workflows)
            
        Returns:
            Created workflow instance
            
        Raises:
            ValidationError: If validation fails
        """
        try:
            with transaction.atomic():
                # Validate one-active-workflow restriction
                if self._has_active_workflow():
                    raise ValidationError(
                        "Only one active workflow is allowed at a time. "
                        "Please stop the current workflow before creating a new one."
                    )

                # Get template
                template = WorkflowTemplate.objects.get(id=template_id, is_active=True)
                
                # Get source campaigns
                campaigns = Campaign.objects.filter(
                    id__in=source_campaign_ids,
                    status__in=['completed', 'stopped']
                )
                
                if not campaigns.exists():
                    raise ValidationError("No valid source campaigns found.")

                # Calculate total accounts to process
                total_accounts = self._calculate_total_accounts(campaigns)
                
                if total_accounts == 0:
                    raise ValidationError("No accounts found in campaign whitelists.")

                # Create workflow
                workflow = Workflow.objects.create(
                    name=name,
                    description=description,
                    template=template,
                    status='pending',
                    enabled_actions=enabled_actions,
                    action_parameters=action_parameters,
                    daily_limit=daily_limit,
                    hourly_limit=hourly_limit,
                    delay_between_actions=delay_between_actions,
                    schedule_type=schedule_type,
                    scheduled_start=scheduled_start,
                    total_accounts=total_accounts
                )

                # Add source campaigns
                workflow.source_campaigns.set(campaigns)

                # Generate actions for the workflow
                self._generate_workflow_actions(workflow, campaigns)

                # Update resource manager
                self.resource_manager.active_workflow = workflow
                self.resource_manager.save()

                # Mark campaigns as in workflow
                campaigns.update(is_in_cep=True)

                logger.info(f"Created workflow: {workflow.name} with {total_accounts} accounts")
                return workflow

        except Exception as e:
            logger.error(f"Error creating workflow: {str(e)}")
            raise

    def _has_active_workflow(self) -> bool:
        """Check if there's already an active workflow."""
        # Check both new and legacy workflows
        active_new = Workflow.objects.filter(
            status__in=['pending', 'running', 'paused']
        ).exists()
        
        active_legacy = CEPWorkflow.objects.filter(
            status__in=['pending', 'running', 'paused']
        ).exists()
        
        return active_new or active_legacy

    def _calculate_total_accounts(self, campaigns) -> int:
        """Calculate total accounts across all campaign whitelists."""
        total = 0
        for campaign in campaigns:
            count = WhiteListEntry.objects.filter(
                account__campaign_id=str(campaign.id)
            ).count()
            total += count
        return total

    def _generate_workflow_actions(self, workflow: Workflow, campaigns):
        """Generate individual actions for the workflow."""
        actions_to_create = []
        
        for campaign in campaigns:
            # Get whitelist entries for this campaign
            whitelist_entries = WhiteListEntry.objects.filter(
                account__campaign_id=str(campaign.id)
            ).select_related('account')
            
            for entry in whitelist_entries:
                username = entry.account.username
                
                # Create actions for each enabled action type
                for action_type in workflow.enabled_actions:
                    action_params = workflow.action_parameters.get(action_type, {})
                    
                    action = WorkflowAction(
                        workflow=workflow,
                        action_type=action_type,
                        target_username=username,
                        parameters=action_params,
                        status='pending'
                    )
                    actions_to_create.append(action)

        # Bulk create actions
        if actions_to_create:
            WorkflowAction.objects.bulk_create(actions_to_create, batch_size=1000)
            logger.info(f"Created {len(actions_to_create)} actions for workflow {workflow.name}")

    def start_workflow(self, workflow_id: str) -> bool:
        """
        Start a workflow execution.
        
        Args:
            workflow_id: ID of the workflow to start
            
        Returns:
            True if started successfully, False otherwise
        """
        try:
            with transaction.atomic():
                workflow = Workflow.objects.get(id=workflow_id)
                
                if not workflow.can_start():
                    logger.warning(f"Cannot start workflow {workflow.name} in status {workflow.status}")
                    return False

                # Check resource availability
                if not self.resource_manager.can_execute_action():
                    logger.warning("Resource manager does not allow new actions")
                    return False

                # Update workflow status
                workflow.status = 'running'
                workflow.started_at = timezone.now()
                workflow.save()

                # Generate Airflow DAG
                dag_id = self._generate_airflow_dag(workflow)
                workflow.airflow_dag_id = dag_id
                workflow.save()

                logger.info(f"Started workflow: {workflow.name}")
                return True

        except Exception as e:
            logger.error(f"Error starting workflow {workflow_id}: {str(e)}")
            return False

    def pause_workflow(self, workflow_id: str) -> bool:
        """Pause a running workflow."""
        try:
            workflow = Workflow.objects.get(id=workflow_id)
            
            if not workflow.can_pause():
                return False

            workflow.status = 'paused'
            workflow.save()

            # Pause Airflow DAG
            self._pause_airflow_dag(workflow.airflow_dag_id)

            logger.info(f"Paused workflow: {workflow.name}")
            return True

        except Exception as e:
            logger.error(f"Error pausing workflow {workflow_id}: {str(e)}")
            return False

    def stop_workflow(self, workflow_id: str) -> bool:
        """Stop a workflow execution."""
        try:
            with transaction.atomic():
                workflow = Workflow.objects.get(id=workflow_id)
                
                if not workflow.can_stop():
                    return False

                workflow.status = 'stopped'
                workflow.completed_at = timezone.now()
                workflow.save()

                # Stop Airflow DAG
                self._stop_airflow_dag(workflow.airflow_dag_id)

                # Release campaigns
                workflow.source_campaigns.update(is_in_cep=False)

                # Clear active workflow from resource manager
                if self.resource_manager.active_workflow == workflow:
                    self.resource_manager.active_workflow = None
                    self.resource_manager.save()

                logger.info(f"Stopped workflow: {workflow.name}")
                return True

        except Exception as e:
            logger.error(f"Error stopping workflow {workflow_id}: {str(e)}")
            return False

    def _generate_airflow_dag(self, workflow: Workflow) -> str:
        """Generate Airflow DAG for the workflow."""
        # This would integrate with Airflow to create a DAG
        # For now, return a mock DAG ID
        dag_id = f"workflow_{workflow.id}_{int(timezone.now().timestamp())}"
        logger.info(f"Generated Airflow DAG: {dag_id}")
        return dag_id

    def _pause_airflow_dag(self, dag_id: str):
        """Pause Airflow DAG."""
        # This would integrate with Airflow API
        logger.info(f"Paused Airflow DAG: {dag_id}")

    def _stop_airflow_dag(self, dag_id: str):
        """Stop Airflow DAG."""
        # This would integrate with Airflow API
        logger.info(f"Stopped Airflow DAG: {dag_id}")

    def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get comprehensive workflow status."""
        try:
            workflow = Workflow.objects.get(id=workflow_id)
            
            # Get action statistics
            actions = WorkflowAction.objects.filter(workflow=workflow)
            action_stats = {
                'total': actions.count(),
                'pending': actions.filter(status='pending').count(),
                'running': actions.filter(status='running').count(),
                'completed': actions.filter(status='completed').count(),
                'failed': actions.filter(status='failed').count(),
            }

            return {
                'id': str(workflow.id),
                'name': workflow.name,
                'status': workflow.status,
                'progress_percentage': workflow.progress_percentage,
                'success_rate': workflow.success_rate,
                'total_accounts': workflow.total_accounts,
                'processed_accounts': workflow.processed_accounts,
                'successful_actions': workflow.successful_actions,
                'failed_actions': workflow.failed_actions,
                'action_stats': action_stats,
                'started_at': workflow.started_at.isoformat() if workflow.started_at else None,
                'completed_at': workflow.completed_at.isoformat() if workflow.completed_at else None,
            }

        except Workflow.DoesNotExist:
            return {'error': 'Workflow not found'}
        except Exception as e:
            logger.error(f"Error getting workflow status {workflow_id}: {str(e)}")
            return {'error': str(e)}
