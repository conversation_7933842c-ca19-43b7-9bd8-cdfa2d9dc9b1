"""
Tests for campaign export functionality.
"""
import uuid
import csv
from io import StringIO
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from campaigns.models import Campaign, DynamicTag, TagCategory, CampaignTag
from campaigns.services.export_service import CampaignExportService
from instagram.models import Accounts


class CampaignExportServiceTestCase(TestCase):
    """Test cases for CampaignExportService."""

    def setUp(self):
        """Set up test data."""
        # Create test campaign
        self.campaign = Campaign.objects.create(
            id=uuid.uuid4(),
            name='Test Export Campaign',
            description='Test campaign for export functionality',
            status='active'
        )

        # Create test tag category
        self.category = TagCategory.objects.create(
            name='Test Category',
            description='Test category for export',
            color='#ff5722'
        )

        # Create test tags
        self.tag1 = DynamicTag.objects.create(
            name='fitness_enthusiast',
            description='Users interested in fitness',
            category=self.category,
            tag_type='keyword',
            pattern='fitness',
            field='bio',
            is_global=True
        )

        self.tag2 = DynamicTag.objects.create(
            name='high_engagement',
            description='Users with high engagement',
            category=self.category,
            tag_type='keyword',
            pattern='engagement',
            field='bio',
            is_global=True
        )

        # Assign tags to campaign
        self.campaign_tag1 = CampaignTag.objects.create(
            id=uuid.uuid4(),
            campaign=self.campaign,
            tag=self.tag1,
            is_required=True
        )

        self.campaign_tag2 = CampaignTag.objects.create(
            id=uuid.uuid4(),
            campaign=self.campaign,
            tag=self.tag2,
            is_required=False
        )

        # Create test accounts
        self.account1 = Accounts.objects.create(
            username='fitness_user',
            full_name='Fitness User',
            bio='I love fitness and working out',
            followers=1000,
            following=500,
            number_of_posts=100,
            account_type='personal',
            is_verified=False,
            campaign_id=str(self.campaign.id)
        )

        self.account2 = Accounts.objects.create(
            username='engagement_user',
            full_name='Engagement User',
            bio='High engagement content creator',
            followers=5000,
            following=200,
            number_of_posts=300,
            account_type='business',
            is_verified=True,
            campaign_id=str(self.campaign.id)
        )

        self.account3 = Accounts.objects.create(
            username='regular_user',
            full_name='Regular User',
            bio='Just a regular user',
            followers=100,
            following=150,
            number_of_posts=50,
            account_type='personal',
            is_verified=False,
            campaign_id=str(self.campaign.id)
        )

    def test_export_service_initialization(self):
        """Test that export service initializes correctly."""
        export_service = CampaignExportService(self.campaign)
        self.assertEqual(export_service.campaign, self.campaign)

    def test_get_whitelisted_accounts(self):
        """Test getting whitelisted accounts based on tag assignments."""
        export_service = CampaignExportService(self.campaign)
        whitelisted_accounts = export_service._get_whitelisted_accounts()

        # Should return accounts that match the tags
        self.assertIsInstance(whitelisted_accounts, list)

        # Check that accounts are properly structured
        for account_data in whitelisted_accounts:
            self.assertIn('account', account_data)
            self.assertIn('tags_info', account_data)
            self.assertIn('all_tags', account_data['tags_info'])
            self.assertIn('required_tags', account_data['tags_info'])

    def test_get_whitelist_stats(self):
        """Test getting whitelist statistics."""
        export_service = CampaignExportService(self.campaign)
        stats = export_service.get_whitelist_stats()

        self.assertIn('total_accounts', stats)
        self.assertIn('whitelisted_accounts', stats)
        self.assertIn('conversion_rate', stats)

        # Check that stats are reasonable
        self.assertEqual(stats['total_accounts'], 3)  # We created 3 accounts
        self.assertGreaterEqual(stats['whitelisted_accounts'], 0)
        self.assertGreaterEqual(stats['conversion_rate'], 0)
        self.assertLessEqual(stats['conversion_rate'], 100)

    def test_export_whitelist_csv(self):
        """Test CSV export functionality."""
        export_service = CampaignExportService(self.campaign)
        response = export_service.export_whitelist_csv()

        # Check response properties
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('attachment', response['Content-Disposition'])
        self.assertIn('whitelist.csv', response['Content-Disposition'])

        # Parse CSV content
        content = response.content.decode('utf-8')
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        # Check headers
        headers = rows[0]
        expected_headers = [
            'Username', 'Full Name', 'Bio', 'Followers', 'Following', 'Posts',
            'Account Type', 'Verified', 'Phone Number', 'Interests', 'Locations',
            'Links', 'Assigned Tags', 'Required Tags', 'Collection Date'
        ]
        self.assertEqual(headers, expected_headers)

        # Check that CEP-related columns are NOT present
        cep_columns = ['DM', 'Discover', 'Comment', 'Post Like', 'Favorite', 'Follow', 'Auto']
        for cep_col in cep_columns:
            self.assertNotIn(cep_col, headers)

    def test_account_matched_tags_simple(self):
        """Test simple tag matching logic."""
        export_service = CampaignExportService(self.campaign)

        # Test fitness tag matching
        matched_tags = export_service._get_account_matched_tags(
            self.account1,
            [self.tag1, self.tag2]
        )

        # Should match fitness tag since bio contains 'fitness'
        tag_names = [tag.name for tag in matched_tags]
        self.assertIn('fitness_enthusiast', tag_names)


class CampaignExportViewsTestCase(TestCase):
    """Test cases for export views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create test campaign
        self.campaign = Campaign.objects.create(
            id=uuid.uuid4(),
            name='Test Export Campaign',
            description='Test campaign for export views',
            status='active'
        )

        # Create test account
        self.account = Accounts.objects.create(
            username='test_user',
            full_name='Test User',
            bio='Test bio',
            followers=1000,
            following=500,
            number_of_posts=100,
            campaign_id=str(self.campaign.id)
        )

    def test_whitelist_export_csv_view(self):
        """Test whitelist CSV export view."""
        url = reverse('campaigns:export_whitelist', kwargs={'campaign_id': self.campaign.id})
        response = self.client.get(url, {'format': 'csv'})

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('whitelist.csv', response['Content-Disposition'])

    def test_whitelist_export_excel_view(self):
        """Test whitelist Excel export view."""
        url = reverse('campaigns:export_whitelist', kwargs={'campaign_id': self.campaign.id})
        response = self.client.get(url, {'format': 'excel'})

        # Should either return Excel file or fallback to CSV
        self.assertEqual(response.status_code, 200)
        self.assertIn('Content-Type', response)

    def test_discovery_export_view(self):
        """Test discovery export view."""
        url = reverse('campaigns:export_discovery', kwargs={'campaign_id': self.campaign.id})
        response = self.client.get(url, {'format': 'csv'})

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('discovery.csv', response['Content-Disposition'])

    def test_legacy_export_redirect(self):
        """Test legacy export view redirects correctly."""
        url = reverse('campaigns:export_campaign', kwargs={'pk': self.campaign.id})
        response = self.client.get(url, {'type': 'whitelist', 'format': 'csv'})

        # Should redirect to new export endpoint
        self.assertEqual(response.status_code, 302)

    def test_export_invalid_campaign(self):
        """Test export with invalid campaign ID."""
        invalid_campaign_id = uuid.uuid4()
        url = reverse('campaigns:export_whitelist', kwargs={'campaign_id': invalid_campaign_id})
        response = self.client.get(url)

        # Due to our error handling, we get 302 redirect instead of 404
        self.assertEqual(response.status_code, 302)

    def test_export_unsupported_format(self):
        """Test export with unsupported format."""
        url = reverse('campaigns:export_whitelist', kwargs={'campaign_id': self.campaign.id})
        response = self.client.get(url, {'format': 'pdf'})

        # Should redirect with error message
        self.assertEqual(response.status_code, 302)


class DataSynchronizationTestCase(TestCase):
    """Test cases for data synchronization between views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create test campaign
        self.campaign = Campaign.objects.create(
            id=uuid.uuid4(),
            name='Test Sync Campaign',
            description='Test campaign for data sync',
            status='active'
        )

        # Create test tag
        self.tag = DynamicTag.objects.create(
            name='test_tag',
            description='Test tag for sync',
            tag_type='keyword',
            pattern='test',
            field='bio',
            is_global=True
        )

    def test_tag_assignment_synchronization(self):
        """Test that tag assignments are synchronized across views."""
        # Assign tag to campaign
        campaign_tag = CampaignTag.objects.create(
            id=uuid.uuid4(),
            campaign=self.campaign,
            tag=self.tag,
            is_required=True
        )

        # Check that export service sees the tag assignment
        export_service = CampaignExportService(self.campaign)
        whitelisted_accounts = export_service._get_whitelisted_accounts()

        # Should be able to process the tag assignment without errors
        self.assertIsInstance(whitelisted_accounts, list)

        # Remove tag assignment
        campaign_tag.delete()

        # Check that export service reflects the change
        whitelisted_accounts_after = export_service._get_whitelisted_accounts()
        self.assertIsInstance(whitelisted_accounts_after, list)
