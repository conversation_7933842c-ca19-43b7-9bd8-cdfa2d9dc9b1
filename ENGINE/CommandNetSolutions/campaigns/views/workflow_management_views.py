"""
Views for Workflow (Customer Engagement Process) management.

This module provides views for managing workflow executions, including:
- Workflow dashboard
- Workflow creation with templates
- Workflow status updates
- Workflow monitoring
"""
import json
import logging
from django.views.generic import ListView, DetailView, CreateView, UpdateView, TemplateView, View
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse, reverse_lazy
from django.http import JsonResponse, HttpResponseRedirect
from django.contrib import messages
from django.db.models import Q
from django.utils import timezone

from campaigns.models import Campaign
from campaigns.models.cep import CEPWorkflow  # Keep for backward compatibility
from campaigns.models.workflow import WorkflowTemplate
from campaigns.forms.cep_forms import CEPWorkflowForm

logger = logging.getLogger(__name__)


class WorkflowDashboardView(TemplateView):
    """
    Dashboard view for workflow executions.
    """
    template_name = 'campaigns/workflows/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        try:
            # Get active workflow execution (using CEPWorkflow for now)
            active_workflow = CEPWorkflow.objects.filter(
                status__in=['pending', 'running', 'paused']
            ).first()

            # Get completed workflow executions (limited to 5)
            completed_workflows = CEPWorkflow.objects.filter(
                status__in=['completed', 'failed', 'stopped']
            ).order_by('-completed_at')[:5]

            # Get campaigns with whitelists that are not in workflow
            from instagram.models import WhiteListEntry

            available_campaigns = []
            campaigns = Campaign.objects.filter(
                is_in_cep=False,
                status__in=['completed', 'stopped']
            ).order_by('-created_at')

            for campaign in campaigns:
                # Get whitelist count for each campaign
                try:
                    whitelist_count = WhiteListEntry.objects.filter(
                        account__campaign_id=str(campaign.id)
                    ).count()

                    if whitelist_count > 0:
                        campaign.whitelist_count = whitelist_count
                        available_campaigns.append(campaign)
                except Exception as e:
                    logger.warning(f"Error getting whitelist count for campaign {campaign.id}: {e}")
                    continue

            # Get workflow templates
            workflow_templates = WorkflowTemplate.objects.filter(
                is_active=True
            ).order_by('category', 'name')

            # Add to context
            context['active_workflow'] = active_workflow
            context['completed_workflows'] = completed_workflows
            context['available_campaigns'] = available_campaigns
            context['workflow_templates'] = workflow_templates

            logger.info(f"Workflow dashboard context: active={bool(active_workflow)}, "
                       f"completed={completed_workflows.count()}, "
                       f"available_campaigns={len(available_campaigns)}, "
                       f"templates={workflow_templates.count()}")

        except Exception as e:
            logger.error(f"Error in WorkflowDashboardView.get_context_data: {e}")
            # Provide empty context to prevent template errors
            context.update({
                'active_workflow': None,
                'completed_workflows': [],
                'available_campaigns': [],
                'workflow_templates': []
            })

        return context


class WorkflowCreateView(View):
    """
    View to create a new workflow execution using templates.
    """
    def get(self, request):
        """
        Display the workflow creation interface.
        """
        # Get campaigns with whitelists that are not in workflow
        from instagram.models import WhiteListEntry

        available_campaigns = []
        campaigns = Campaign.objects.filter(
            is_in_cep=False,
            status__in=['completed', 'stopped']
        ).order_by('-created_at')

        for campaign in campaigns:
            # Get whitelist count for each campaign
            whitelist_count = WhiteListEntry.objects.filter(
                account__campaign_id=str(campaign.id)
            ).count()

            if whitelist_count > 0:
                campaign.whitelist_count = whitelist_count
                available_campaigns.append(campaign)

        # Get workflow templates
        workflow_templates = WorkflowTemplate.objects.filter(
            is_active=True
        ).order_by('category', 'name')

        context = {
            'available_campaigns': available_campaigns,
            'workflow_templates': workflow_templates,
        }

        return render(request, 'campaigns/workflows/create.html', context)

    def post(self, request):
        """
        Handle workflow creation form submission.
        """
        try:
            # Get form data
            campaign_id = request.POST.get('campaign_id')
            template_id = request.POST.get('template_id')
            workflow_name = request.POST.get('workflow_name')
            workflow_description = request.POST.get('workflow_description', '')
            enabled_actions = request.POST.getlist('enabled_actions')
            daily_limit = int(request.POST.get('daily_limit', 50))
            hourly_limit = int(request.POST.get('hourly_limit', 10))
            delay_between_actions = int(request.POST.get('delay_between_actions', 30))

            # Validate required fields
            if not all([campaign_id, template_id, workflow_name]):
                messages.error(request, 'Please fill in all required fields.')
                return redirect('campaigns:workflow_create')

            # Get campaign and template
            campaign = get_object_or_404(Campaign, id=campaign_id)
            template = get_object_or_404(WorkflowTemplate, id=template_id)

            # Create workflow execution (using CEPWorkflow for now)
            workflow = CEPWorkflow.objects.create(
                campaign=campaign,
                name=workflow_name,
                description=workflow_description,
                selected_workflows=enabled_actions,
                daily_limit=daily_limit,
                delay_between_actions=delay_between_actions,
                status='pending'
            )

            messages.success(request, f'Workflow "{workflow_name}" created successfully!')
            return redirect('campaigns:workflow_detail', pk=workflow.id)

        except Exception as e:
            logger.error(f"Error creating workflow: {str(e)}")
            messages.error(request, f'Error creating workflow: {str(e)}')
            return redirect('campaigns:workflow_create')


class WorkflowDetailView(DetailView):
    """
    Detail view for a specific workflow execution.
    """
    model = CEPWorkflow  # Using CEPWorkflow for now
    template_name = 'campaigns/workflows/detail.html'
    context_object_name = 'workflow'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get workflow
        workflow = self.object

        # Add campaign to context
        context['campaign'] = workflow.campaign

        # Add template information if available
        if hasattr(workflow, 'template'):
            context['template'] = workflow.template
        else:
            # For backward compatibility, create a mock template
            context['template'] = {
                'name': 'Legacy Workflow',
                'description': 'Legacy workflow configuration',
                'category': 'custom'
            }

        return context


class WorkflowStartView(View):
    """
    View to start a workflow execution.
    """
    def post(self, request, pk):
        """Start the workflow execution."""
        workflow = get_object_or_404(CEPWorkflow, pk=pk)
        
        if workflow.start():
            messages.success(request, f'Workflow "{workflow.name}" started successfully!')
        else:
            messages.error(request, 'Unable to start workflow. Check workflow status.')
        
        return redirect('campaigns:workflow_detail', pk=pk)


class WorkflowPauseView(View):
    """
    View to pause a workflow execution.
    """
    def post(self, request, pk):
        """Pause the workflow execution."""
        workflow = get_object_or_404(CEPWorkflow, pk=pk)
        
        if workflow.pause():
            messages.success(request, f'Workflow "{workflow.name}" paused successfully!')
        else:
            messages.error(request, 'Unable to pause workflow. Check workflow status.')
        
        return redirect('campaigns:workflow_detail', pk=pk)


class WorkflowResumeView(View):
    """
    View to resume a workflow execution.
    """
    def post(self, request, pk):
        """Resume the workflow execution."""
        workflow = get_object_or_404(CEPWorkflow, pk=pk)
        
        if workflow.resume():
            messages.success(request, f'Workflow "{workflow.name}" resumed successfully!')
        else:
            messages.error(request, 'Unable to resume workflow. Check workflow status.')
        
        return redirect('campaigns:workflow_detail', pk=pk)


class WorkflowStopView(View):
    """
    View to stop a workflow execution.
    """
    def post(self, request, pk):
        """Stop the workflow execution."""
        workflow = get_object_or_404(CEPWorkflow, pk=pk)
        
        if workflow.stop():
            messages.success(request, f'Workflow "{workflow.name}" stopped successfully!')
        else:
            messages.error(request, 'Unable to stop workflow. Check workflow status.')
        
        return redirect('campaigns:workflow_detail', pk=pk)


class WorkflowStatusAPIView(View):
    """
    API view to get workflow execution status.
    """
    def get(self, request, pk):
        """Get workflow status as JSON."""
        try:
            workflow = get_object_or_404(CEPWorkflow, pk=pk)
            
            return JsonResponse({
                'success': True,
                'workflow': {
                    'id': str(workflow.id),
                    'name': workflow.name,
                    'status': workflow.status,
                    'progress': workflow.overall_progress,
                    'started_at': workflow.started_at.isoformat() if workflow.started_at else None,
                    'completed_at': workflow.completed_at.isoformat() if workflow.completed_at else None,
                    'whitelist_count': workflow.whitelist_count,
                    'selected_workflows': workflow.selected_workflows,
                    'daily_limit': workflow.daily_limit,
                    'delay_between_actions': workflow.delay_between_actions
                }
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)


class WorkflowTemplateListView(ListView):
    """
    List view for workflow templates.
    """
    model = WorkflowTemplate
    template_name = 'campaigns/workflows/templates.html'
    context_object_name = 'templates'
    
    def get_queryset(self):
        return WorkflowTemplate.objects.filter(is_active=True).order_by('category', 'name')


class WorkflowTemplateDetailView(DetailView):
    """
    Detail view for a workflow template.
    """
    model = WorkflowTemplate
    template_name = 'campaigns/workflows/template_detail.html'
    context_object_name = 'template'
