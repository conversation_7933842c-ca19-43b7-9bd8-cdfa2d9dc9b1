"""
Views for CEP (Customer Engagement Process) workflow management.

This module provides views for managing CEP workflows, including:
- CEP workflow dashboard
- CEP workflow creation
- CEP workflow status updates
- CEP workflow monitoring
"""
import json
import logging
from django.views.generic import ListView, DetailView, CreateView, UpdateView, TemplateView, View
# Removed LoginRequiredMixin to disable authentication requirements
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse, reverse_lazy
from django.http import JsonResponse, HttpResponseRedirect
from django.contrib import messages
from django.db.models import Q
from django.utils import timezone

from campaigns.models import Campaign
from campaigns.models.cep import CEPWorkflow
from campaigns.forms.cep_forms import CEPWorkflowForm

logger = logging.getLogger(__name__)

class CEPDashboardView(TemplateView):
    """
    Dashboard view for CEP workflows.
    """
    template_name = 'campaigns/cep/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get active CEP workflow
        active_workflow = CEPWorkflow.objects.filter(
            status__in=['pending', 'running', 'paused']
        ).first()

        # Get completed CEP workflows (limited to 5)
        completed_workflows = CEPWorkflow.objects.filter(
            status__in=['completed', 'failed', 'stopped']
        ).order_by('-completed_at')[:5]

        # Get campaigns with whitelists that are not in CEP
        available_campaigns = Campaign.objects.filter(
            is_in_cep=False,
            status__in=['completed', 'stopped']
        ).order_by('-created_at')

        # Add to context
        context['active_workflow'] = active_workflow
        context['completed_workflows'] = completed_workflows
        context['available_campaigns'] = available_campaigns

        # Add subscription tier options
        context['subscription_tiers'] = [
            {'value': 'bronze', 'label': 'Bronze', 'description': 'Basic follow and like actions'},
            {'value': 'silver', 'label': 'Silver', 'description': 'Follow, like, and comment actions'},
            {'value': 'gold', 'label': 'Gold', 'description': 'All engagement actions (follow, like, comment, DM)'}
        ]

        return context

class CEPWorkflowCreateView(View):
    """
    View to create a new CEP workflow using the wizard interface.
    """
    def get(self, request):
        """
        Display the CEP workflow creation wizard.
        """
        # Get campaigns with whitelists that are not in CEP
        from instagram.models import WhiteListEntry

        available_campaigns = []
        campaigns = Campaign.objects.filter(
            is_in_cep=False,
            status__in=['completed', 'stopped']
        ).order_by('-created_at')

        for campaign in campaigns:
            # Get whitelist count for each campaign
            whitelist_count = WhiteListEntry.objects.filter(
                account__campaign_id=str(campaign.id)
            ).count()

            if whitelist_count > 0:
                campaign.whitelist_count = whitelist_count
                available_campaigns.append(campaign)

        # Check if there's already an active CEP workflow
        active_workflow = CEPWorkflow.objects.filter(
            status__in=['pending', 'running', 'paused']
        ).first()

        return render(request, 'campaigns/cep/create_wizard.html', {
            'available_campaigns': available_campaigns,
            'active_workflow': active_workflow
        })

    def post(self, request):
        """
        Handle CEP workflow creation from the wizard.
        """
        try:
            # Get form data
            campaign_id = request.POST.get('campaign')
            workflow_name = request.POST.get('workflow_name')
            workflow_description = request.POST.get('workflow_description', '')
            selected_workflows = request.POST.getlist('workflows')
            daily_limit = int(request.POST.get('daily_limit', 50))
            delay_between_actions = int(request.POST.get('delay_between_actions', 30))

            # Validate campaign
            campaign = get_object_or_404(Campaign, id=campaign_id)

            # Check if campaign is already in CEP
            if campaign.is_in_cep:
                messages.error(request, "This campaign is already in a CEP workflow.")
                return redirect('campaigns:cep_create')

            # Check if there's already an active CEP workflow
            active_workflow = CEPWorkflow.objects.filter(
                status__in=['pending', 'running', 'paused']
            ).first()

            if active_workflow:
                messages.error(request, "Only one active CEP workflow is allowed at a time.")
                return redirect('campaigns:cep_create')

            # Create CEP workflow with new fields
            workflow = CEPWorkflow.objects.create(
                campaign=campaign,
                name=workflow_name,
                description=workflow_description,
                selected_workflows=selected_workflows,
                daily_limit=daily_limit,
                delay_between_actions=delay_between_actions,
                status='pending'
            )

            # Mark campaign as in CEP
            campaign.is_in_cep = True
            campaign.save()

            messages.success(request, f"CEP workflow '{workflow_name}' created successfully!")
            return redirect('campaigns:cep_detail', pk=workflow.id)

        except Exception as e:
            logger.exception(f"Error creating CEP workflow: {str(e)}")
            messages.error(request, f"Error creating CEP workflow: {str(e)}")
            return redirect('campaigns:cep_create')

class CEPWorkflowDetailView(DetailView):
    """
    View to display CEP workflow details.
    """
    model = CEPWorkflow
    template_name = 'campaigns/cep/detail.html'
    context_object_name = 'workflow'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get workflow
        workflow = self.object

        # Add campaign to context
        context['campaign'] = workflow.campaign

        # Add subscription tier info
        subscription_tiers = {
            'bronze': {
                'label': 'Bronze',
                'description': 'Basic follow and like actions',
                'actions': ['follow', 'like']
            },
            'silver': {
                'label': 'Silver',
                'description': 'Follow, like, and comment actions',
                'actions': ['follow', 'like', 'comment']
            },
            'gold': {
                'label': 'Gold',
                'description': 'All engagement actions (follow, like, comment, DM)',
                'actions': ['follow', 'like', 'comment', 'dm']
            }
        }

        context['tier_info'] = subscription_tiers.get(workflow.subscription_tier, {})

        # Calculate overall progress
        context['overall_progress'] = workflow.overall_progress

        return context

class CEPWorkflowStartView(View):
    """
    View to start a CEP workflow.
    """
    def post(self, request, pk):
        """
        Process the request to start a CEP workflow.
        """
        # Get workflow
        workflow = get_object_or_404(CEPWorkflow, pk=pk)

        try:
            # Check if workflow can be started
            if workflow.status != 'pending':
                messages.error(request, f"Cannot start workflow. Current status: {workflow.get_status_display()}")
                return redirect('campaigns:cep_detail', pk=workflow.id)

            # Import airflow client
            from airflow.api.client.local_client import Client

            # Create client
            client = Client(None, None)

            # Determine workflow type based on subscription tier
            workflow_type = 'all'  # Default to 'all' for Gold tier

            if workflow.subscription_tier == 'bronze':
                workflow_type = 'follow'  # Start with follow for Bronze tier
            elif workflow.subscription_tier == 'silver':
                workflow_type = 'follow'  # Start with follow for Silver tier

            # Create DAG run configuration
            conf = {
                'campaign_id': str(workflow.campaign.id),
                'subscription_tier': workflow.subscription_tier,
                'workflow_type': workflow_type,
                'batch_size': 10,
                'delay_between_actions': 60
            }

            # Trigger DAG run
            dag_run = client.trigger_dag(
                dag_id='campaign_cep',
                run_id=f"cep_{workflow.id}_{timezone.now().strftime('%Y%m%d_%H%M%S')}",
                conf=conf
            )

            # Update workflow status
            workflow.status = 'running'
            workflow.started_at = timezone.now()
            workflow.save()

            # Add success message
            messages.success(request, f"CEP workflow started successfully for campaign '{workflow.campaign.name}'.")

            # Redirect to CEP workflow detail page
            return redirect('campaigns:cep_detail', pk=workflow.id)

        except Exception as e:
            logger.exception(f"Error starting CEP workflow: {str(e)}")
            messages.error(request, f"Error starting CEP workflow: {str(e)}")
            return redirect('campaigns:cep_detail', pk=workflow.id)

class CEPWorkflowPauseView(View):
    """
    View to pause a CEP workflow.
    """
    def post(self, request, pk):
        """
        Process the request to pause a CEP workflow.
        """
        # Get workflow
        workflow = get_object_or_404(CEPWorkflow, pk=pk)

        try:
            # Check if workflow can be paused
            if workflow.status != 'running':
                messages.error(request, f"Cannot pause workflow. Current status: {workflow.get_status_display()}")
                return redirect('campaigns:cep_detail', pk=workflow.id)

            # Update workflow status
            workflow.status = 'paused'
            workflow.save()

            # Add success message
            messages.success(request, f"CEP workflow paused successfully for campaign '{workflow.campaign.name}'.")

            # Redirect to CEP workflow detail page
            return redirect('campaigns:cep_detail', pk=workflow.id)

        except Exception as e:
            logger.exception(f"Error pausing CEP workflow: {str(e)}")
            messages.error(request, f"Error pausing CEP workflow: {str(e)}")
            return redirect('campaigns:cep_detail', pk=workflow.id)

class CEPWorkflowResumeView(View):
    """
    View to resume a CEP workflow.
    """
    def post(self, request, pk):
        """
        Process the request to resume a CEP workflow.
        """
        # Get workflow
        workflow = get_object_or_404(CEPWorkflow, pk=pk)

        try:
            # Check if workflow can be resumed
            if workflow.status != 'paused':
                messages.error(request, f"Cannot resume workflow. Current status: {workflow.get_status_display()}")
                return redirect('campaigns:cep_detail', pk=workflow.id)

            # Update workflow status
            workflow.status = 'running'
            workflow.save()

            # Add success message
            messages.success(request, f"CEP workflow resumed successfully for campaign '{workflow.campaign.name}'.")

            # Redirect to CEP workflow detail page
            return redirect('campaigns:cep_detail', pk=workflow.id)

        except Exception as e:
            logger.exception(f"Error resuming CEP workflow: {str(e)}")
            messages.error(request, f"Error resuming CEP workflow: {str(e)}")
            return redirect('campaigns:cep_detail', pk=workflow.id)

class CEPWorkflowStopView(View):
    """
    View to stop a CEP workflow.
    """
    def post(self, request, pk):
        """
        Process the request to stop a CEP workflow.
        """
        # Get workflow
        workflow = get_object_or_404(CEPWorkflow, pk=pk)

        try:
            # Check if workflow can be stopped
            if workflow.status not in ['pending', 'running', 'paused']:
                messages.error(request, f"Cannot stop workflow. Current status: {workflow.get_status_display()}")
                return redirect('campaigns:cep_detail', pk=workflow.id)

            # Update workflow status
            workflow.status = 'stopped'
            workflow.completed_at = timezone.now()
            workflow.save()

            # Update campaign is_in_cep flag
            campaign = workflow.campaign
            campaign.is_in_cep = False
            campaign.save(update_fields=['is_in_cep', 'updated_at'])

            # Add success message
            messages.success(request, f"CEP workflow stopped successfully for campaign '{workflow.campaign.name}'.")

            # Redirect to CEP dashboard
            return redirect('campaigns:workflow_dashboard')

        except Exception as e:
            logger.exception(f"Error stopping CEP workflow: {str(e)}")
            messages.error(request, f"Error stopping CEP workflow: {str(e)}")
            return redirect('campaigns:cep_detail', pk=workflow.id)

class CEPWorkflowStatusAPIView(View):
    """
    API view to get CEP workflow status.
    """
    def get(self, request, pk):
        """
        Get CEP workflow status.
        """
        try:
            # Get workflow
            workflow = get_object_or_404(CEPWorkflow, pk=pk)

            # Return workflow status
            return JsonResponse({
                'id': str(workflow.id),
                'status': workflow.status,
                'status_display': workflow.get_status_display(),
                'follow_progress': workflow.follow_progress,
                'like_progress': workflow.like_progress,
                'comment_progress': workflow.comment_progress,
                'dm_progress': workflow.dm_progress,
                'overall_progress': workflow.overall_progress,
                'whitelist_count': workflow.whitelist_count,
                'started_at': workflow.started_at.isoformat() if workflow.started_at else None,
                'completed_at': workflow.completed_at.isoformat() if workflow.completed_at else None,
                'updated_at': workflow.updated_at.isoformat()
            })

        except Exception as e:
            logger.exception(f"Error getting CEP workflow status: {str(e)}")
            return JsonResponse({
                'error': str(e)
            }, status=500)
