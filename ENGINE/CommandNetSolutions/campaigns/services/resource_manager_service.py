"""
Bot Worker Resource Manager Service for PyFlow workflows.

This service provides methods for:
1. Managing exclusive access to the bot worker for PyFlow workflows
2. Implementing a queue system for workflows waiting for the bot worker
3. Creating a prioritization framework for workflow scheduling
4. Implementing conflict resolution for Instagram API rate limits
5. Ensuring system stability through monitoring and recovery mechanisms
6. Maintaining compatibility with existing progress tracking infrastructure
"""
import logging
import time
import threading
import redis
import json
import os
import uuid
import heapq
from datetime import datetime, timedelta
from django.conf import settings
from django.db import transaction
from django.utils import timezone
from django.core.cache import cache

from campaigns.models.workflow import WorkflowExecution
from campaigns.models import Campaign

logger = logging.getLogger(__name__)

# Constants for resource management
DEFAULT_NETWORK_LIMIT = 100  # Requests per minute
DEFAULT_MAX_QUEUE_SIZE = 50  # Maximum number of workflows in the queue

# System priority modes
PRIORITY_MODES = {
    'dmp': 'Data Collection Priority',  # Prioritize data mining and collection
    'cep': 'Action Execution Priority'  # Prioritize customer engagement actions
}

# Workflow type priorities based on system priority mode
WORKFLOW_PRIORITIES = {
    'dmp': {  # Data Collection Priority mode
        'collection': 10,  # Collection workflows have highest priority
        'analysis': 9,     # Analysis workflows have second highest priority
        'tagging': 8,
        'cep': 7,          # CEP workflows have lower priority
        'follow': 5,
        'like': 4,
        'comment': 3,
        'dm': 2
    },
    'cep': {  # Action Execution Priority mode
        'cep': 10,         # CEP workflows have highest priority
        'follow': 9,
        'like': 8,
        'comment': 7,
        'dm': 6,
        'collection': 5,   # Collection workflows have lower priority
        'analysis': 4,
        'tagging': 3
    }
}

# API rate limit settings (requests per time window)
API_RATE_LIMITS = {
    'default': {'limit': 200, 'window': 3600},  # 200 requests per hour
    'follow': {'limit': 60, 'window': 3600},    # 60 follows per hour
    'like': {'limit': 120, 'window': 3600},     # 120 likes per hour
    'comment': {'limit': 20, 'window': 3600},   # 20 comments per hour
    'dm': {'limit': 20, 'window': 3600}         # 20 DMs per hour
}

# Priority queue entry for workflow scheduling
class WorkflowQueueEntry:
    """
    Entry in the workflow priority queue.

    Implements comparison methods for priority queue ordering.
    Higher priority workflows come first, and for equal priorities,
    earlier submission time comes first (FIFO within same priority).
    """
    def __init__(self, workflow_id, priority, submission_time):
        self.workflow_id = workflow_id
        self.priority = priority
        self.submission_time = submission_time

    def __lt__(self, other):
        # Higher priority comes first
        if self.priority != other.priority:
            return self.priority > other.priority
        # For same priority, earlier submission time comes first
        return self.submission_time < other.submission_time

    def __eq__(self, other):
        return (self.priority == other.priority and
                self.submission_time == other.submission_time)

class ResourceManagerService:
    """
    Service for managing bot worker access for PyFlow workflows.

    This service provides methods for managing exclusive access to the bot worker,
    implementing a queue system, and creating a prioritization framework.
    """

    def __init__(self):
        """Initialize the resource manager service."""
        # Load configuration first to get Redis settings
        self.load_configuration()

        # Initialize Redis client with configurable settings
        self.redis_client = self._create_redis_client()

        self.running_workflows = {}  # All registered workflows
        self.active_workflow_id = None  # Currently active workflow using the bot worker
        self.workflow_queue = []  # Priority queue of waiting workflows
        self.lock = threading.RLock()
        self.scheduler_thread = None
        self.stop_scheduler = False

        # Start scheduler thread
        self.start_scheduler()

    def _create_redis_client(self):
        """
        Create a Redis client using settings from Django configuration.

        Returns:
            redis.Redis: Configured Redis client
        """
        try:
            # Check if Redis is enabled
            redis_enabled = getattr(settings, 'REDIS_ENABLED', True)
            if not redis_enabled:
                logger.warning("Redis is disabled in settings. Some features may not work properly.")
                # Return a dummy Redis client that will raise ConnectionError when used
                return redis.Redis(host='localhost', port=6379, db=0, socket_connect_timeout=1)

            # Create connection pool for better performance
            pool = redis.ConnectionPool(
                host=self.redis_config['host'],
                port=self.redis_config['port'],
                db=self.redis_config['db'],
                password=self.redis_config['password'],
                socket_timeout=self.redis_config['socket_timeout'],
                socket_connect_timeout=self.redis_config['socket_connect_timeout'],
                retry_on_timeout=self.redis_config['retry_on_timeout'],
                max_connections=self.redis_config['max_connections'],
                decode_responses=True  # Automatically decode responses to Python strings
            )

            # Create Redis client
            client = redis.Redis(connection_pool=pool)

            # Test connection
            client.ping()

            logger.info(f"Successfully connected to Redis at {self.redis_config['host']}:{self.redis_config['port']}/{self.redis_config['db']}")
            return client
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Redis connection error: {str(e)}")
            # Return a dummy Redis client that will raise ConnectionError when used
            # This allows the application to start even if Redis is not available
            return redis.Redis(host='localhost', port=6379, db=0, socket_connect_timeout=1)
        except Exception as e:
            logger.error(f"Error creating Redis client: {str(e)}")
            # Return a dummy Redis client that will raise ConnectionError when used
            # This allows the application to start even if Redis is not available
            return redis.Redis(host='localhost', port=6379, db=0, socket_connect_timeout=1)

    def load_configuration(self):
        """Load resource management configuration."""
        # Load from settings or use defaults
        self.network_limit = getattr(settings, 'RESOURCE_NETWORK_LIMIT', DEFAULT_NETWORK_LIMIT)
        self.max_queue_size = getattr(settings, 'RESOURCE_MAX_QUEUE_SIZE', DEFAULT_MAX_QUEUE_SIZE)

        # Load workflow priorities based on current priority mode
        self.priority_mode = self.get_priority_mode()
        self.workflow_priorities = WORKFLOW_PRIORITIES.get(self.priority_mode, WORKFLOW_PRIORITIES['cep'])

        # Load API rate limits
        self.api_rate_limits = getattr(settings, 'API_RATE_LIMITS', API_RATE_LIMITS)

        # Load Redis configuration
        self.redis_config = {
            'host': getattr(settings, 'REDIS_HOST', 'localhost'),
            'port': getattr(settings, 'REDIS_PORT', 6379),
            'db': getattr(settings, 'REDIS_DB', 0),
            'password': getattr(settings, 'REDIS_PASSWORD', None),
            'socket_timeout': getattr(settings, 'REDIS_SOCKET_TIMEOUT', 5),
            'socket_connect_timeout': getattr(settings, 'REDIS_SOCKET_CONNECT_TIMEOUT', 5),
            'retry_on_timeout': getattr(settings, 'REDIS_RETRY_ON_TIMEOUT', True),
            'max_connections': getattr(settings, 'REDIS_MAX_CONNECTIONS', 10)
        }

        logger.info(f"Bot worker resource manager configured with max queue size: {self.max_queue_size}")
        logger.info(f"Priority mode: {self.priority_mode} ({PRIORITY_MODES.get(self.priority_mode, 'Unknown')})")
        logger.info(f"Redis configured at {self.redis_config['host']}:{self.redis_config['port']}/{self.redis_config['db']}")

    def start_scheduler(self):
        """Start the workflow scheduler thread."""
        if self.scheduler_thread is None or not self.scheduler_thread.is_alive():
            self.stop_scheduler = False
            self.scheduler_thread = threading.Thread(
                target=self._schedule_workflows,
                daemon=True
            )
            self.scheduler_thread.start()
            logger.info("Workflow scheduler thread started")

    def stop_scheduler_thread(self):
        """Stop the workflow scheduler thread."""
        self.stop_scheduler = True
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
            logger.info("Workflow scheduler thread stopped")

    def _schedule_workflows(self):
        """
        Schedule workflows from the queue when the bot worker becomes available.

        This method runs in a separate thread and continuously checks if:
        1. The bot worker is available (no active workflow)
        2. There are workflows in the queue

        If both conditions are met, it dequeues the highest priority workflow
        and starts it.
        """
        while not self.stop_scheduler:
            try:
                with self.lock:
                    # Check if bot worker is available and queue is not empty
                    if self.active_workflow_id is None and self.workflow_queue:
                        # Get the highest priority workflow from the queue
                        next_workflow = heapq.heappop(self.workflow_queue)
                        workflow_id = next_workflow.workflow_id

                        # Check if workflow still exists
                        if workflow_id in self.running_workflows:
                            # Set as active workflow
                            self.active_workflow_id = workflow_id
                            self.running_workflows[workflow_id]['status'] = 'running'
                            self.running_workflows[workflow_id]['start_time'] = timezone.now()

                            # Send start signal via Redis
                            self.redis_client.publish(
                                f"workflow:{workflow_id}:control",
                                json.dumps({"action": "start"})
                            )

                            logger.info(f"Started workflow {workflow_id} from queue")

                # Sleep before next check
                time.sleep(1)
            except Exception as e:
                logger.exception(f"Error in workflow scheduler: {str(e)}")
                time.sleep(5)  # Sleep longer on error

    def _pause_workflow(self, workflow_id):
        """
        Pause a workflow by sending a pause signal.

        Args:
            workflow_id (str): Workflow execution ID
        """
        try:
            # Mark workflow as paused
            if workflow_id in self.running_workflows:
                self.running_workflows[workflow_id]['status'] = 'paused'
                self.running_workflows[workflow_id]['paused_at'] = timezone.now()

                # Send pause signal via Redis
                self.redis_client.publish(
                    f"workflow:{workflow_id}:control",
                    json.dumps({"action": "pause"})
                )

                # If this was the active workflow, release the bot worker
                if self.active_workflow_id == workflow_id:
                    self.active_workflow_id = None
                    logger.info(f"Released bot worker by pausing active workflow {workflow_id}")

                logger.info(f"Paused workflow {workflow_id}")
            else:
                logger.warning(f"Cannot pause workflow {workflow_id}: not found in running workflows")
        except Exception as e:
            logger.exception(f"Error pausing workflow {workflow_id}: {str(e)}")

    def _resume_workflow(self, workflow_id):
        """
        Resume a paused workflow by adding it back to the queue.

        Args:
            workflow_id (str): Workflow execution ID
        """
        try:
            # Check if workflow exists and is paused
            if workflow_id in self.running_workflows and self.running_workflows[workflow_id]['status'] == 'paused':
                # Mark workflow as queued
                self.running_workflows[workflow_id]['status'] = 'queued'
                self.running_workflows[workflow_id]['resumed_at'] = timezone.now()

                # Add to queue
                priority = self.running_workflows[workflow_id]['priority']
                queue_entry = WorkflowQueueEntry(
                    workflow_id=workflow_id,
                    priority=priority,
                    submission_time=timezone.now()
                )
                heapq.heappush(self.workflow_queue, queue_entry)

                logger.info(f"Resumed workflow {workflow_id} (added to queue)")
            else:
                logger.warning(f"Cannot resume workflow {workflow_id}: not found or not paused")
        except Exception as e:
            logger.exception(f"Error resuming workflow {workflow_id}: {str(e)}")

    def _update_workflow_stats(self):
        """Update statistics for running workflows."""
        try:
            # Get workflow executions
            workflow_executions = WorkflowExecution.objects.filter(
                status__in=['pending', 'running', 'paused']
            )

            with self.lock:
                # Update running workflows
                current_ids = set()
                for execution in workflow_executions:
                    workflow_id = str(execution.id)
                    current_ids.add(workflow_id)

                    # Add to running workflows if not already tracked
                    if workflow_id not in self.running_workflows:
                        # Determine status based on execution status
                        status = 'queued'
                        if execution.status == 'running' and self.active_workflow_id == workflow_id:
                            status = 'running'
                        elif execution.status == 'paused':
                            status = 'paused'

                        # Add to running workflows
                        self.running_workflows[workflow_id] = {
                            'id': workflow_id,
                            'campaign_id': str(execution.campaign_id),
                            'workflow_type': execution.workflow_type,
                            'priority': self.workflow_priorities.get(execution.workflow_type, 0),
                            'start_time': execution.start_time,
                            'status': status,
                            'api_usage': {}
                        }

                # Remove completed workflows
                for workflow_id in list(self.running_workflows.keys()):
                    if workflow_id not in current_ids:
                        # If this was the active workflow, release the bot worker
                        if self.active_workflow_id == workflow_id:
                            self.active_workflow_id = None

                        # Remove from running workflows
                        self.running_workflows.pop(workflow_id, None)
        except Exception as e:
            logger.exception(f"Error updating workflow stats: {str(e)}")

    def register_workflow(self, workflow_execution_id, workflow_type, campaign_id, priority=None):
        """
        Register a workflow with the resource manager.

        Args:
            workflow_execution_id (str): Workflow execution ID
            workflow_type (str): Type of workflow
            campaign_id (str): Campaign ID
            priority (int, optional): Custom priority (higher = more important)

        Returns:
            dict: Registration result with status and position in queue
        """
        with self.lock:
            # Check if workflow is already registered
            if workflow_execution_id in self.running_workflows:
                return {
                    'success': False,
                    'error': 'Workflow already registered',
                    'workflow_id': workflow_execution_id
                }

            # Check if queue is full
            if len(self.workflow_queue) >= self.max_queue_size:
                return {
                    'success': False,
                    'error': 'Workflow queue is full',
                    'workflow_id': workflow_execution_id
                }

            # Determine priority
            if priority is None:
                priority = self.workflow_priorities.get(workflow_type, 0)

            # Register workflow
            self.running_workflows[workflow_execution_id] = {
                'id': workflow_execution_id,
                'campaign_id': campaign_id,
                'workflow_type': workflow_type,
                'priority': priority,
                'submission_time': timezone.now(),
                'status': 'queued',
                'api_usage': {}
            }

            # Check if bot worker is available
            if self.active_workflow_id is None:
                # Set as active workflow
                self.active_workflow_id = workflow_execution_id
                self.running_workflows[workflow_execution_id]['status'] = 'running'
                self.running_workflows[workflow_execution_id]['start_time'] = timezone.now()

                logger.info(f"Registered workflow {workflow_execution_id} as active (bot worker available)")

                return {
                    'success': True,
                    'status': 'running',
                    'workflow_id': workflow_execution_id,
                    'message': 'Workflow started immediately (bot worker available)'
                }
            else:
                # Add to queue
                queue_entry = WorkflowQueueEntry(
                    workflow_id=workflow_execution_id,
                    priority=priority,
                    submission_time=timezone.now()
                )
                heapq.heappush(self.workflow_queue, queue_entry)

                # Calculate position in queue
                queue_position = 1
                for entry in self.workflow_queue:
                    if entry.workflow_id != workflow_execution_id:
                        if entry.priority > priority:
                            queue_position += 1

                logger.info(f"Registered workflow {workflow_execution_id} in queue (position {queue_position})")

                return {
                    'success': True,
                    'status': 'queued',
                    'workflow_id': workflow_execution_id,
                    'queue_position': queue_position,
                    'message': f'Workflow queued (position {queue_position})'
                }

    def _can_start_workflow(self, workflow_type):
        """
        Check if a new workflow can be started based on bot worker availability.

        Args:
            workflow_type (str): Type of workflow

        Returns:
            bool: True if workflow can start, False otherwise
        """
        # Check if bot worker is available
        if self.active_workflow_id is not None:
            logger.warning(f"Cannot start workflow: Bot worker is busy with workflow {self.active_workflow_id}")
            return False

        # Check API rate limits
        if not self._check_api_rate_limit(workflow_type):
            logger.warning(f"Cannot start workflow: API rate limit would be exceeded for {workflow_type}")
            return False

        return True

    def _check_api_rate_limit(self, workflow_type):
        """
        Check if starting a workflow would exceed API rate limits.

        Args:
            workflow_type (str): Type of workflow

        Returns:
            bool: True if within rate limits, False otherwise
        """
        # Get rate limit settings for this workflow type
        rate_limit = self.api_rate_limits.get(workflow_type, self.api_rate_limits['default'])
        limit = rate_limit['limit']
        window = rate_limit['window']

        # Get current usage from Redis
        key = f"api_rate:{workflow_type}"
        current_usage = self.redis_client.get(key)

        if current_usage:
            current_usage = int(current_usage)
            if current_usage >= limit:
                return False

        return True

    def track_api_usage(self, workflow_id, api_type, count=1):
        """
        Track API usage for rate limiting.

        Args:
            workflow_id (str): Workflow execution ID
            api_type (str): Type of API call (follow, like, comment, dm)
            count (int): Number of API calls

        Returns:
            bool: True if within rate limits, False if exceeded
        """
        with self.lock:
            # Update workflow API usage
            if workflow_id in self.running_workflows:
                if api_type not in self.running_workflows[workflow_id]['api_usage']:
                    self.running_workflows[workflow_id]['api_usage'][api_type] = 0
                self.running_workflows[workflow_id]['api_usage'][api_type] += count

            # Update Redis rate limit counter
            key = f"api_rate:{api_type}"

            # Get rate limit settings
            rate_limit = self.api_rate_limits.get(api_type, self.api_rate_limits['default'])
            limit = rate_limit['limit']
            window = rate_limit['window']

            # Increment counter
            current = self.redis_client.incr(key, count)

            # Set expiry if new key
            if current == count:
                self.redis_client.expire(key, window)

            # Check if limit exceeded
            if current > limit:
                logger.warning(f"API rate limit exceeded for {api_type}: {current}/{limit}")
                return False

            return True

    def get_workflow_status(self, workflow_id):
        """
        Get status of a workflow.

        Args:
            workflow_id (str): Workflow execution ID

        Returns:
            dict: Workflow status information
        """
        with self.lock:
            if workflow_id in self.running_workflows:
                return self.running_workflows[workflow_id]
            return None

    def check_redis_availability(self):
        """
        Check if Redis is available.

        Returns:
            bool: True if Redis is available, False otherwise
            str: Error message if Redis is not available, None otherwise
        """
        try:
            # Try to ping Redis
            self.redis_client.ping()
            return True, None
        except redis.exceptions.ConnectionError as e:
            return False, f"Redis connection error: {str(e)}"
        except Exception as e:
            return False, f"Redis error: {str(e)}"

    def get_system_resources(self):
        """
        Get current bot worker status and workflow queue information.

        Returns:
            dict: Bot worker status and queue information
        """
        with self.lock:
            # Check Redis availability
            redis_available, redis_error = self.check_redis_availability()

            # Get queue statistics
            queue_by_priority = {}
            for entry in self.workflow_queue:
                priority = entry.priority
                if priority not in queue_by_priority:
                    queue_by_priority[priority] = 0
                queue_by_priority[priority] += 1

            # Get queue by workflow type
            queue_by_type = {}
            for entry in self.workflow_queue:
                workflow_id = entry.workflow_id
                if workflow_id in self.running_workflows:
                    workflow_type = self.running_workflows[workflow_id]['workflow_type']
                    if workflow_type not in queue_by_type:
                        queue_by_type[workflow_type] = 0
                    queue_by_type[workflow_type] += 1

            # Get active workflow info
            active_workflow = None
            if self.active_workflow_id and self.active_workflow_id in self.running_workflows:
                active_workflow = {
                    'id': self.active_workflow_id,
                    'workflow_type': self.running_workflows[self.active_workflow_id]['workflow_type'],
                    'campaign_id': self.running_workflows[self.active_workflow_id]['campaign_id'],
                    'start_time': self.running_workflows[self.active_workflow_id].get('start_time', timezone.now()).isoformat(),
                    'priority': self.running_workflows[self.active_workflow_id]['priority']
                }

            return {
                'bot_worker': {
                    'status': 'busy' if self.active_workflow_id else 'available',
                    'active_workflow': active_workflow
                },
                'queue': {
                    'total': len(self.workflow_queue),
                    'by_priority': queue_by_priority,
                    'by_type': queue_by_type,
                    'max_size': self.max_queue_size
                },
                'api_usage': self._get_api_usage(),
                'redis_status': {
                    'available': redis_available,
                    'error': redis_error
                },
                'timestamp': timezone.now().isoformat()
            }

    def schedule_workflow(self, workflow_execution_id, delay=None):
        """
        Schedule a workflow to run after a delay or when resources are available.

        Args:
            workflow_execution_id (str): Workflow execution ID
            delay (int, optional): Delay in seconds before running

        Returns:
            bool: True if scheduled successfully
        """
        try:
            # Get workflow execution
            workflow_execution = WorkflowExecution.objects.get(id=workflow_execution_id)

            # Schedule workflow
            if delay:
                # Schedule with delay
                self.redis_client.set(
                    f"workflow:{workflow_execution_id}:scheduled",
                    timezone.now().timestamp() + delay
                )
                logger.info(f"Scheduled workflow {workflow_execution_id} to run after {delay} seconds")
            else:
                # Schedule to run when resources are available
                self.redis_client.set(
                    f"workflow:{workflow_execution_id}:scheduled",
                    timezone.now().timestamp()
                )
                logger.info(f"Scheduled workflow {workflow_execution_id} to run when resources are available")

            return True
        except Exception as e:
            logger.exception(f"Error scheduling workflow {workflow_execution_id}: {str(e)}")
            return False

    def check_scheduled_workflows(self):
        """
        Check for scheduled workflows that can be run.

        Returns:
            list: List of workflow IDs that can be run
        """
        try:
            # Get all scheduled workflows
            scheduled_keys = self.redis_client.keys("workflow:*:scheduled")
            now = timezone.now().timestamp()

            runnable = []
            for key in scheduled_keys:
                workflow_id = key.decode('utf-8').split(':')[1]
                scheduled_time = float(self.redis_client.get(key))

                if scheduled_time <= now:
                    # Check if resources are available
                    workflow_execution = WorkflowExecution.objects.get(id=workflow_id)
                    if self._can_start_workflow(workflow_execution.workflow_type):
                        runnable.append(workflow_id)
                        # Remove from scheduled
                        self.redis_client.delete(key)

            return runnable
        except Exception as e:
            logger.exception(f"Error checking scheduled workflows: {str(e)}")
            return []

    def handle_error(self, workflow_id, error_message):
        """
        Handle workflow error.

        Args:
            workflow_id (str): Workflow execution ID
            error_message (str): Error message

        Returns:
            bool: True if error was handled
        """
        try:
            # Get workflow execution
            workflow_execution = WorkflowExecution.objects.get(id=workflow_id)

            # Check if we should retry
            if workflow_execution.retry_count < 3:  # Max 3 retries
                # Increment retry count
                workflow_execution.retry_count += 1
                workflow_execution.last_retry = timezone.now()
                workflow_execution.save(update_fields=['retry_count', 'last_retry'])

                # Schedule retry with exponential backoff
                delay = 60 * (2 ** workflow_execution.retry_count)  # 2, 4, 8 minutes
                self.schedule_workflow(workflow_id, delay)

                logger.info(f"Scheduled retry {workflow_execution.retry_count} for workflow {workflow_id} in {delay} seconds")
                return True
            else:
                # Mark as failed
                workflow_execution.fail(error_message)
                logger.error(f"Workflow {workflow_id} failed after {workflow_execution.retry_count} retries: {error_message}")
                return False
        except Exception as e:
            logger.exception(f"Error handling workflow error: {str(e)}")
            return False

    def _get_api_usage(self):
        """
        Get current API usage statistics.

        Returns:
            dict: API usage statistics
        """
        api_usage = {}

        try:
            # Get API usage from Redis
            for api_type in self.api_rate_limits.keys():
                key = f"api_rate:{api_type}"
                try:
                    current_usage = self.redis_client.get(key)
                    current_usage = int(current_usage) if current_usage else 0

                    # Get TTL (time to reset)
                    ttl = self.redis_client.ttl(key)
                    if ttl < 0:
                        ttl = self.api_rate_limits[api_type]['window']

                    # Get rate limit
                    limit = self.api_rate_limits[api_type]['limit']

                    api_usage[api_type] = {
                        'current': current_usage,
                        'limit': limit,
                        'reset_in': ttl
                    }
                except redis.exceptions.ConnectionError as e:
                    # Handle connection error for this specific key
                    logger.warning(f"Redis connection error when getting usage for {api_type}: {str(e)}")
                    # Provide default values
                    limit = self.api_rate_limits[api_type]['limit']
                    api_usage[api_type] = {
                        'current': 0,
                        'limit': limit,
                        'reset_in': self.api_rate_limits[api_type]['window'],
                        'connection_error': True
                    }
        except redis.exceptions.ConnectionError as e:
            # Handle connection error for the entire operation
            logger.error(f"Redis connection error in _get_api_usage: {str(e)}")
            # Provide default values for all API types
            for api_type in self.api_rate_limits.keys():
                limit = self.api_rate_limits[api_type]['limit']
                api_usage[api_type] = {
                    'current': 0,
                    'limit': limit,
                    'reset_in': self.api_rate_limits[api_type]['window'],
                    'connection_error': True
                }
        except Exception as e:
            # Handle any other unexpected errors
            logger.exception(f"Unexpected error in _get_api_usage: {str(e)}")
            # Provide default values for all API types
            for api_type in self.api_rate_limits.keys():
                limit = self.api_rate_limits[api_type]['limit']
                api_usage[api_type] = {
                    'current': 0,
                    'limit': limit,
                    'reset_in': self.api_rate_limits[api_type]['window'],
                    'error': True
                }

        return api_usage

    def optimize_resource_allocation(self):
        """
        Optimize bot worker allocation by ensuring the highest priority workflow is running.

        This method checks if the currently active workflow is the highest priority one.
        If not, it pauses the current workflow and starts the highest priority one.

        Returns:
            dict: Optimization results
        """
        with self.lock:
            # Get current status
            resources = self.get_system_resources()

            # If no active workflow, nothing to optimize
            if not self.active_workflow_id:
                return {
                    'optimized': False,
                    'message': 'No active workflow to optimize',
                    'resources': resources
                }

            # If queue is empty, nothing to optimize
            if not self.workflow_queue:
                return {
                    'optimized': False,
                    'message': 'No queued workflows to optimize',
                    'resources': resources
                }

            # Get highest priority workflow in queue
            highest_priority_entry = max(self.workflow_queue)
            highest_priority_id = highest_priority_entry.workflow_id
            highest_priority = highest_priority_entry.priority

            # Get current workflow priority
            current_priority = self.running_workflows[self.active_workflow_id]['priority']

            # If highest priority workflow has higher priority than current, switch
            if highest_priority > current_priority:
                # Pause current workflow
                current_id = self.active_workflow_id
                self._pause_workflow(current_id)

                # Remove highest priority workflow from queue
                self.workflow_queue = [entry for entry in self.workflow_queue if entry.workflow_id != highest_priority_id]
                heapq.heapify(self.workflow_queue)

                # Start highest priority workflow
                self.active_workflow_id = highest_priority_id
                self.running_workflows[highest_priority_id]['status'] = 'running'
                self.running_workflows[highest_priority_id]['start_time'] = timezone.now()

                # Send start signal via Redis
                self.redis_client.publish(
                    f"workflow:{highest_priority_id}:control",
                    json.dumps({"action": "start"})
                )

                logger.info(f"Optimized: Paused workflow {current_id} (priority {current_priority}) and started workflow {highest_priority_id} (priority {highest_priority})")

                return {
                    'optimized': True,
                    'message': f"Switched to higher priority workflow {highest_priority_id}",
                    'paused_workflow': current_id,
                    'started_workflow': highest_priority_id,
                    'resources': self.get_system_resources()
                }

            return {
                'optimized': False,
                'message': 'Current workflow already has highest priority',
                'resources': resources
            }

    def get_priority_mode(self):
        """
        Get the current system priority mode.

        Returns:
            str: Current priority mode ('dmp' or 'cep')
        """
        try:
            # Try to get from Redis first
            mode = self.redis_client.get('system:priority_mode')
            if mode and mode in PRIORITY_MODES:
                return mode
        except Exception as e:
            logger.warning(f"Could not get priority mode from Redis: {str(e)}")

        # Default to CEP priority
        return 'cep'

    def set_priority_mode(self, mode):
        """
        Set the system priority mode.

        Args:
            mode (str): Priority mode ('dmp' or 'cep')

        Returns:
            bool: True if successful, False otherwise
        """
        if mode not in PRIORITY_MODES:
            logger.error(f"Invalid priority mode: {mode}. Valid modes: {list(PRIORITY_MODES.keys())}")
            return False

        try:
            # Store in Redis
            self.redis_client.set('system:priority_mode', mode)

            # Update local configuration
            self.priority_mode = mode
            self.workflow_priorities = WORKFLOW_PRIORITIES.get(mode, WORKFLOW_PRIORITIES['cep'])

            logger.info(f"Priority mode changed to: {mode} ({PRIORITY_MODES[mode]})")

            # Re-prioritize existing workflows in queue
            self._reprioritize_queue()

            return True
        except Exception as e:
            logger.error(f"Could not set priority mode: {str(e)}")
            return False

    def _reprioritize_queue(self):
        """
        Re-prioritize workflows in the queue based on new priority mode.
        """
        try:
            with self.lock:
                # Get all workflows from queue
                workflows = []
                while self.workflow_queue:
                    entry = heapq.heappop(self.workflow_queue)
                    workflow_id = entry.workflow_id

                    if workflow_id in self.running_workflows:
                        # Update priority based on new mode
                        workflow_type = self.running_workflows[workflow_id]['workflow_type']
                        new_priority = self.workflow_priorities.get(workflow_type, 0)

                        # Update stored priority
                        self.running_workflows[workflow_id]['priority'] = new_priority

                        # Create new queue entry with updated priority
                        new_entry = WorkflowQueueEntry(
                            workflow_id=workflow_id,
                            priority=new_priority,
                            submission_time=entry.submission_time
                        )
                        workflows.append(new_entry)

                # Re-add all workflows to queue with new priorities
                for workflow in workflows:
                    heapq.heappush(self.workflow_queue, workflow)

                logger.info(f"Re-prioritized {len(workflows)} workflows in queue")

        except Exception as e:
            logger.exception(f"Error re-prioritizing queue: {str(e)}")

    def get_priority_mode_info(self):
        """
        Get information about the current priority mode and available modes.

        Returns:
            dict: Priority mode information
        """
        current_mode = self.get_priority_mode()

        return {
            'current_mode': current_mode,
            'current_mode_name': PRIORITY_MODES.get(current_mode, 'Unknown'),
            'available_modes': PRIORITY_MODES,
            'workflow_priorities': self.workflow_priorities,
            'queue_length': len(self.workflow_queue),
            'active_workflow': self.active_workflow_id
        }
