/* Workflow Creation Styles */

.workflow-create-container {
    background-color: #f8f9fa;
    min-height: 100vh;
    padding: 2rem 0;
}

.workflow-step-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.workflow-step-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border: none;
}

.workflow-step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 700;
    font-size: 1.1rem;
    margin-right: 1rem;
}

.campaign-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

.campaign-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    position: relative;
    overflow: hidden;
}

.campaign-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #0056b3);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.campaign-card:hover {
    border-color: #007bff;
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 123, 255, 0.15);
}

.campaign-card:hover::before {
    transform: scaleX(1);
}

.campaign-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
    box-shadow: 0 4px 16px rgba(0, 123, 255, 0.2);
}

.campaign-card.selected::before {
    transform: scaleX(1);
}

.campaign-card .form-check {
    margin: 0;
}

.campaign-card .form-check-input {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 1.25rem;
    height: 1.25rem;
}

.campaign-card .form-check-label {
    display: block;
    padding: 1.5rem;
    cursor: pointer;
    width: 100%;
}

.campaign-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
}

.campaign-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 1rem;
}

.template-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

.template-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    position: relative;
    overflow: hidden;
}

.template-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #28a745, #20c997);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.template-card:hover {
    border-color: #28a745;
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(40, 167, 69, 0.15);
}

.template-card:hover::before {
    transform: scaleX(1);
}

.template-card.selected {
    border-color: #28a745;
    background-color: #f8fff8;
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.2);
}

.template-card.selected::before {
    transform: scaleX(1);
}

.template-card .form-check {
    margin: 0;
}

.template-card .form-check-input {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 1.25rem;
    height: 1.25rem;
}

.template-card .form-check-label {
    display: block;
    padding: 1.5rem;
    cursor: pointer;
    width: 100%;
}

.template-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.template-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.template-category-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-weight: 500;
}

.template-actions-list {
    margin: 1rem 0;
}

.template-actions-list .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
    background-color: #e9ecef;
    color: #495057;
}

.template-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.workflow-config-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid #e9ecef;
}

.config-form-group {
    margin-bottom: 1.5rem;
}

.config-form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.config-form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.config-form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.action-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.action-checkbox-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s ease;
    background: white;
}

.action-checkbox-card:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.action-checkbox-card .form-check {
    margin: 0;
}

.action-checkbox-card .form-check-input:checked ~ .form-check-label {
    color: #007bff;
    font-weight: 600;
}

.workflow-create-actions {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    text-align: right;
}

.btn-create-workflow {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
}

.btn-create-workflow:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 123, 255, 0.3);
}

.btn-create-workflow:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.btn-cancel {
    background: transparent;
    border: 2px solid #6c757d;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    color: #6c757d;
    transition: all 0.2s ease;
    margin-right: 1rem;
}

.btn-cancel:hover {
    background: #6c757d;
    color: white;
}

.form-validation-error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.validation-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.progress-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 2rem 0;
}

.progress-step {
    display: flex;
    align-items: center;
    color: #6c757d;
}

.progress-step.active {
    color: #007bff;
    font-weight: 600;
}

.progress-step.completed {
    color: #28a745;
}

.progress-step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    font-weight: 600;
    margin-right: 0.5rem;
}

.progress-step.active .progress-step-number {
    background-color: #007bff;
    color: white;
}

.progress-step.completed .progress-step-number {
    background-color: #28a745;
    color: white;
}

.progress-connector {
    width: 3rem;
    height: 2px;
    background-color: #e9ecef;
    margin: 0 1rem;
}

.progress-step.completed + .progress-connector {
    background-color: #28a745;
}

@media (max-width: 768px) {
    .campaign-selection-grid,
    .template-selection-grid {
        grid-template-columns: 1fr;
    }
    
    .action-selection-grid {
        grid-template-columns: 1fr;
    }
    
    .workflow-config-section {
        padding: 1rem;
    }
    
    .workflow-create-actions {
        text-align: center;
    }
    
    .btn-cancel {
        margin-right: 0;
        margin-bottom: 1rem;
        width: 100%;
    }
    
    .btn-create-workflow {
        width: 100%;
    }
}

/* Loading animation */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
