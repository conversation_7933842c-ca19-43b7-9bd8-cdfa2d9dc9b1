# Customer Engagement Process (CEP) Workflow Documentation

## Overview

The Customer Engagement Process (CEP) is the final stage of the campaign workflow that enables automated Instagram engagement with accounts from your campaign whitelist. This document explains the complete workflow from whitelist generation through CEP execution.

## Complete Campaign to CEP Workflow

### Stage 1: Campaign Creation and Data Collection
1. **Campaign Setup**: Create a campaign with location and/or username targets
2. **Data Collection**: Run collection workflows to gather Instagram accounts
3. **Account Discovery**: Collect usernames, follower counts, following counts, and basic profile data

### Stage 2: Account Analysis and Tag Assignment
1. **Tag Creation**: Create dynamic tags with conditions to categorize accounts
2. **Automatic Analysis**: Apply tag filters synchronously to collected account data
3. **Tag Assignment**: Accounts are automatically tagged based on defined conditions
4. **Manual Review**: Review and adjust tag assignments if needed

### Stage 3: Whitelist Generation
1. **Filter Application**: Apply tag-based filters to generate final whitelist
2. **Quality Control**: Review whitelist for accuracy and relevance
3. **Export Options**: Export whitelist data for external use
4. **CEP Preparation**: Whitelist becomes available for CEP workflow creation

### Stage 4: CEP Workflow Creation
1. **Campaign Selection**: Choose a completed campaign with generated whitelist
2. **Workflow Configuration**: Select Instagram action types and parameters
3. **Safety Settings**: Configure daily limits and action delays
4. **Workflow Launch**: Deploy CEP workflow for execution

## CEP Workflow Creation Process

### Multi-Step Wizard Interface

#### Step 1: Campaign Selection
- **Purpose**: Select a campaign with a completed whitelist
- **Requirements**: Campaign must have status 'completed' or 'stopped' with generated whitelist
- **Display**: Shows campaign name, description, whitelist count, and creation date
- **Validation**: Ensures campaign is not already in another CEP workflow

#### Step 2: Workflow Selection
- **Available Actions**:
  - **Follow Actions**: Automatically follow accounts to increase follower base
  - **Post Likes**: Like recent posts to increase engagement
  - **Comments**: Leave thoughtful comments to build relationships
  - **Direct Messages**: Send personalized messages for direct outreach
  - **Discover Actions**: Explore and interact with account networks
- **Multi-Selection**: Users can combine multiple action types
- **Workflow Files**: Based on PyFlow automation files in `ENGINE/INSTA/WORKFLOW/`

#### Step 3: Configuration Settings
- **Workflow Name**: Descriptive name for the CEP workflow
- **Description**: Optional detailed description
- **Daily Limit**: Maximum actions per day (recommended: 50-100)
- **Action Delay**: Time between actions in seconds (recommended: 30-60)
- **Safety Features**: Built-in rate limiting and human-like behavior

#### Step 4: Review and Creation
- **Summary Display**: Review all selected options and settings
- **Safety Information**: Display built-in protection features
- **Final Validation**: Ensure all requirements are met
- **Workflow Creation**: Deploy the CEP workflow

## CEP Workflow Execution

### Workflow Processing
1. **Account Queue**: Whitelist accounts are processed sequentially
2. **Action Execution**: Selected Instagram actions are performed with proper delays
3. **Rate Limiting**: Daily limits and action delays are enforced
4. **Error Handling**: Automatic retry and recovery mechanisms
5. **Progress Tracking**: Real-time monitoring and analytics

### Safety Features
- **Rate Limiting Protection**: Prevents Instagram API rate limit violations
- **Human-like Delays**: Randomized delays between actions
- **Daily Action Limits**: Configurable maximum actions per day
- **Error Handling & Recovery**: Automatic handling of API errors
- **Real-time Monitoring**: Continuous workflow status tracking

### Workflow Management
- **Start/Pause/Resume**: Full control over workflow execution
- **Stop Functionality**: Ability to terminate workflows
- **Progress Analytics**: Detailed execution statistics
- **Account Tracking**: Monitor which accounts have been processed

## System Priority Management

### Priority Modes
The system supports two priority modes for resource allocation:

#### DMP (Data Collection) Priority
- **Focus**: Prioritizes data mining and collection workflows
- **Use Case**: When building large datasets or discovering new accounts
- **Workflow Priorities**:
  1. Collection workflows (highest)
  2. Analysis workflows
  3. Tagging workflows
  4. CEP workflows (lower priority)

#### CEP (Action Execution) Priority
- **Focus**: Prioritizes customer engagement and action workflows
- **Use Case**: When actively engaging with collected accounts
- **Workflow Priorities**:
  1. CEP workflows (highest)
  2. Follow actions
  3. Like actions
  4. Comment actions
  5. Collection workflows (lower priority)

### Priority Mode Management
- **Resource Manager**: Access priority settings through the resource manager dashboard
- **Dynamic Switching**: Change priority modes without system restart
- **Queue Re-prioritization**: Existing workflows are automatically re-prioritized
- **Real-time Updates**: Priority changes take effect immediately

## Integration with PyFlow Workflows

### Available Workflow Types
The CEP system integrates with PyFlow automation files located in `ENGINE/INSTA/WORKFLOW/`:

1. **FOLLOW_WORKFLOW.pygraph**: Automated following actions
2. **LIKE_WORKFLOW.pygraph**: Post liking automation
3. **COMMENT_WORKFLOW.pygraph**: Comment posting automation
4. **DM_WORKFLOW.pygraph**: Direct message automation
5. **DISCOVER_WORKFLOW.pygraph**: Account discovery and exploration

### Workflow Execution
- **Sequential Processing**: One workflow at a time for account safety
- **Exclusive Access**: Single bot worker ensures no conflicts
- **Progress Logging**: Detailed execution logs and progress tracking
- **Error Recovery**: Automatic retry mechanisms for failed actions

## Best Practices

### Campaign Design
1. **Target Selection**: Choose relevant locations and usernames for your niche
2. **Tag Strategy**: Create comprehensive tags to filter high-quality accounts
3. **Whitelist Quality**: Review and refine whitelist before CEP creation

### CEP Configuration
1. **Action Selection**: Choose actions appropriate for your engagement strategy
2. **Daily Limits**: Start with conservative limits (50-100 actions/day)
3. **Timing**: Schedule CEP workflows during optimal engagement hours
4. **Monitoring**: Regularly check workflow progress and adjust as needed

### Safety Considerations
1. **Rate Limiting**: Always respect Instagram's rate limits
2. **Human Behavior**: Use realistic delays between actions
3. **Account Safety**: Monitor for any unusual account behavior
4. **Compliance**: Ensure all actions comply with Instagram's terms of service

## Technical Architecture

### Database Models
- **Campaign**: Core campaign data and configuration
- **CampaignTag**: Dynamic tagging system for account categorization
- **CEPWorkflow**: CEP workflow configuration and status
- **WhiteListEntry**: Final filtered accounts for engagement

### API Endpoints
- **CEP Creation**: Multi-step wizard for workflow creation
- **Workflow Management**: Start, pause, resume, stop operations
- **Status Monitoring**: Real-time workflow status and progress
- **Priority Management**: System priority mode configuration

### Resource Management
- **Bot Worker**: Single worker for exclusive workflow execution
- **Queue Management**: Priority-based workflow scheduling
- **Redis Integration**: Real-time state management and caching
- **System Monitoring**: Resource usage and performance tracking

## Troubleshooting

### Common Issues
1. **No Available Campaigns**: Ensure campaigns have completed whitelists
2. **Workflow Conflicts**: Only one active CEP workflow allowed at a time
3. **API Limits**: Monitor daily action limits and adjust accordingly
4. **Connection Issues**: Check Redis and database connectivity

### Error Resolution
1. **Check Logs**: Review Django and PyFlow execution logs
2. **Verify Configuration**: Ensure all settings are correct
3. **Resource Status**: Check resource manager dashboard
4. **Manual Intervention**: Pause workflows if issues persist

## Future Enhancements

### Planned Features
1. **Advanced Scheduling**: Time-based workflow execution
2. **A/B Testing**: Multiple engagement strategies comparison
3. **Analytics Dashboard**: Comprehensive engagement analytics
4. **Custom Actions**: User-defined engagement workflows
5. **Integration APIs**: External system integration capabilities

This documentation provides a comprehensive guide to the CEP workflow system, from initial campaign creation through final engagement execution.
