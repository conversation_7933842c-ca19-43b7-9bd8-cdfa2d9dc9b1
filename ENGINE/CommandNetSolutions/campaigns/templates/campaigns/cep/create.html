{% extends "base.html" %}
{% load static %}

{% block title %}Create CEP Workflow{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Create CEP Workflow</h1>
        <a href="{% url 'campaigns:workflow_dashboard' %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm mr-2">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Dashboard
        </a>
    </div>

    {% if messages %}
    <div class="row">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    {% if active_workflow %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> There is already an active CEP workflow for campaign <strong>{{ active_workflow.campaign.name }}</strong>. Only one active CEP workflow is allowed at a time.
                <a href="{% url 'campaigns:cep_detail' pk=active_workflow.id %}" class="btn btn-sm btn-primary ml-2">View Active Workflow</a>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row">
        <div class="col-xl-12 col-lg-12">
            <div class="card shadow mb-4 border-left-primary">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Create New CEP Workflow</h6>
                </div>
                <div class="card-body">
                    {% if not active_workflow %}
                    <form method="post" action="{% url 'campaigns:cep_create' %}">
                        {% csrf_token %}
                        
                        <div class="form-group mb-4">
                            <label for="id_campaign">Campaign</label>
                            <select name="campaign" id="id_campaign" class="form-control {% if form.campaign.errors %}is-invalid{% endif %}" required>
                                {% for value, text in form.fields.campaign.widget.choices %}
                                <option value="{{ value }}" {% if form.campaign.value == value %}selected{% endif %}>{{ text }}</option>
                                {% endfor %}
                            </select>
                            {% if form.campaign.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.campaign.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">Select a campaign with a whitelist to process.</small>
                        </div>
                        
                        <div class="form-group mb-4">
                            <label for="id_subscription_tier">Subscription Tier</label>
                            <select name="subscription_tier" id="id_subscription_tier" class="form-control {% if form.subscription_tier.errors %}is-invalid{% endif %}" required>
                                {% for value, text in form.fields.subscription_tier.choices %}
                                <option value="{{ value }}" {% if form.subscription_tier.value == value %}selected{% endif %}>{{ text }}</option>
                                {% endfor %}
                            </select>
                            {% if form.subscription_tier.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.subscription_tier.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5>Subscription Tier Information</h5>
                                <div class="card-deck">
                                    {% for tier in subscription_tiers %}
                                    <div class="card mb-4 tier-card" data-tier="{{ tier.value }}">
                                        <div class="card-header bg-{% if tier.value == 'bronze' %}secondary{% elif tier.value == 'silver' %}primary{% else %}warning{% endif %} text-white">
                                            <h5 class="card-title mb-0">{{ tier.label }}</h5>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text">{{ tier.description }}</p>
                                            <ul class="list-group list-group-flush">
                                                {% if tier.value == 'bronze' %}
                                                <li class="list-group-item">Follow actions</li>
                                                <li class="list-group-item">Like actions</li>
                                                <li class="list-group-item text-muted">Comment actions (not available)</li>
                                                <li class="list-group-item text-muted">DM actions (not available)</li>
                                                {% elif tier.value == 'silver' %}
                                                <li class="list-group-item">Follow actions</li>
                                                <li class="list-group-item">Like actions</li>
                                                <li class="list-group-item">Comment actions</li>
                                                <li class="list-group-item text-muted">DM actions (not available)</li>
                                                {% else %}
                                                <li class="list-group-item">Follow actions</li>
                                                <li class="list-group-item">Like actions</li>
                                                <li class="list-group-item">Comment actions</li>
                                                <li class="list-group-item">DM actions</li>
                                                {% endif %}
                                            </ul>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">Create Workflow</button>
                            <a href="{% url 'campaigns:workflow_dashboard' %}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                    {% else %}
                    <div class="text-center py-4">
                        <p>You cannot create a new CEP workflow while another one is active.</p>
                        <a href="{% url 'campaigns:cep_detail' pk=active_workflow.id %}" class="btn btn-primary">View Active Workflow</a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Highlight selected tier card
        function highlightSelectedTier() {
            const selectedTier = $("#id_subscription_tier").val();
            $(".tier-card").removeClass("border-primary").addClass("border-light");
            $(`.tier-card[data-tier="${selectedTier}"]`).removeClass("border-light").addClass("border-primary");
        }
        
        // Initial highlight
        highlightSelectedTier();
        
        // Update highlight when selection changes
        $("#id_subscription_tier").change(function() {
            highlightSelectedTier();
        });
    });
</script>
{% endblock %}
