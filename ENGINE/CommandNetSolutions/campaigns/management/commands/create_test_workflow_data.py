"""
Management command to create test data for workflow system.
"""
import random
from django.core.management.base import BaseCommand
from django.utils import timezone
from campaigns.models import Campaign
from campaigns.models import CampaignTag
from instagram.models import Accounts, WhiteListEntry


class Command(BaseCommand):
    help = 'Create test data for workflow system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--accounts',
            type=int,
            default=100,
            help='Number of test accounts to create'
        )

    def handle(self, *args, **options):
        """Create test data for workflow system."""
        
        accounts_count = options['accounts']
        
        # Create a test campaign
        campaign, created = Campaign.objects.get_or_create(
            name='Test Workflow Campaign',
            defaults={
                'description': 'Test campaign for workflow system with realistic data',
                'status': 'completed',
                'is_in_cep': False,
                'created_at': timezone.now()
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Created test campaign: {campaign.name}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Using existing campaign: {campaign.name}')
            )

        # Create test dynamic tags first
        from campaigns.models import DynamicTag, TagCategory

        # Create or get tag category
        tag_category, _ = TagCategory.objects.get_or_create(
            name='Test Category',
            defaults={'description': 'Test category for workflow data'}
        )

        tag_data = [
            {
                'name': 'High Engagement',
                'description': 'Accounts with high engagement rates',
                'pattern': 'high_engagement',
                'field': 'bio'
            },
            {
                'name': 'Micro Influencer',
                'description': 'Accounts with moderate following',
                'pattern': 'influencer',
                'field': 'bio'
            },
            {
                'name': 'Active User',
                'description': 'Recently active accounts',
                'pattern': 'active',
                'field': 'bio'
            }
        ]

        created_tags = []
        for tag_info in tag_data:
            # Create DynamicTag
            dynamic_tag, created = DynamicTag.objects.get_or_create(
                name=tag_info['name'],
                defaults={
                    'description': tag_info['description'],
                    'category': tag_category,
                    'pattern': tag_info['pattern'],
                    'field': tag_info['field'],
                    'tag_type': 'keyword'
                }
            )

            # Create CampaignTag association
            campaign_tag, ct_created = CampaignTag.objects.get_or_create(
                campaign=campaign,
                tag=dynamic_tag,
                defaults={'is_required': False}
            )

            created_tags.append(dynamic_tag)

            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created tag: {dynamic_tag.name}')
                )

        # Create test accounts
        usernames = [
            'fitness_guru_2024', 'travel_explorer', 'food_blogger_nyc', 'tech_reviewer',
            'fashion_stylist', 'photography_pro', 'music_producer', 'art_creator',
            'lifestyle_coach', 'business_mentor', 'health_expert', 'beauty_tips',
            'sports_fan', 'movie_critic', 'book_lover', 'gaming_streamer',
            'yoga_instructor', 'chef_recipes', 'diy_projects', 'pet_lover',
            'nature_photographer', 'city_explorer', 'vintage_collector', 'minimalist_life',
            'entrepreneur_tips', 'study_motivation', 'workout_daily', 'meditation_guide',
            'coffee_addict', 'plant_parent', 'adventure_seeker', 'creative_writer',
            'dance_moves', 'skincare_routine', 'home_decor', 'sustainable_living',
            'language_learner', 'coding_tutorials', 'investment_tips', 'parenting_advice',
            'wedding_planner', 'event_organizer', 'graphic_designer', 'web_developer',
            'marketing_expert', 'sales_coach', 'productivity_hacks', 'time_management',
            'self_improvement', 'motivation_daily', 'success_mindset', 'goal_achiever'
        ]

        created_accounts = 0
        for i in range(min(accounts_count, len(usernames))):
            username = usernames[i]
            
            # Generate realistic account data
            followers_count = random.randint(500, 100000)
            following_count = random.randint(100, 5000)
            posts_count = random.randint(20, 500)

            account, created = Accounts.objects.get_or_create(
                username=username,
                defaults={
                    'campaign_id': str(campaign.id),
                    'followers': followers_count,
                    'following': following_count,
                    'number_of_posts': posts_count,
                    'bio': f'Bio for {username} - passionate about sharing content',
                    'is_verified': random.choice([True, False]) if followers_count > 10000 else False,
                    'full_name': f'{username.replace("_", " ").title()}',
                    'account_type': random.choice(['personal', 'business']),
                    'collection_date': timezone.now()
                }
            )
            
            if created:
                created_accounts += 1
                
                # Assign tags based on account data
                assigned_tags = []

                # High Engagement tag
                if followers_count >= 1000 and following_count <= 2000:
                    assigned_tags.append('High Engagement')

                # Micro Influencer tag
                if 5000 <= followers_count <= 50000:
                    assigned_tags.append('Micro Influencer')

                # Active User tag
                if posts_count >= 50:
                    assigned_tags.append('Active User')

                # Create whitelist entry for accounts that meet criteria
                if assigned_tags:
                    _, wl_created = WhiteListEntry.objects.get_or_create(
                        account=account,
                        defaults={
                            'tags': assigned_tags,
                            'dm': True,
                            'follow': True,
                            'post_like': True,
                            'is_auto': True
                        }
                    )

                    if wl_created:
                        self.stdout.write(
                            self.style.SUCCESS(f'Added {username} to whitelist with tags: {", ".join(assigned_tags)}')
                        )

        # Update campaign status
        whitelist_count = WhiteListEntry.objects.filter(
            account__campaign_id=str(campaign.id)
        ).count()
        
        if whitelist_count > 0:
            campaign.status = 'completed'
            campaign.save()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'\nTest data creation completed!'
                    f'\n- Campaign: {campaign.name}'
                    f'\n- Created accounts: {created_accounts}'
                    f'\n- Whitelist entries: {whitelist_count}'
                    f'\n- Tags created: {len(created_tags)}'
                    f'\n\nThe campaign is now ready for workflow creation!'
                )
            )
        else:
            self.stdout.write(
                self.style.ERROR('No accounts were added to whitelist. Check tag conditions.')
            )
