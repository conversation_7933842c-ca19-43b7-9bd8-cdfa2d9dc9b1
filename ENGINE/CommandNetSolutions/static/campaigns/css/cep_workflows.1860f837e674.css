/* CEP Workflow Styles */

/* Card styling */
.tier-card {
    transition: all 0.3s ease;
    border-width: 2px;
}

.tier-card.border-primary {
    box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
}

/* Progress bars */
.progress {
    height: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 0.5rem;
    background-color: #f0f0f0;
}

.progress-bar {
    background-color: #007bff;
    transition: width 0.6s ease;
}

.progress-bar.bg-success {
    background-color: #28a745;
}

/* Status badges */
.badge.badge-success {
    background-color: #28a745;
}

.badge.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge.badge-danger {
    background-color: #dc3545;
}

.badge.badge-info {
    background-color: #17a2b8;
}

.badge.badge-secondary {
    background-color: #6c757d;
}

/* Tier headers */
.card-header.bg-secondary {
    background-color: #6c757d !important;
}

.card-header.bg-primary {
    background-color: #007bff !important;
}

.card-header.bg-warning {
    background-color: #ffc107 !important;
}

/* Action buttons */
.action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

/* Workflow details */
.workflow-details {
    margin-top: 1.5rem;
}

.workflow-details h5 {
    margin-bottom: 1rem;
    font-weight: 600;
}

/* List group styling */
.list-group-item.text-muted {
    color: #6c757d;
    text-decoration: line-through;
    opacity: 0.7;
}

/* Alert styling */
.alert i {
    margin-right: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tier-card {
        margin-bottom: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .action-buttons form {
        margin-bottom: 0.5rem;
    }
}

/* Animation for progress bars */
@keyframes progress-bar-stripes {
    from { background-position: 1rem 0; }
    to { background-position: 0 0; }
}

.progress-bar.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

.progress-bar.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
}

/* Tier comparison table */
.tier-comparison {
    margin-top: 2rem;
}

.tier-comparison th {
    text-align: center;
}

.tier-comparison td {
    text-align: center;
}

.tier-comparison .feature-available {
    color: #28a745;
}

.tier-comparison .feature-unavailable {
    color: #dc3545;
}

/* Workflow status indicators */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.running {
    background-color: #28a745;
    box-shadow: 0 0 5px #28a745;
    animation: pulse 1.5s infinite;
}

.status-indicator.paused {
    background-color: #ffc107;
    box-shadow: 0 0 5px #ffc107;
}

.status-indicator.pending {
    background-color: #17a2b8;
    box-shadow: 0 0 5px #17a2b8;
}

.status-indicator.completed {
    background-color: #28a745;
    box-shadow: 0 0 5px #28a745;
}

.status-indicator.failed {
    background-color: #dc3545;
    box-shadow: 0 0 5px #dc3545;
}

.status-indicator.stopped {
    background-color: #6c757d;
    box-shadow: 0 0 5px #6c757d;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Campaign card styling */
.campaign-card {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #dee2e6;
}

.campaign-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.campaign-card.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Workflow history timeline */
.workflow-timeline {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 2rem;
}

.workflow-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0.5rem;
    width: 2px;
    background-color: #dee2e6;
}

.timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    top: 0.25rem;
    left: -1.5rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background-color: #007bff;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #007bff;
}

.timeline-item.completed::before {
    background-color: #28a745;
    box-shadow: 0 0 0 2px #28a745;
}

.timeline-item.failed::before {
    background-color: #dc3545;
    box-shadow: 0 0 0 2px #dc3545;
}

.timeline-item.stopped::before {
    background-color: #6c757d;
    box-shadow: 0 0 0 2px #6c757d;
}

.timeline-content {
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
}

.timeline-date {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Add this to the existing CSS file */
.card-header .btn-sm {
    margin-left: 0.5rem;
}

.tier-info-box {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.tier-info-box ul {
    margin-bottom: 0;
}

.tier-info-box h5 {
    margin-bottom: 0.5rem;
}

/* Ensure buttons have proper spacing */
form.d-inline {
    margin-right: 0.25rem;
}

/* Ensure proper button alignment */
.card-header .d-flex {
    align-items: center;
}

/* Ensure proper spacing in detail view */
.detail-section {
    margin-bottom: 2rem;
}

/* Ensure proper spacing in dashboard */
.dashboard-section {
    margin-bottom: 2rem;
}

/* Ensure proper spacing in create view */
.create-section {
    margin-bottom: 2rem;
}

/* Ensure proper spacing in form groups */
.form-group {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in alerts */
.alert {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in cards */
.card {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in tables */
.table {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in progress bars */
.progress {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in badges */
.badge {
    margin-right: 0.25rem;
}

/* Ensure proper spacing in buttons */
.btn {
    margin-right: 0.25rem;
}

/* Ensure proper spacing in forms */
form {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form controls */
/* .form-control {
    margin-bottom: 1rem;
} */

/* Ensure proper spacing in form labels */
.form-label {
    margin-bottom: 0.5rem;
}

/* Ensure proper spacing in form text */
.form-text {
    margin-top: 0.25rem;
}

/* Ensure proper spacing in form feedback */
.invalid-feedback {
    margin-top: 0.25rem;
}

/* Ensure proper spacing in form check */
.form-check {
    margin-bottom: 0.5rem;
}

/* Ensure proper spacing in form check label */
.form-check-label {
    margin-bottom: 0;
}

/* Ensure proper spacing in form check input */
.form-check-input {
    margin-top: 0.25rem;
}

/* Ensure proper spacing in form select */
.form-select {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form range */
.form-range {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form file */
.form-file {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form color */
.form-color {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form date */
.form-date {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form time */
.form-time {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form datetime */
.form-datetime {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form month */
.form-month {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form week */
.form-week {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form number */
.form-number {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form search */
.form-search {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form tel */
.form-tel {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form url */
.form-url {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form email */
.form-email {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form password */
.form-password {
    margin-bottom: 1rem;
}

/* Ensure proper spacing in form textarea */
.form-textarea {
    margin-bottom: 1rem;
}
