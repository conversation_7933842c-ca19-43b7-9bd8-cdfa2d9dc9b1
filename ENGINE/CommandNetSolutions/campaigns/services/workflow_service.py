"""
Enhanced Workflow service for managing campaign workflows.

This service provides methods for creating and running workflows for campaigns
with comprehensive error handling, transaction management, and resource monitoring.
"""
import logging
import time
from typing import Dict, Any, Optional, List
from contextlib import contextmanager
from django.db import transaction, IntegrityError, OperationalError
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.conf import settings

from campaigns.models import Campaign
from campaigns.models.workflow import WorkflowExecution
from campaigns.services.pyflow_factory import PyFlowFactory
from campaigns.services.redis_service import RedisService, RedisConnectionError

logger = logging.getLogger(__name__)


class WorkflowExecutionError(Exception):
    """Custom exception for workflow execution errors."""
    pass


class WorkflowService:
    """
    Enhanced service for managing campaign workflows with robust error handling.

    This service provides methods for creating and running workflows for campaigns
    with comprehensive transaction management, resource monitoring, and error recovery.
    """

    def __init__(self):
        """Initialize the workflow service with enhanced capabilities."""
        # Get the appropriate PyFlow service implementation
        self.pyflow_service = PyFlowFactory.create_pyflow_service()

        # Initialize Redis service for workflow tracking
        try:
            self.redis_service = RedisService()
        except RedisConnectionError as e:
            logger.warning(f"Redis service unavailable: {str(e)}. Workflow tracking will be limited.")
            self.redis_service = None

        # Workflow execution limits
        self.max_concurrent_workflows = getattr(settings, 'PYFLOW_MAX_CONCURRENT_WORKFLOWS', 5)
        self.workflow_timeout = getattr(settings, 'PYFLOW_WORKFLOW_TIMEOUT', 3600)

    @contextmanager
    def _workflow_execution_context(self, campaign_id: str, workflow_type: str):
        """
        Context manager for workflow execution with proper resource management.

        Args:
            campaign_id (str): Campaign ID
            workflow_type (str): Type of workflow

        Yields:
            WorkflowExecution: Created workflow execution instance
        """
        workflow_execution = None
        lock_name = f"workflow:{campaign_id}:{workflow_type}"

        try:
            # Check if Redis is available for locking
            if self.redis_service:
                with self.redis_service.get_lock(lock_name, timeout=self.workflow_timeout):
                    # Check concurrent workflow limit
                    active_workflows = self._get_active_workflow_count()
                    if active_workflows >= self.max_concurrent_workflows:
                        raise WorkflowExecutionError(
                            f"Maximum concurrent workflows ({self.max_concurrent_workflows}) reached"
                        )

                    # Create workflow execution with transaction
                    with transaction.atomic():
                        campaign = Campaign.objects.select_for_update().get(id=campaign_id)
                        workflow_execution = WorkflowExecution.objects.create(
                            campaign=campaign,
                            workflow_type=workflow_type,
                            status='pending',
                            start_time=timezone.now()
                        )

                        # Update Redis tracking
                        self.redis_service.set_workflow_status(
                            str(workflow_execution.id),
                            {
                                'campaign_id': campaign_id,
                                'workflow_type': workflow_type,
                                'status': 'pending',
                                'start_time': workflow_execution.start_time.isoformat()
                            }
                        )

                    yield workflow_execution
            else:
                # Fallback without Redis locking
                with transaction.atomic():
                    campaign = Campaign.objects.select_for_update().get(id=campaign_id)
                    workflow_execution = WorkflowExecution.objects.create(
                        campaign=campaign,
                        workflow_type=workflow_type,
                        status='pending',
                        start_time=timezone.now()
                    )

                yield workflow_execution

        except Campaign.DoesNotExist:
            logger.error(f"Campaign {campaign_id} not found")
            raise WorkflowExecutionError(f"Campaign {campaign_id} not found")

        except IntegrityError as e:
            logger.error(f"Database integrity error: {str(e)}")
            raise WorkflowExecutionError(f"Database error: {str(e)}")

        except OperationalError as e:
            logger.error(f"Database operational error: {str(e)}")
            raise WorkflowExecutionError(f"Database connection error: {str(e)}")

        except Exception as e:
            logger.exception(f"Unexpected error in workflow execution context: {str(e)}")
            if workflow_execution:
                try:
                    workflow_execution.fail(str(e))
                except Exception:
                    pass
            raise WorkflowExecutionError(f"Workflow execution failed: {str(e)}")

        finally:
            # Cleanup Redis tracking if workflow failed
            if workflow_execution and self.redis_service:
                try:
                    if workflow_execution.status in ['failed', 'cancelled']:
                        self.redis_service.delete_workflow_status(str(workflow_execution.id))
                except Exception as e:
                    logger.warning(f"Failed to cleanup Redis tracking: {str(e)}")

    def _get_active_workflow_count(self) -> int:
        """
        Get the count of currently active workflows.

        Returns:
            int: Number of active workflows
        """
        try:
            if self.redis_service:
                active_workflows = self.redis_service.get_active_workflows()
                return len(active_workflows)
            else:
                # Fallback to database query
                return WorkflowExecution.objects.filter(status='running').count()
        except Exception as e:
            logger.warning(f"Failed to get active workflow count: {str(e)}")
            return 0

    def run_collection_workflow(self, campaign_id, targets=None, options=None):
        """
        Run a collection workflow for a campaign.

        Args:
            campaign_id (str): Campaign ID
            targets (list, optional): List of targets (locations or usernames)
            options (dict, optional): Additional options

        Returns:
            dict: Result of workflow execution
        """
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)

            # Get targets if not provided
            if targets is None:
                targets = self._get_campaign_targets(campaign)

            # Run workflow
            result = self.pyflow_service.create_and_run_collection_workflow(
                campaign_id=str(campaign.id),
                targets=targets,
                options=options
            )

            # Update campaign status if workflow started successfully
            if result.get('success'):
                with transaction.atomic():
                    campaign.status = 'running'
                    campaign.save(update_fields=['status'])

            return result

        except Campaign.DoesNotExist:
            logger.error(f"Campaign {campaign_id} not found")
            return {
                'success': False,
                'error': f"Campaign {campaign_id} not found"
            }
        except Exception as e:
            logger.exception(f"Error running collection workflow: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def run_analysis_workflow(self, campaign_id, analysis_settings=None, options=None):
        """
        Run an analysis workflow for a campaign.

        Args:
            campaign_id (str): Campaign ID
            analysis_settings (dict, optional): Analysis settings
            options (dict, optional): Additional options

        Returns:
            dict: Result of workflow execution
        """
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)

            # Get analysis settings if not provided
            if analysis_settings is None:
                analysis_settings = self._get_campaign_analysis_settings(campaign)

            # Run workflow
            result = self.pyflow_service.create_and_run_analysis_workflow(
                campaign_id=str(campaign.id),
                analysis_settings=analysis_settings,
                options=options
            )

            # Update campaign status if workflow started successfully
            if result.get('success'):
                with transaction.atomic():
                    campaign.status = 'analyzing'
                    campaign.save(update_fields=['status'])

            return result

        except Campaign.DoesNotExist:
            logger.error(f"Campaign {campaign_id} not found")
            return {
                'success': False,
                'error': f"Campaign {campaign_id} not found"
            }
        except Exception as e:
            logger.exception(f"Error running analysis workflow: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def run_engagement_workflow(self, campaign_id, workflow_type, accounts=None, options=None):
        """
        Run an engagement workflow for a campaign.

        Args:
            campaign_id (str): Campaign ID
            workflow_type (str): Type of engagement workflow ('follow', 'like', 'comment', 'dm', 'cep')
            accounts (list, optional): List of accounts to engage with
            options (dict, optional): Additional options

        Returns:
            dict: Result of workflow execution
        """
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)

            # Get accounts if not provided
            if accounts is None:
                accounts = self._get_campaign_whitelist(campaign)

            # Run workflow
            result = self.pyflow_service.create_and_run_engagement_workflow(
                campaign_id=str(campaign.id),
                workflow_type=workflow_type,
                accounts=accounts,
                options=options
            )

            # Update campaign status if workflow started successfully
            if result.get('success'):
                with transaction.atomic():
                    campaign.status = 'engaging'
                    campaign.save(update_fields=['status'])

            return result

        except Campaign.DoesNotExist:
            logger.error(f"Campaign {campaign_id} not found")
            return {
                'success': False,
                'error': f"Campaign {campaign_id} not found"
            }
        except Exception as e:
            logger.exception(f"Error running engagement workflow: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_workflow_status(self, workflow_execution_id):
        """
        Get the status of a workflow execution.

        Args:
            workflow_execution_id (str): Workflow execution ID

        Returns:
            dict: Status of the workflow execution
        """
        return self.pyflow_service.get_workflow_status(workflow_execution_id)

    def _get_campaign_targets(self, campaign):
        """
        Get targets for a campaign.

        Args:
            campaign (Campaign): Campaign object

        Returns:
            list: List of targets
        """
        targets = []

        # Add location targets
        for location_target in campaign.location_targets.all():
            targets.append({
                'type': 'location',
                'location_id': location_target.location_id,
                'location_name': location_target.location_name
            })

        # Add username targets
        for username_target in campaign.username_targets.all():
            targets.append({
                'type': 'username',
                'username': username_target.username,
                'audience_type': username_target.audience_type
            })

        return targets

    def _get_campaign_analysis_settings(self, campaign):
        """
        Get analysis settings for a campaign.

        Args:
            campaign (Campaign): Campaign object

        Returns:
            dict: Analysis settings
        """
        try:
            # Get analysis settings
            settings = campaign.analysis_settings

            return {
                'min_followers': settings.min_followers,
                'max_followers': settings.max_followers,
                'target_tags': settings.target_tags,
                'enable_tagging': settings.enable_tagging
            }
        except Exception:
            # Return default settings
            return {
                'min_followers': None,
                'max_followers': None,
                'target_tags': [],
                'enable_tagging': True
            }

    def _get_campaign_whitelist(self, campaign):
        """
        Get whitelist for a campaign.

        Args:
            campaign (Campaign): Campaign object

        Returns:
            list: List of whitelisted accounts
        """
        # This is a placeholder implementation
        # In a real implementation, you would query the database for whitelisted accounts
        return []
