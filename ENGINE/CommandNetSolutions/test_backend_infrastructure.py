#!/usr/bin/env python
"""
Comprehensive test script for enhanced Django backend infrastructure.

This script tests:
1. Database connections and ORM operations
2. Redis connections and caching
3. Airflow API connectivity
4. PyFlow service availability
5. Health check services
6. Resource management
7. Workflow execution pipeline
"""
import os
import sys
import django
import time
import json
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CommandNetSolutions.settings.dev')
django.setup()

from django.test import TestCase, Client
from django.db import transaction, connection
from django.core.cache import cache
from django.contrib.auth.models import User
from campaigns.models import Campaign, DynamicTag, TagGroup, TagCategory
from campaigns.models.workflow import WorkflowExecution
from campaigns.services.health_check_service import HealthCheckService
from campaigns.services.redis_service import RedisService, RedisConnectionError
from campaigns.services.airflow_service import AirflowService, AirflowConnectionError
from campaigns.services.workflow_service import WorkflowService, WorkflowExecutionError
from campaigns.services.resource_manager import Resource<PERSON>anager


def test_database_infrastructure():
    """Test database connections and ORM operations."""
    print("🔍 Testing Database Infrastructure...")
    
    try:
        # Test basic connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT version()")
            db_version = cursor.fetchone()[0]
            print(f"✅ Database connection successful: {db_version}")
        
        # Test connection pooling
        print(f"✅ Connection max age: {connection.settings_dict.get('CONN_MAX_AGE', 'Not set')}")
        print(f"✅ Health checks enabled: {connection.settings_dict.get('CONN_HEALTH_CHECKS', False)}")
        print(f"✅ Atomic requests: {connection.settings_dict.get('ATOMIC_REQUESTS', False)}")
        
        # Test transaction management
        with transaction.atomic():
            user = User.objects.create_user(
                username=f'test_user_{int(time.time())}',
                email='<EMAIL>'
            )
            campaign = Campaign.objects.create(
                name=f'Test Campaign {int(time.time())}',
                description='Test campaign for infrastructure testing',
                target_type='location',
                audience_type='followers',
                creator=user
            )
            print(f"✅ Transaction management working: Created campaign {campaign.id}")
        
        # Test foreign key relationships
        workflow = WorkflowExecution.objects.create(
            campaign=campaign,
            workflow_name='test_workflow.pygraph',
            workflow_path='/test/path',
            workflow_type='collection',
            status='pending'
        )
        print(f"✅ Foreign key relationships working: Created workflow {workflow.id}")
        
        # Test cascade deletion
        campaign_id = campaign.id
        workflow_id = workflow.id
        campaign.delete()
        
        # Verify cascade deletion
        assert not Campaign.objects.filter(id=campaign_id).exists()
        assert not WorkflowExecution.objects.filter(id=workflow_id).exists()
        print("✅ Cascade deletion working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Database infrastructure test failed: {str(e)}")
        return False


def test_redis_infrastructure():
    """Test Redis connections and caching."""
    print("\n🔍 Testing Redis Infrastructure...")
    
    try:
        # Test Redis service
        redis_service = RedisService()
        print("✅ Redis service initialized successfully")
        
        # Test connection info
        conn_info = redis_service.get_connection_info()
        print(f"✅ Redis connection info: {conn_info.get('redis_version', 'unknown')}")
        
        # Test caching operations
        test_key = f'test_key_{int(time.time())}'
        test_data = {'test': 'data', 'timestamp': time.time()}
        
        cache.set(test_key, test_data, timeout=60)
        retrieved_data = cache.get(test_key)
        
        assert retrieved_data == test_data
        print("✅ Django cache operations working")
        
        # Test workflow status tracking
        workflow_id = f'test_workflow_{int(time.time())}'
        status_data = {
            'campaign_id': 'test_campaign',
            'workflow_type': 'collection',
            'status': 'running',
            'progress': 50
        }
        
        redis_service.set_workflow_status(workflow_id, status_data)
        retrieved_status = redis_service.get_workflow_status(workflow_id)
        
        assert retrieved_status['campaign_id'] == status_data['campaign_id']
        print("✅ Workflow status tracking working")
        
        # Test distributed locking
        lock_name = f'test_lock_{int(time.time())}'
        with redis_service.get_lock(lock_name, timeout=10):
            print("✅ Distributed locking working")
        
        # Cleanup
        redis_service.delete_workflow_status(workflow_id)
        cache.delete(test_key)
        
        return True
        
    except RedisConnectionError as e:
        print(f"❌ Redis connection failed: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Redis infrastructure test failed: {str(e)}")
        return False


def test_airflow_infrastructure():
    """Test Airflow API connectivity."""
    print("\n🔍 Testing Airflow Infrastructure...")
    
    try:
        # Test Airflow service
        airflow_service = AirflowService()
        print("✅ Airflow service initialized successfully")
        
        # Test connection health
        is_healthy = airflow_service._check_connection_health()
        print(f"✅ Airflow health check: {'Healthy' if is_healthy else 'Unhealthy'}")
        
        # Test basic API call (get DAGs)
        try:
            response = airflow_service._make_request('GET', 'dags', params={'limit': 1})
            if response['success']:
                print("✅ Airflow API connectivity working")
            else:
                print(f"⚠️ Airflow API returned error: {response.get('error', 'Unknown')}")
        except AirflowConnectionError as e:
            print(f"⚠️ Airflow API connection failed: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Airflow infrastructure test failed: {str(e)}")
        return False


def test_health_check_service():
    """Test health check service."""
    print("\n🔍 Testing Health Check Service...")
    
    try:
        health_service = HealthCheckService()
        print("✅ Health check service initialized successfully")
        
        # Test individual service checks
        db_health = health_service.check_service('database')
        print(f"✅ Database health: {'Healthy' if db_health.get('healthy') else 'Unhealthy'}")
        
        redis_health = health_service.check_service('redis')
        print(f"✅ Redis health: {'Healthy' if redis_health.get('healthy') else 'Unhealthy'}")
        
        filesystem_health = health_service.check_service('filesystem')
        print(f"✅ Filesystem health: {'Healthy' if filesystem_health.get('healthy') else 'Unhealthy'}")
        
        # Test comprehensive health check
        all_health = health_service.check_all_services()
        print(f"✅ Overall system health: {all_health.get('overall_status', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Health check service test failed: {str(e)}")
        return False


def test_resource_management():
    """Test resource management service."""
    print("\n🔍 Testing Resource Management...")
    
    try:
        resource_manager = ResourceManager()
        print("✅ Resource manager initialized successfully")
        
        # Test resource usage monitoring
        resource_usage = resource_manager.get_resource_usage()
        print(f"✅ Memory usage: {resource_usage.get('memory', {}).get('percent', 'unknown')}%")
        print(f"✅ CPU usage: {resource_usage.get('cpu', {}).get('percent', 'unknown')}%")
        print(f"✅ Disk usage: {resource_usage.get('disk', {}).get('percent', 'unknown')}%")
        
        # Test resource availability check
        availability = resource_manager.check_resources_available()
        print(f"✅ Resources available: {availability.get('available', False)}")
        print(f"✅ Active workflows: {availability.get('active_workflows', 0)}/{availability.get('max_workflows', 0)}")
        
        # Test system info
        system_info = resource_manager.get_system_info()
        print(f"✅ CPU count: {system_info.get('hardware', {}).get('cpu_count', 'unknown')}")
        print(f"✅ Total memory: {system_info.get('hardware', {}).get('memory_total_gb', 'unknown')}GB")
        
        return True
        
    except Exception as e:
        print(f"❌ Resource management test failed: {str(e)}")
        return False


def test_workflow_execution_pipeline():
    """Test workflow execution pipeline."""
    print("\n🔍 Testing Workflow Execution Pipeline...")
    
    try:
        # Create test data
        user = User.objects.create_user(
            username=f'workflow_test_{int(time.time())}',
            email='<EMAIL>'
        )
        campaign = Campaign.objects.create(
            name=f'Workflow Test Campaign {int(time.time())}',
            description='Test campaign for workflow execution',
            target_type='location',
            audience_type='followers',
            creator=user
        )
        
        # Test workflow service
        workflow_service = WorkflowService()
        print("✅ Workflow service initialized successfully")
        
        # Test workflow execution context (without actually running)
        try:
            with workflow_service._workflow_execution_context(str(campaign.id), 'collection') as workflow_execution:
                print(f"✅ Workflow execution context created: {workflow_execution.id}")
                print(f"✅ Workflow status: {workflow_execution.status}")
                
                # Simulate workflow completion
                workflow_execution.complete({'success': True, 'message': 'Test completed'})
                print("✅ Workflow completion simulation successful")
                
        except WorkflowExecutionError as e:
            print(f"⚠️ Workflow execution context failed: {str(e)}")
        
        # Test concurrent workflow limits
        active_count = workflow_service._get_active_workflow_count()
        print(f"✅ Active workflow count: {active_count}")
        
        # Cleanup
        campaign.delete()
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow execution pipeline test failed: {str(e)}")
        return False


def main():
    """Run all infrastructure tests."""
    print("🚀 Starting Django Backend Infrastructure Tests\n")
    
    test_results = {
        'database': test_database_infrastructure(),
        'redis': test_redis_infrastructure(),
        'airflow': test_airflow_infrastructure(),
        'health_check': test_health_check_service(),
        'resource_management': test_resource_management(),
        'workflow_pipeline': test_workflow_execution_pipeline(),
    }
    
    print("\n📊 Test Results Summary:")
    print("=" * 50)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
        if result:
            passed_tests += 1
    
    print("=" * 50)
    print(f"Overall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All backend infrastructure tests passed!")
        print("✅ Production-ready backend reliability achieved")
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
        
    return passed_tests == total_tests


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
