"""
Views for account-related functionality in the campaigns app.
"""
import logging
from django.shortcuts import get_object_or_404
from django.views.generic import ListView
# Removed LoginRequiredMixin to disable authentication requirements
# from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.contenttypes.models import ContentType

from campaigns.models import Campaign, DynamicTag
from campaigns.forms.account_forms import AccountFilterForm, DiscoveryFilterForm
from campaigns.services import CampaignAnalysisService
from instagram.models import Accounts, WhiteListEntry, CustomTaggedItem
from taggit.models import Tag

logger = logging.getLogger(__name__)


class CampaignAccountsView(ListView):
    """
    View to display accounts collected by a campaign with filtering and sorting.
    Supports different view types based on URL pattern.
    """
    template_name = 'campaigns/campaign_accounts.html'
    context_object_name = 'accounts'
    paginate_by = 20

    def get_template_names(self):
        """Return different templates based on the view type."""
        view_type = self.get_view_type()
        if view_type == 'list':
            return ['campaigns/campaign_accounts_list.html']
        elif view_type == 'analyzed':
            return ['campaigns/campaign_accounts_analyzed.html']
        return [self.template_name]

    def get_view_type(self):
        """Determine the view type based on the URL pattern."""
        url_name = self.request.resolver_match.url_name
        if url_name == 'campaign_accounts_list':
            return 'list'
        elif url_name == 'campaign_accounts_analyzed':
            return 'analyzed'
        return 'default'

    def get_queryset(self):
        campaign_id = self.kwargs.get('pk')
        self.campaign = get_object_or_404(Campaign, pk=campaign_id)

        # Get all accounts for this campaign
        queryset = Accounts.objects.filter(campaign_id=str(self.campaign.id))

        # Filter based on view type
        view_type = self.get_view_type()
        if view_type == 'analyzed':
            # Only show accounts that have been analyzed (have whitelist entries or tags)
            analyzed_usernames = WhiteListEntry.objects.filter(
                account__campaign_id=str(self.campaign.id)
            ).values_list('account__username', flat=True)
            queryset = queryset.filter(username__in=analyzed_usernames)

        # Use different forms based on view type
        if view_type == 'list':
            # Stage 1: Discovery view - use simplified form
            self.filter_form = DiscoveryFilterForm(self.request.GET or None)
            if self.filter_form.is_valid():
                queryset = self.filter_form.filter_queryset(queryset, self.campaign)
            else:
                queryset = queryset.order_by('-collection_date')  # Most recent first for list view
        else:
            # Stage 2: Analysis view - use full form
            self.filter_form = AccountFilterForm(self.request.GET or None)
            if self.filter_form.is_valid():
                queryset = self.filter_form.filter_queryset(queryset)
            else:
                queryset = queryset.order_by('-followers')

        # Get content type for Accounts model
        content_type = ContentType.objects.get_for_model(Accounts)

        # Get all tags for these accounts
        account_tags = {}
        tagged_items = CustomTaggedItem.objects.filter(
            content_type=content_type,
            object_id__in=[account.username for account in queryset]
        ).select_related('tag')

        # Group tags by account
        for tagged_item in tagged_items:
            if tagged_item.object_id not in account_tags:
                account_tags[tagged_item.object_id] = []
            account_tags[tagged_item.object_id].append(tagged_item.tag.name)

        # Add tags_list attribute to each account
        for account in queryset:
            account.tags_list = account_tags.get(account.username, [])

        return queryset

    def populate_tag_choices(self, queryset):
        """
        Populate the tag choices for the filter form based on the tags used by the accounts.
        """
        # Get content type for Accounts model
        content_type = ContentType.objects.get_for_model(Accounts)

        # Get all tags used by these accounts
        tag_ids = CustomTaggedItem.objects.filter(
            content_type=content_type,
            object_id__in=queryset.values_list('username', flat=True)
        ).values_list('tag_id', flat=True).distinct()

        # Get the tag names
        tags = Tag.objects.filter(id__in=tag_ids).order_by('name')

        # Set the choices for the tags field
        self.filter_form.fields['tags'].choices = [(tag.name, tag.name) for tag in tags]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['campaign'] = self.campaign
        context['filter_form'] = self.filter_form
        context['view_type'] = self.get_view_type()

        # Get analysis stats
        analysis_service = CampaignAnalysisService()
        context['analysis_stats'] = analysis_service.get_campaign_analysis_stats(self.campaign.id)

        # Get account stats
        context['total_accounts'] = self.get_queryset().count()
        context['whitelisted_accounts'] = WhiteListEntry.objects.filter(
            account__campaign_id=str(self.campaign.id)
        ).count()

        # Calculate conversion rate
        if context['total_accounts'] > 0:
            whitelisted = context['whitelisted_accounts']
            total = context['total_accounts']
            context['conversion_rate'] = (whitelisted / total) * 100
        else:
            context['conversion_rate'] = 0

        # Add source information for accounts in list view
        if context['view_type'] == 'list':
            for account in context['accounts']:
                # Determine source type and details based on campaign targets
                # This is a simplified approach since we don't have direct source tracking
                if self.campaign.location_targets.exists():
                    account.source_type = "location"
                    location_target = self.campaign.location_targets.first()
                    account.source_details = f"{location_target.city}, {location_target.country}"
                elif self.campaign.username_targets.exists():
                    account.source_type = "username"
                    username_target = self.campaign.username_targets.first()
                    account.source_details = f"@{username_target.username} ({username_target.get_audience_type_display()})"
                else:
                    account.source_type = None
                    account.source_details = "Unknown source"

        return context


class CampaignWhiteListView(ListView):
    """
    View to display white listed accounts from a campaign.
    Uses proper campaign tag assignments instead of CEP-related WhiteListEntry.
    """
    template_name = 'campaigns/campaign_whitelist.html'
    context_object_name = 'whitelist_entries'
    paginate_by = 20

    def get_queryset(self):
        campaign_id = self.kwargs.get('pk')
        self.campaign = get_object_or_404(Campaign, pk=campaign_id)

        # Use the export service to get whitelisted accounts
        from campaigns.services.export_service import CampaignExportService
        export_service = CampaignExportService(self.campaign)
        whitelisted_accounts = export_service._get_whitelisted_accounts()

        # Convert to a format compatible with the template
        whitelist_entries = []
        for account_data in whitelisted_accounts:
            # Create a mock entry object that matches the template expectations
            entry = type('WhitelistEntry', (), {
                'account': account_data['account'],
                'tags': account_data['tags_info']['all_tags'],
                'dynamic_tags': [
                    {'name': tag, 'description': '', 'category__name': ''}
                    for tag in account_data['tags_info']['all_tags']
                ]
            })()
            whitelist_entries.append(entry)

        # Apply sorting if provided in request
        sort_by = self.request.GET.get('sort_by')
        if sort_by:
            reverse_sort = sort_by.startswith('-')
            field_name = sort_by.lstrip('-')

            if field_name == 'username':
                whitelist_entries.sort(key=lambda x: x.account.username or '', reverse=reverse_sort)
            elif field_name == 'full_name':
                whitelist_entries.sort(key=lambda x: x.account.full_name or '', reverse=reverse_sort)
            elif field_name == 'followers':
                whitelist_entries.sort(key=lambda x: x.account.followers or 0, reverse=reverse_sort)
            elif field_name == 'following':
                whitelist_entries.sort(key=lambda x: x.account.following or 0, reverse=reverse_sort)
            elif field_name == 'number_of_posts':
                whitelist_entries.sort(key=lambda x: x.account.number_of_posts or 0, reverse=reverse_sort)
        else:
            # Default sorting by username
            whitelist_entries.sort(key=lambda x: x.account.username or '')

        return whitelist_entries

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['campaign'] = self.campaign

        # Use the export service to get stats
        from campaigns.services.export_service import CampaignExportService
        export_service = CampaignExportService(self.campaign)
        stats = export_service.get_whitelist_stats()

        context['total_accounts'] = stats['total_accounts']
        context['whitelisted_accounts'] = stats['whitelisted_accounts']
        context['conversion_rate'] = stats['conversion_rate']

        # The dynamic tags are already set in get_queryset, no need to process them again
        logger.info(f"Whitelist context: {context['whitelisted_accounts']} of {context['total_accounts']} accounts whitelisted")

        return context
