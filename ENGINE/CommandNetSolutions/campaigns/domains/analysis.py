"""
Analysis Domain Service.
Responsible for account analysis, scoring, and tagging.
"""
from django.utils import timezone
from campaigns.models import Campaign, CampaignResult


class AnalysisDomain:
    """
    Domain service for analysis operations.
    Encapsulates business logic for analyzing accounts.
    """

    def __init__(self, repository, tagging_system=None):
        self.repository = repository
        self.tagging_system = tagging_system

    def analyze_campaign(self, campaign_id, options=None):
        """
        Analyze accounts collected by a campaign.

        Args:
            campaign_id (uuid): Campaign ID
            options (dict): Optional analysis options

        Returns:
            dict: Analysis results

        Raises:
            Campaign.DoesNotExist: If campaign not found
        """
        # Get campaign
        campaign = self.repository.get_by_id(campaign_id)

        # Get accounts to analyze
        from instagram.models import Accounts
        accounts = Accounts.objects.filter(campaign=campaign)

        # Initialize counters
        total = accounts.count()
        processed = 0
        white_listed = 0

        # Process accounts
        for account in accounts:
            # Skip if no tagging system
            if not self.tagging_system:
                processed += 1
                continue

            # Process account
            result = self.tagging_system.process_account(account)

            # Apply dynamic tags based on campaign tags
            try:
                # Get all available tags (simplified - no global filtering)
                from campaigns.models import DynamicTag
                from django.db.models import Q
                campaign_tags = DynamicTag.objects.all().distinct()

                if campaign_tags.exists():
                    dynamic_tags = self._process_dynamic_tags(account, campaign_tags)
                    result['tags'].extend(dynamic_tags)
            except Exception as e:
                # Log error but continue processing
                print(f"Error processing dynamic tags: {str(e)}")

            # Update whitelist
            from instagram.models import WhiteListEntry
            qualifies = bool(result.get('privileges'))

            if qualifies:
                WhiteListEntry.objects.update_or_create(
                    account=account,
                    defaults={
                        'tags': result['tags'],
                        'is_auto': True,
                        **result['privileges']
                    }
                )
                white_listed += 1
            else:
                # Remove from whitelist if exists
                WhiteListEntry.objects.filter(account=account).delete()

            processed += 1

        # Update campaign result
        result, created = CampaignResult.objects.get_or_create(campaign=campaign)
        result.total_accounts_processed = processed
        result.last_processed_at = timezone.now()
        result.save()

        return {
            'total': total,
            'processed': processed,
            'white_listed': white_listed,
            'percentage': (white_listed / total * 100) if total > 0 else 0
        }

    def get_analysis_stats(self, campaign_id):
        """
        Get analysis statistics for a campaign.

        Args:
            campaign_id (uuid): Campaign ID

        Returns:
            dict: Analysis statistics

        Raises:
            Campaign.DoesNotExist: If campaign not found
        """
        # Get campaign
        campaign = self.repository.get_by_id(campaign_id)

        # Get campaign result
        from campaigns.models import CampaignResult
        result, created = CampaignResult.objects.get_or_create(campaign=campaign)

        # Get total accounts
        from instagram.models import Accounts
        total_accounts = Accounts.objects.filter(campaign=campaign).count()

        # Get whitelisted accounts
        from instagram.models import WhiteListEntry
        white_listed = WhiteListEntry.objects.filter(
            account__campaign=campaign
        ).count()

        return {
            'total': total_accounts,
            'processed': result.total_accounts_processed,
            'white_listed': white_listed,
            'percentage': (white_listed / total_accounts * 100) if total_accounts > 0 else 0,
            'last_processed_at': result.last_processed_at
        }

    def _process_dynamic_tags(self, account, dynamic_tags):
        """
        Process dynamic tags for an account.

        Args:
            account: Account instance
            dynamic_tags: QuerySet of DynamicTag objects

        Returns:
            list: List of matched tag names
        """
        import re

        matched_tags = []

        for tag in dynamic_tags:
            field_value = getattr(account, tag.field, None)

            # Skip if field value is None
            if field_value is None:
                continue

            # Convert to string if it's not already
            if isinstance(field_value, list):
                # For array fields like interests, locations, etc.
                field_value = ' '.join(str(item) for item in field_value if item)
            else:
                field_value = str(field_value)

            # Process based on tag type
            if tag.tag_type == 'keyword':
                # Split pattern into keywords
                keywords = [k.strip().lower() for k in tag.pattern.split(',')]
                if any(keyword in field_value.lower() for keyword in keywords):
                    matched_tags.append(f"dynamic_{tag.name}")

            elif tag.tag_type == 'regex':
                try:
                    if re.search(tag.pattern, field_value, re.IGNORECASE):
                        matched_tags.append(f"dynamic_{tag.name}")
                except:
                    # Invalid regex pattern
                    pass

            elif tag.tag_type == 'sentiment':
                # Basic sentiment analysis
                try:
                    threshold = float(tag.pattern)
                    # Simple sentiment scoring based on positive/negative word lists
                    positive_words = ['good', 'great', 'excellent', 'amazing', 'love', 'best', 'happy']
                    negative_words = ['bad', 'worst', 'hate', 'terrible', 'awful', 'poor']

                    words = field_value.lower().split()
                    positive_count = sum(1 for word in words if word in positive_words)
                    negative_count = sum(1 for word in words if word in negative_words)

                    if words:
                        sentiment_score = (positive_count - negative_count) / len(words)
                        if sentiment_score >= threshold:
                            matched_tags.append(f"dynamic_{tag.name}")
                except:
                    # Invalid threshold or other error
                    pass

            elif tag.tag_type == 'category':
                # Category classification
                categories = [c.strip().lower() for c in tag.pattern.split(',')]

                # Simple category matching based on keyword presence
                for category in categories:
                    if category in field_value.lower():
                        matched_tags.append(f"dynamic_{tag.name}_{category}")

        return matched_tags
