{% extends "campaigns/base.html" %}

{% block title %}{% if form.instance.id %}Edit{% else %}Create{% endif %} Tag Group{% endblock %}

{% block campaign_content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">{% if form.instance.id %}Edit{% else %}Create{% endif %} Tag Group</h1>
            <p class="page-subtitle">{% if form.instance.id %}Update the details of this tag group{% else %}Create a new tag group to organize your tags{% endif %}</p>
        </div>
        <div>
            <a href="{% url 'campaigns:tag_group_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-tags me-2"></i>Tag Group Information</h5>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}

                <div class="mb-3">
                    <label for="{{ form.name.id_for_label }}" class="form-label">Name</label>
                    {{ form.name.errors }}
                    <input type="text" name="{{ form.name.name }}" id="{{ form.name.id_for_label }}" class="form-control {% if form.name.errors %}is-invalid{% endif %}" value="{{ form.name.value|default:'' }}" required>
                    <div class="form-text">Enter a descriptive name for this tag group.</div>
                </div>

                <div class="mb-3">
                    <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                    {{ form.description.errors }}
                    <textarea name="{{ form.description.name }}" id="{{ form.description.id_for_label }}" class="form-control {% if form.description.errors %}is-invalid{% endif %}" rows="3">{{ form.description.value|default:'' }}</textarea>
                    <div class="form-text">Provide a brief description of this tag group's purpose.</div>
                </div>

                <div class="mb-3">
                    <label for="{{ form.color.id_for_label }}" class="form-label">Color</label>
                    {{ form.color.errors }}
                    <input type="color" name="{{ form.color.name }}" id="{{ form.color.id_for_label }}" class="form-control form-control-color {% if form.color.errors %}is-invalid{% endif %}" value="{{ form.color.value|default:'#6c757d' }}">
                    <div class="form-text">Choose a color for this tag group.</div>
                </div>

                <!-- Global tag group functionality removed as per requirements -->

                {% if form.instance.id %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    After saving, you can add tags to this group from the tag group detail page.
                </div>
                {% endif %}

                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> {% if form.instance.id %}Update{% else %}Create{% endif %} Tag Group
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
