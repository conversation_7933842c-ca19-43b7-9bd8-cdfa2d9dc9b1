"""
Campaign Data Service

This service provides consistent data calculation and retrieval for campaign views,
fixing discrepancies between campaign detail views and export data.

Features:
- Centralized data calculation logic
- Consistent statistics across all views
- Real-time data synchronization
- Proper error handling and logging
"""

import logging
from typing import Dict, List, Any, Optional
from django.db.models import Count, Q, Avg, Sum
from django.utils import timezone

from campaigns.models import (
    Campaign, CampaignResult, CampaignTag, TagAnalysisResult
)
from instagram.models import Accounts, WhiteListEntry

logger = logging.getLogger(__name__)


class CampaignDataService:
    """
    Service for consistent campaign data calculation and retrieval.
    """

    def __init__(self, campaign: Campaign):
        self.campaign = campaign

    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive campaign statistics with consistent calculations.

        Returns:
            Dict containing all campaign statistics
        """
        try:
            # Get basic account counts
            total_accounts = self._get_total_accounts()
            analyzed_accounts = self._get_analyzed_accounts_count()
            whitelisted_accounts = self._get_whitelisted_accounts_count()

            # Calculate rates
            analysis_rate = (analyzed_accounts / total_accounts * 100) if total_accounts > 0 else 0
            conversion_rate = (whitelisted_accounts / analyzed_accounts * 100) if analyzed_accounts > 0 else 0

            # Get workflow statistics
            workflow_stats = self._get_workflow_statistics()

            # Get tag statistics
            tag_stats = self._get_tag_statistics()

            # Get recent accounts data
            recent_data = self._get_recent_accounts_data()

            return {
                'accounts': {
                    'total': total_accounts,
                    'analyzed': analyzed_accounts,
                    'whitelisted': whitelisted_accounts,
                    'pending': total_accounts - analyzed_accounts
                },
                'rates': {
                    'analysis_rate': round(analysis_rate, 1),
                    'conversion_rate': round(conversion_rate, 1)
                },
                'workflow': workflow_stats,
                'tags': tag_stats,
                'recent': recent_data,
                'last_updated': timezone.now()
            }
        except Exception as e:
            logger.error(f"Error getting comprehensive stats for campaign {self.campaign.id}: {str(e)}")
            return self._get_fallback_stats()

    def get_stage_specific_stats(self, stage: int) -> Dict[str, Any]:
        """
        Get statistics specific to a workflow stage.

        Args:
            stage (int): Workflow stage (1=Discovery, 2=Analysis, 3=Tagging)

        Returns:
            Dict containing stage-specific statistics
        """
        if stage == 1:
            return self._get_discovery_stats()
        elif stage == 2:
            return self._get_analysis_stats()
        elif stage == 3:
            return self._get_tagging_stats()
        else:
            return {}

    def _get_total_accounts(self) -> int:
        """Get total accounts collected for this campaign."""
        return Accounts.objects.filter(campaign_id=str(self.campaign.id)).count()

    def _get_analyzed_accounts_count(self) -> int:
        """Get count of accounts that have been analyzed."""
        # Accounts are considered analyzed if they have tag analysis results
        analyzed_usernames = TagAnalysisResult.objects.filter(
            campaign=self.campaign
        ).values_list('account_id', flat=True).distinct()

        return len(set(analyzed_usernames))

    def _get_whitelisted_accounts_count(self) -> int:
        """Get count of whitelisted accounts."""
        return WhiteListEntry.objects.filter(
            account__campaign_id=str(self.campaign.id)
        ).count()

    def _get_workflow_statistics(self) -> Dict[str, Any]:
        """Get workflow execution statistics."""
        from campaigns.models import WorkflowExecution

        workflows = WorkflowExecution.objects.filter(campaign=self.campaign)

        return {
            'total_executions': workflows.count(),
            'completed': workflows.filter(status='completed').count(),
            'running': workflows.filter(status='running').count(),
            'failed': workflows.filter(status='failed').count(),
            'last_execution': workflows.order_by('-start_time').first()
        }

    def _get_tag_statistics(self) -> Dict[str, Any]:
        """Get tag-related statistics."""
        campaign_tags = CampaignTag.objects.filter(campaign=self.campaign)
        tag_results = TagAnalysisResult.objects.filter(campaign=self.campaign)

        return {
            'total_tags': campaign_tags.count(),
            'required_tags': campaign_tags.filter(is_required=True).count(),
            'optional_tags': campaign_tags.filter(is_required=False).count(),
            'total_matches': tag_results.filter(matched=True).count(),
            'average_confidence': tag_results.aggregate(
                avg=Avg('confidence_score')
            )['avg'] or 0
        }

    def _get_recent_accounts_data(self) -> Dict[str, List[Dict]]:
        """Get recent accounts data for all stages."""
        return {
            'discoveries': self._get_recent_discoveries(),
            'analyzed': self._get_recent_analyzed(),
            'whitelisted': self._get_recent_whitelisted()
        }

    def _get_recent_discoveries(self) -> List[Dict[str, Any]]:
        """Get recent account discoveries."""
        recent_accounts = Accounts.objects.filter(
            campaign_id=str(self.campaign.id)
        ).order_by('-collection_date')[:5]

        discoveries = []
        for account in recent_accounts:
            # Determine source based on campaign targets
            source = self._determine_account_source(account)

            discoveries.append({
                'username': account.username,
                'source': source,
                'discovered_at': account.collection_date,
                'followers': account.followers or 0
            })

        return discoveries

    def _get_recent_analyzed(self) -> List[Dict[str, Any]]:
        """Get recently analyzed accounts."""
        # Get accounts that have tag analysis results
        analyzed_usernames = TagAnalysisResult.objects.filter(
            campaign=self.campaign
        ).values_list('account_id', flat=True).distinct()

        recent_analyzed = Accounts.objects.filter(
            campaign_id=str(self.campaign.id),
            username__in=analyzed_usernames
        ).order_by('-collection_date')[:5]

        analyzed = []
        for account in recent_analyzed:
            # Get tag count for this account
            tag_count = TagAnalysisResult.objects.filter(
                campaign=self.campaign,
                account_id=account.username,
                matched=True
            ).count()

            analyzed.append({
                'username': account.username,
                'followers': account.followers or 0,
                'tag_count': tag_count,
                'analyzed_at': account.collection_date
            })

        return analyzed

    def _get_recent_whitelisted(self) -> List[Dict[str, Any]]:
        """Get recently whitelisted accounts."""
        recent_whitelist = WhiteListEntry.objects.filter(
            account__campaign_id=str(self.campaign.id)
        ).select_related('account').order_by('-last_updated')[:5]

        whitelisted = []
        for entry in recent_whitelist:
            # Get required tags that this account matched
            required_tags = TagAnalysisResult.objects.filter(
                campaign=self.campaign,
                account_id=entry.account.username,
                matched=True
            ).filter(
                tag_name__in=CampaignTag.objects.filter(
                    campaign=self.campaign,
                    is_required=True
                ).values_list('tag__name', flat=True)
            ).values_list('tag_name', flat=True)

            whitelisted.append({
                'username': entry.account.username,
                'followers': entry.account.followers or 0,
                'required_tags': list(required_tags),
                'whitelisted_at': entry.last_updated
            })

        return whitelisted

    def _determine_account_source(self, account: Accounts) -> str:
        """Determine the source of an account based on campaign targets."""
        if self.campaign.location_targets.exists():
            location_target = self.campaign.location_targets.first()
            return f"Location: {location_target.city}, {location_target.country}"
        elif self.campaign.username_targets.exists():
            username_target = self.campaign.username_targets.first()
            return f"Followers of: @{username_target.username}"
        else:
            return "Unknown"

    def _get_discovery_stats(self) -> Dict[str, Any]:
        """Get Stage 1 (Discovery) specific statistics."""
        total_accounts = self._get_total_accounts()

        # Get source distribution
        location_count = 0
        username_count = 0

        if self.campaign.target_type in ['location', 'mixed']:
            location_count = total_accounts  # Simplified for now
        if self.campaign.target_type in ['username', 'mixed']:
            username_count = total_accounts  # Simplified for now

        return {
            'total_discovered': total_accounts,
            'from_locations': location_count,
            'from_usernames': username_count,
            'discovery_rate': 100 if total_accounts > 0 else 0
        }

    def _get_analysis_stats(self) -> Dict[str, Any]:
        """Get Stage 2 (Analysis) specific statistics."""
        total_accounts = self._get_total_accounts()
        analyzed_accounts = self._get_analyzed_accounts_count()

        return {
            'total_accounts': total_accounts,
            'analyzed_accounts': analyzed_accounts,
            'pending_analysis': total_accounts - analyzed_accounts,
            'analysis_rate': (analyzed_accounts / total_accounts * 100) if total_accounts > 0 else 0
        }

    def _get_tagging_stats(self) -> Dict[str, Any]:
        """Get Stage 3 (Tagging) specific statistics."""
        analyzed_accounts = self._get_analyzed_accounts_count()
        whitelisted_accounts = self._get_whitelisted_accounts_count()

        return {
            'analyzed_accounts': analyzed_accounts,
            'whitelisted_accounts': whitelisted_accounts,
            'filtered_out': analyzed_accounts - whitelisted_accounts,
            'conversion_rate': (whitelisted_accounts / analyzed_accounts * 100) if analyzed_accounts > 0 else 0
        }

    def _get_fallback_stats(self) -> Dict[str, Any]:
        """Get fallback statistics in case of errors."""
        return {
            'accounts': {'total': 0, 'analyzed': 0, 'whitelisted': 0, 'pending': 0},
            'rates': {'analysis_rate': 0, 'conversion_rate': 0},
            'workflow': {'total_executions': 0, 'completed': 0, 'running': 0, 'failed': 0},
            'tags': {'total_tags': 0, 'required_tags': 0, 'optional_tags': 0},
            'recent': {'discoveries': [], 'analyzed': [], 'whitelisted': []},
            'error': True,
            'last_updated': timezone.now()
        }

    def sync_campaign_result(self) -> CampaignResult:
        """
        Synchronize CampaignResult with actual data.

        Returns:
            Updated CampaignResult instance
        """
        try:
            result, created = CampaignResult.objects.get_or_create(
                campaign=self.campaign,
                defaults={
                    'total_accounts_found': 0,
                    'total_accounts_processed': 0,
                    'total_accounts_pending': 0
                }
            )

            # Update with current data
            stats = self.get_comprehensive_stats()

            result.total_accounts_found = stats['accounts']['total']
            result.total_accounts_processed = stats['accounts']['analyzed']
            result.total_accounts_pending = stats['accounts']['pending']
            result.total_accounts_whitelisted = stats['accounts']['whitelisted']
            # Note: average_confidence_score field removed as per requirements
            result.last_processed_at = timezone.now()

            # Calculate and update quality score
            result.calculate_quality_score()
            result.save()

            logger.info(f"Synchronized CampaignResult for campaign {self.campaign.id}")
            return result

        except Exception as e:
            logger.error(f"Error syncing CampaignResult for campaign {self.campaign.id}: {str(e)}")
            raise
