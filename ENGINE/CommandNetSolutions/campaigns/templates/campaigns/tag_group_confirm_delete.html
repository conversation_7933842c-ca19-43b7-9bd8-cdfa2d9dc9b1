{% extends "campaigns/base.html" %}

{% block title %}Delete Tag Group{% endblock %}

{% block campaign_content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">Delete Tag Group</h1>
            <p class="page-subtitle">Are you sure you want to delete this tag group?</p>
        </div>
        <div>
            <a href="{% url 'campaigns:tag_group_detail' tag_group.id %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <h5 class="alert-heading"><i class="fas fa-exclamation-circle me-2"></i>Warning!</h5>
                <p>You are about to delete the tag group <strong>"{{ tag_group.name }}"</strong>. This action cannot be undone.</p>
                <p>This will remove the tag group association from all tags in this group, but will not delete the tags themselves.</p>
            </div>

            <div class="mb-4">
                <h6 class="fw-bold">Tag Group Details:</h6>
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>Name:</span>
                        <span class="fw-bold">{{ tag_group.name }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>Description:</span>
                        <span>{{ tag_group.description|default:"No description" }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>Tags in group:</span>
                        <span class="badge bg-primary rounded-pill">{{ tag_group.tags.count }}</span>
                    </li>
                    <!-- Global status removed as per requirements -->
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>Created:</span>
                        <span>{{ tag_group.created_at|date:"M d, Y" }}</span>
                    </li>
                </ul>
            </div>

            <form method="post">
                {% csrf_token %}
                <div class="d-flex justify-content-between">
                    <a href="{% url 'campaigns:tag_group_detail' tag_group.id %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i> Delete Tag Group
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
