# Generated by Django 4.2.16 on 2025-05-31 18:03

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("campaigns", "0011_cepworkflow_daily_limit_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="WorkflowTemplate",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.Char<PERSON>ield(
                        help_text="Name of the workflow template", max_length=255
                    ),
                ),
                (
                    "description",
                    models.TextField(help_text="Description of the workflow template"),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("growth", "Growth Focused"),
                            ("engagement", "Engagement Heavy"),
                            ("conservative", "Conservative Outreach"),
                            ("discovery", "Account Discovery"),
                            ("custom", "Custom Template"),
                        ],
                        default="custom",
                        help_text="Category of the workflow template",
                        max_length=20,
                    ),
                ),
                (
                    "available_actions",
                    models.J<PERSON><PERSON><PERSON>(
                        default=dict,
                        help_text="Available actions and their configurations",
                    ),
                ),
                (
                    "default_daily_limit",
                    models.IntegerField(
                        default=50, help_text="Default maximum actions per day"
                    ),
                ),
                (
                    "default_hourly_limit",
                    models.IntegerField(
                        default=10, help_text="Default maximum actions per hour"
                    ),
                ),
                (
                    "default_delay_between_actions",
                    models.IntegerField(
                        default=30, help_text="Default delay between actions in seconds"
                    ),
                ),
                (
                    "dag_structure",
                    models.JSONField(
                        default=dict,
                        help_text="DAG structure defining action sequence and dependencies",
                    ),
                ),
                (
                    "is_predefined",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this is a predefined system template",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this template is available for use",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Workflow Template",
                "verbose_name_plural": "Workflow Templates",
                "db_table": "campaigns_workflow_template",
                "ordering": ["category", "name"],
            },
        ),
    ]
