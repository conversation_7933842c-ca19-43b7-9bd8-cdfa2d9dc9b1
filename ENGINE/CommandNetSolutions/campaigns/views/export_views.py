"""
Export views for campaign data.
Handles proper data export without CEP-related fields.
"""
import logging
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404, redirect
from django.views import View
from campaigns.models import Campaign
from campaigns.services.export_service import CampaignExportService

logger = logging.getLogger(__name__)


class CampaignWhitelistExportView(View):
    """
    View to export campaign whitelist data in various formats.
    Excludes CEP-related fields and uses proper campaign tag assignments.
    """

    def get(self, request, campaign_id):
        """
        Handle whitelist export requests.
        """
        try:
            # Get campaign
            campaign = get_object_or_404(Campaign, pk=campaign_id)
            
            # Get export format
            export_format = request.GET.get('format', 'csv').lower()
            
            logger.info(f"Exporting whitelist for campaign {campaign.id} in {export_format} format")
            
            # Create export service
            export_service = CampaignExportService(campaign)
            
            # Handle different export formats
            if export_format == 'csv':
                return export_service.export_whitelist_csv()
            elif export_format == 'excel' or export_format == 'xlsx':
                response = export_service.export_whitelist_excel()
                if response is None:
                    messages.error(request, "Excel export requires xlsxwriter package. Using CSV instead.")
                    return export_service.export_whitelist_csv()
                return response
            else:
                messages.error(request, f"Unsupported export format: {export_format}")
                return redirect('campaigns:campaign_whitelist', pk=campaign.id)
                
        except Exception as e:
            logger.exception(f"Error exporting whitelist for campaign {campaign_id}: {str(e)}")
            messages.error(request, f"Error exporting whitelist: {str(e)}")
            return redirect('campaigns:campaign_whitelist', pk=campaign_id)


class CampaignAccountsExportView(View):
    """
    View to export all campaign accounts data.
    """

    def get(self, request, campaign_id):
        """
        Handle accounts export requests.
        """
        try:
            # Get campaign
            campaign = get_object_or_404(Campaign, pk=campaign_id)
            
            # Get export format
            export_format = request.GET.get('format', 'csv').lower()
            
            logger.info(f"Exporting accounts for campaign {campaign.id} in {export_format} format")
            
            # Create export service
            export_service = CampaignExportService(campaign)
            
            # For now, redirect to whitelist export
            # In the future, this could export all accounts, not just whitelisted ones
            if export_format == 'csv':
                return export_service.export_whitelist_csv()
            elif export_format == 'excel' or export_format == 'xlsx':
                response = export_service.export_whitelist_excel()
                if response is None:
                    messages.error(request, "Excel export requires xlsxwriter package. Using CSV instead.")
                    return export_service.export_whitelist_csv()
                return response
            else:
                messages.error(request, f"Unsupported export format: {export_format}")
                return redirect('campaigns:campaign_accounts_analyzed', pk=campaign.id)
                
        except Exception as e:
            logger.exception(f"Error exporting accounts for campaign {campaign_id}: {str(e)}")
            messages.error(request, f"Error exporting accounts: {str(e)}")
            return redirect('campaigns:campaign_accounts_analyzed', pk=campaign_id)


class CampaignDiscoveryExportView(View):
    """
    View to export campaign discovery data.
    """

    def get(self, request, campaign_id):
        """
        Handle discovery export requests.
        """
        try:
            from instagram.models import Accounts
            import csv
            
            # Get campaign
            campaign = get_object_or_404(Campaign, pk=campaign_id)
            
            # Get export format
            export_format = request.GET.get('format', 'csv').lower()
            
            logger.info(f"Exporting discovery data for campaign {campaign.id} in {export_format} format")
            
            if export_format != 'csv':
                messages.error(request, "Only CSV format is supported for discovery export.")
                return redirect('campaigns:campaign_accounts_list', pk=campaign.id)
            
            # Get all accounts for this campaign (discovery data)
            accounts = Accounts.objects.filter(
                campaign_id=str(campaign.id)
            ).order_by('username')
            
            # Create CSV response
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="campaign_{campaign.id}_discovery.csv"'
            
            writer = csv.writer(response)
            
            # Write headers
            headers = [
                'Username',
                'Full Name',
                'Bio',
                'Followers',
                'Following',
                'Posts',
                'Account Type',
                'Verified',
                'Phone Number',
                'Interests',
                'Locations',
                'Links',
                'Collection Date'
            ]
            writer.writerow(headers)
            
            # Write data rows
            for account in accounts:
                writer.writerow([
                    account.username,
                    account.full_name or '',
                    account.bio or '',
                    account.followers or 0,
                    account.following or 0,
                    account.number_of_posts or 0,
                    account.account_type or '',
                    'Yes' if account.is_verified else 'No',
                    account.phone_number or '',
                    ', '.join(account.interests) if account.interests else '',
                    ', '.join(account.locations) if account.locations else '',
                    ', '.join(account.links) if account.links else '',
                    account.collection_date.strftime('%Y-%m-%d %H:%M:%S') if account.collection_date else ''
                ])
            
            logger.info(f"Exported {accounts.count()} discovery accounts to CSV")
            return response
                
        except Exception as e:
            logger.exception(f"Error exporting discovery data for campaign {campaign_id}: {str(e)}")
            messages.error(request, f"Error exporting discovery data: {str(e)}")
            return redirect('campaigns:campaign_accounts_list', pk=campaign_id)


class LegacyCampaignExportView(View):
    """
    Legacy export view for backward compatibility.
    Redirects to appropriate new export views.
    """

    def get(self, request, pk):
        """
        Handle legacy export requests and redirect to new views.
        """
        export_type = request.GET.get('type', 'accounts')
        export_format = request.GET.get('format', 'csv')
        
        logger.info(f"Legacy export request: type={export_type}, format={export_format}")
        
        # Redirect to appropriate new export view
        if export_type == 'whitelist':
            return redirect(f"/campaigns/{pk}/export/whitelist/?format={export_format}")
        elif export_type == 'accounts':
            return redirect(f"/campaigns/{pk}/export/accounts/?format={export_format}")
        elif export_type == 'discovery':
            return redirect(f"/campaigns/{pk}/export/discovery/?format={export_format}")
        else:
            messages.error(request, f"Unknown export type: {export_type}")
            return redirect('campaigns:campaign_detail', pk=pk)
