{% extends 'campaigns/base.html' %}
{% load static %}

{% block title %}Create Workflow{% endblock %}

{% block extra_css %}
<link href="{% static 'campaigns/css/workflow-create.css' %}" rel="stylesheet">
{% endblock %}

{% block campaign_content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Create New Workflow</h1>
            <p class="text-muted">Set up a new workflow execution using predefined templates</p>
        </div>
        <a href="{% url 'campaigns:workflow_dashboard' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
        </a>
    </div>

    <form method="post" id="workflow-create-form">
        {% csrf_token %}
        
        <!-- Step 1: Campaign Selection -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <span class="badge bg-primary me-2">1</span>
                    Select Campaign
                </h5>
            </div>
            <div class="card-body">
                {% if available_campaigns %}
                    <div class="row">
                        {% for campaign in available_campaigns %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card campaign-card h-100" data-campaign-id="{{ campaign.id }}">
                                    <div class="card-body">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="campaign_id" 
                                                   id="campaign_{{ campaign.id }}" value="{{ campaign.id }}" required>
                                            <label class="form-check-label w-100" for="campaign_{{ campaign.id }}">
                                                <h6 class="card-title mb-2">{{ campaign.name }}</h6>
                                                <p class="card-text small text-muted mb-2">{{ campaign.description|truncatechars:80 }}</p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <i class="fas fa-users me-1"></i>{{ campaign.whitelist_count }} accounts
                                                    </small>
                                                    <small class="text-muted">
                                                        {{ campaign.created_at|date:"M d, Y" }}
                                                    </small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No campaigns available for workflow creation. Please ensure you have completed campaigns with whitelist entries.
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Step 2: Template Selection -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <span class="badge bg-primary me-2">2</span>
                    Choose Workflow Template
                </h5>
            </div>
            <div class="card-body">
                {% if workflow_templates %}
                    <div class="row">
                        {% for template in workflow_templates %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card template-card h-100" data-template-id="{{ template.id }}">
                                    <div class="card-body">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="template_id" 
                                                   id="template_{{ template.id }}" value="{{ template.id }}" required>
                                            <label class="form-check-label w-100" for="template_{{ template.id }}">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="card-title mb-0">{{ template.name }}</h6>
                                                    <span class="badge bg-secondary">{{ template.get_category_display }}</span>
                                                </div>
                                                <p class="card-text small text-muted mb-3">{{ template.description }}</p>
                                                
                                                <!-- Template Actions -->
                                                <div class="mb-2">
                                                    <small class="text-muted d-block mb-1">Included Actions:</small>
                                                    <div class="d-flex flex-wrap gap-1">
                                                        {% for action, config in template.available_actions.items %}
                                                            {% if config.enabled %}
                                                                <span class="badge bg-light text-dark">{{ action|title }}</span>
                                                            {% endif %}
                                                        {% empty %}
                                                            <span class="badge bg-light text-dark">No actions configured</span>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                                
                                                <!-- Template Limits -->
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock me-1"></i>{{ template.default_delay_between_actions }}s delay
                                                    </small>
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar me-1"></i>{{ template.default_daily_limit }}/day
                                                    </small>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No workflow templates available. Please contact your administrator.
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Step 3: Workflow Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <span class="badge bg-primary me-2">3</span>
                    Configure Workflow
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="workflow_name" class="form-label">Workflow Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="workflow_name" name="workflow_name" 
                                   placeholder="Enter workflow name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="workflow_description" class="form-label">Description</label>
                            <textarea class="form-control" id="workflow_description" name="workflow_description" 
                                      rows="3" placeholder="Optional description"></textarea>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="daily_limit" class="form-label">Daily Action Limit</label>
                            <input type="number" class="form-control" id="daily_limit" name="daily_limit" 
                                   value="50" min="1" max="500">
                            <div class="form-text">Maximum number of actions per day</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="hourly_limit" class="form-label">Hourly Action Limit</label>
                            <input type="number" class="form-control" id="hourly_limit" name="hourly_limit" 
                                   value="10" min="1" max="100">
                            <div class="form-text">Maximum number of actions per hour</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="delay_between_actions" class="form-label">Delay Between Actions (seconds)</label>
                            <input type="number" class="form-control" id="delay_between_actions" name="delay_between_actions" 
                                   value="30" min="10" max="300">
                            <div class="form-text">Delay between individual actions</div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Selection -->
                <div class="mt-4">
                    <h6 class="mb-3">Select Actions to Enable</h6>
                    <div id="action-selection" class="row">
                        <!-- Will be populated by JavaScript based on selected template -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="d-flex justify-content-end gap-2 mb-4">
            <a href="{% url 'campaigns:workflow_dashboard' %}" class="btn btn-outline-secondary">Cancel</a>
            <button type="submit" class="btn btn-primary" id="create-workflow-btn" disabled>
                <i class="fas fa-plus me-2"></i>Create Workflow
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const campaignCards = document.querySelectorAll('.campaign-card');
    const templateCards = document.querySelectorAll('.template-card');
    const createBtn = document.getElementById('create-workflow-btn');
    const actionSelection = document.getElementById('action-selection');
    
    // Template data for action configuration
    const templateData = {
        {% for template in workflow_templates %}
        '{{ template.id }}': {
            'name': '{{ template.name }}',
            'actions': {{ template.available_actions|safe }},
            'daily_limit': {{ template.default_daily_limit }},
            'hourly_limit': {{ template.default_hourly_limit }},
            'delay': {{ template.default_delay_between_actions }}
        },
        {% endfor %}
    };
    
    // Handle campaign selection
    campaignCards.forEach(card => {
        card.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            
            // Update visual selection
            campaignCards.forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            
            checkFormValidity();
        });
    });
    
    // Handle template selection
    templateCards.forEach(card => {
        card.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            
            // Update visual selection
            templateCards.forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            
            // Update configuration based on template
            updateConfigurationFromTemplate(radio.value);
            
            checkFormValidity();
        });
    });
    
    function updateConfigurationFromTemplate(templateId) {
        const template = templateData[templateId];
        if (!template) return;
        
        // Update limits
        document.getElementById('daily_limit').value = template.daily_limit;
        document.getElementById('hourly_limit').value = template.hourly_limit;
        document.getElementById('delay_between_actions').value = template.delay;
        
        // Update action selection
        actionSelection.innerHTML = '';
        
        Object.entries(template.actions).forEach(([action, config]) => {
            const actionDiv = document.createElement('div');
            actionDiv.className = 'col-md-4 col-sm-6 mb-2';
            
            actionDiv.innerHTML = `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="enabled_actions" 
                           value="${action}" id="action_${action}" ${config.enabled ? 'checked' : ''}>
                    <label class="form-check-label" for="action_${action}">
                        ${action.charAt(0).toUpperCase() + action.slice(1).replace('_', ' ')}
                    </label>
                </div>
            `;
            
            actionSelection.appendChild(actionDiv);
        });
    }
    
    function checkFormValidity() {
        const campaignSelected = document.querySelector('input[name="campaign_id"]:checked');
        const templateSelected = document.querySelector('input[name="template_id"]:checked');
        const workflowName = document.getElementById('workflow_name').value.trim();
        
        createBtn.disabled = !(campaignSelected && templateSelected && workflowName);
    }
    
    // Check form validity on input changes
    document.getElementById('workflow_name').addEventListener('input', checkFormValidity);
    
    // Form submission
    document.getElementById('workflow-create-form').addEventListener('submit', function(e) {
        const selectedActions = document.querySelectorAll('input[name="enabled_actions"]:checked');
        
        if (selectedActions.length === 0) {
            e.preventDefault();
            alert('Please select at least one action to enable.');
            return;
        }
        
        // Show loading state
        createBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
        createBtn.disabled = true;
    });
});
</script>

<style>
.campaign-card, .template-card {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.campaign-card:hover, .template-card:hover {
    border-color: #0d6efd;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.campaign-card.selected, .template-card.selected {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}

.form-check-input:checked + .form-check-label {
    color: #0d6efd;
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}
