"""
Views for the resource manager dashboard.
"""
from django.views.generic import TemplateView, View
# Removed LoginRequiredMixin to disable authentication requirements
# from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.shortcuts import get_object_or_404, render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
# Removed IsAuthenticated to disable authentication requirements
# from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
import heapq

from campaigns.services.mock_resource_manager_service import mock_resource_manager as resource_manager
from campaigns.models.workflow import WorkflowExecution

class ResourceManagerDashboardView(TemplateView):
    """
    View for the resource manager dashboard.
    """
    template_name = 'campaigns/resource_manager_dashboard.html'

    def get_context_data(self, **kwargs):
        """Get context data for the template."""
        context = super().get_context_data(**kwargs)

        # Get system resources
        resources = resource_manager.get_system_resources()

        # Get running workflows
        running_workflows = []
        with resource_manager.lock:
            for workflow_id, info in resource_manager.running_workflows.items():
                running_workflows.append({
                    'id': workflow_id,
                    'campaign_id': info.get('campaign_id'),
                    'workflow_type': info.get('workflow_type'),
                    'priority': info.get('priority'),
                    'start_time': info.get('start_time').isoformat() if info.get('start_time') else None,
                    'paused': info.get('paused', False),
                    'resource_usage': info.get('resource_usage', {}),
                    'api_usage': info.get('api_usage', {})
                })

        context.update({
            'resources': resources,
            'running_workflows': running_workflows
        })

        return context


class ResourceManagerDemoView(View):
    """
    View for the resource manager demo dashboard (no login required).
    """
    def get(self, request):
        """Render the demo template."""
        return render(request, 'campaigns/resource_manager_demo.html')


class ResourceManagerStyleDemoView(View):
    """
    View for the resource manager style demo dashboard (no login required).
    """
    def get(self, request):
        """Render the style demo template."""
        return render(request, 'campaigns/resource_manager_style_demo.html')

@method_decorator(csrf_exempt, name='dispatch')
class PauseWorkflowView(APIView):
    """
    API view for pausing a workflow.
    """
    # Removed authentication requirement
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Pause a workflow.

        Request Body:
            workflow_id (str): Workflow execution ID

        Returns:
            Response: Success or error message
        """
        workflow_id = request.data.get('workflow_id')

        if not workflow_id:
            return Response({
                'error': 'Missing workflow_id parameter'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Get workflow execution
            workflow_execution = get_object_or_404(WorkflowExecution, id=workflow_id)

            # Pause workflow
            resource_manager._pause_workflow(workflow_id)

            return Response({
                'success': True,
                'message': f"Workflow {workflow_id} paused successfully"
            })
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@method_decorator(csrf_exempt, name='dispatch')
class ResumeWorkflowView(APIView):
    """
    API view for resuming a workflow.
    """
    # Removed authentication requirement
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Resume a workflow.

        Request Body:
            workflow_id (str): Workflow execution ID

        Returns:
            Response: Success or error message
        """
        workflow_id = request.data.get('workflow_id')

        if not workflow_id:
            return Response({
                'error': 'Missing workflow_id parameter'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Get workflow execution
            workflow_execution = get_object_or_404(WorkflowExecution, id=workflow_id)

            # Resume workflow
            resource_manager._resume_workflow(workflow_id)

            return Response({
                'success': True,
                'message': f"Workflow {workflow_id} resumed successfully"
            })
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@method_decorator(csrf_exempt, name='dispatch')
class ResumeAllWorkflowsView(APIView):
    """
    API view for resuming all paused workflows.
    """
    # Removed authentication requirement
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Resume all paused workflows.

        Returns:
            Response: Success or error message
        """
        try:
            # Get paused workflows
            paused_workflows = []
            with resource_manager.lock:
                for workflow_id, info in resource_manager.running_workflows.items():
                    if info.get('status') == 'paused':
                        paused_workflows.append(workflow_id)

            # Resume each workflow
            for workflow_id in paused_workflows:
                resource_manager._resume_workflow(workflow_id)

            return Response({
                'success': True,
                'message': f"Resumed {len(paused_workflows)} workflows"
            })
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@method_decorator(csrf_exempt, name='dispatch')
class RemoveFromQueueView(APIView):
    """
    API view for removing a workflow from the queue.
    """
    # Removed authentication requirement
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Remove a workflow from the queue.

        Request Body:
            workflow_id (str): Workflow execution ID

        Returns:
            Response: Success or error message
        """
        workflow_id = request.data.get('workflow_id')

        if not workflow_id:
            return Response({
                'error': 'Missing workflow_id parameter'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Get workflow execution
            workflow_execution = get_object_or_404(WorkflowExecution, id=workflow_id)

            # Remove from queue
            with resource_manager.lock:
                # Remove from queue
                resource_manager.workflow_queue = [
                    entry for entry in resource_manager.workflow_queue
                    if entry.workflow_id != workflow_id
                ]
                heapq.heapify(resource_manager.workflow_queue)

                # Update workflow status
                if workflow_id in resource_manager.running_workflows:
                    # Mark as cancelled
                    resource_manager.running_workflows[workflow_id]['status'] = 'cancelled'

                    # Remove from running workflows
                    resource_manager.running_workflows.pop(workflow_id, None)

            # Update workflow execution record
            workflow_execution.status = 'cancelled'
            workflow_execution.end_time = timezone.now()
            workflow_execution.save(update_fields=['status', 'end_time'])

            return Response({
                'success': True,
                'message': f"Workflow {workflow_id} removed from queue"
            })
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator(csrf_exempt, name='dispatch')
class ResourceManagerStatusView(APIView):
    """
    API view for getting resource manager status.
    """
    # Temporarily removed authentication for testing
    # permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get resource manager status.

        Returns:
            Response: Resource manager status
        """
        try:
            # Get system resources
            resources = resource_manager.get_system_resources()

            # Get running workflows
            running_workflows = []
            with resource_manager.lock:
                for workflow_id, info in resource_manager.running_workflows.items():
                    running_workflows.append({
                        'id': workflow_id,
                        'campaign_id': info.get('campaign_id'),
                        'workflow_type': info.get('workflow_type'),
                        'priority': info.get('priority'),
                        'status': info.get('status', 'running'),
                        'start_time': info.get('start_time').isoformat() if info.get('start_time') else None,
                        'submission_time': info.get('submission_time').isoformat() if info.get('submission_time') else None,
                        'paused': info.get('paused', False),
                        'resource_usage': info.get('resource_usage', {}),
                        'api_usage': info.get('api_usage', {})
                    })

            # Get priority mode information
            priority_mode_info = resource_manager.get_priority_mode_info()

            return Response({
                'resources': resources,
                'running_workflows': running_workflows,
                'priority_mode_info': priority_mode_info,
                'timestamp': timezone.now().isoformat()
            })
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator(csrf_exempt, name='dispatch')
class OptimizeResourcesView(APIView):
    """
    API view for optimizing resources.
    """
    # Temporarily removed authentication for testing
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Optimize resources.

        Returns:
            Response: Success or error message
        """
        try:
            # Implement resource optimization logic
            # For example, clear unused resources, rebalance workloads, etc.

            # For now, just return success
            return Response({
                'success': True,
                'message': "Resources optimized successfully"
            })
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator(csrf_exempt, name='dispatch')
class SetPriorityModeView(APIView):
    """
    API view for setting system priority mode.
    """
    # Temporarily removed authentication for testing
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Set system priority mode.

        Returns:
            Response: Success or error message
        """
        try:
            mode = request.POST.get('mode')

            if not mode:
                return Response({
                    'success': False,
                    'error': 'Priority mode is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Set priority mode using resource manager
            success = resource_manager.set_priority_mode(mode)

            if success:
                return Response({
                    'success': True,
                    'message': f"Priority mode set to {mode}",
                    'mode': mode
                })
            else:
                return Response({
                    'success': False,
                    'error': 'Failed to set priority mode'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)