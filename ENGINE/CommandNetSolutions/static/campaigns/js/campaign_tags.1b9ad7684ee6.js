/**
 * Campaign Tags Management JavaScript
 * Handles tag search, filtering, and assignment functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Load tag categories for filtering
    loadTagCategories();
    
    // Initialize event listeners
    initializeTagSelection();
    initializeTagGroupSelection();
    initializeFormHandlers();
});

/**
 * Load tag categories for the filter dropdown
 */
function loadTagCategories() {
    fetch('/campaigns/api/tag-categories/')
        .then(response => response.json())
        .then(data => {
            const categoryFilter = document.getElementById('category-filter');
            if (categoryFilter && data.categories) {
                // Clear existing options except "All Categories"
                categoryFilter.innerHTML = '<option value="">All Categories</option>';
                
                // Add category options
                data.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    categoryFilter.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading tag categories:', error);
        });
}

/**
 * Initialize tag selection functionality
 */
function initializeTagSelection() {
    // Handle tag selection from search results
    document.addEventListener('click', function(e) {
        if (e.target.closest('.select-tag-btn')) {
            e.preventDefault();
            const button = e.target.closest('.select-tag-btn');
            const tagId = button.dataset.tagId;
            const tagName = button.dataset.tagName;
            
            selectTag(tagId, tagName);
        }
    });
    
    // Handle cancel tag selection
    document.addEventListener('click', function(e) {
        if (e.target.closest('#cancel-tag-selection')) {
            e.preventDefault();
            cancelTagSelection();
        }
    });
}

/**
 * Initialize tag group selection functionality
 */
function initializeTagGroupSelection() {
    // Handle tag group selection
    document.addEventListener('click', function(e) {
        if (e.target.closest('.select-tag-group-btn')) {
            e.preventDefault();
            const button = e.target.closest('.select-tag-group-btn');
            const tagGroupId = button.dataset.tagGroupId;
            const tagGroupName = button.dataset.tagGroupName;
            
            selectTagGroup(tagGroupId, tagGroupName);
        }
    });
    
    // Handle cancel tag group selection
    document.addEventListener('click', function(e) {
        if (e.target.closest('#cancel-tag-group-selection')) {
            e.preventDefault();
            cancelTagGroupSelection();
        }
    });
}

/**
 * Initialize form handlers
 */
function initializeFormHandlers() {
    // Handle individual tag assignment form submission
    const assignTagForm = document.getElementById('assign-tag-form');
    if (assignTagForm) {
        assignTagForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitTagAssignment();
        });
    }
    
    // Handle HTMX events for tag group assignment
    document.addEventListener('htmx:afterRequest', function(e) {
        if (e.detail.target.id === 'assigned-tags-container') {
            // Reset tag group selection after successful assignment
            cancelTagGroupSelection();
            
            // Show success message if present
            const xhr = e.detail.xhr;
            if (xhr.status === 200) {
                // The messages are handled by Django messages framework
                // and will be displayed automatically
            }
        }
    });
}

/**
 * Select a tag for assignment
 */
function selectTag(tagId, tagName) {
    // Set the selected tag
    document.getElementById('selected-tag-id').value = tagId;
    document.getElementById('selected-tag-display').innerHTML = 
        `<span class="badge bg-primary">${tagName}</span>`;
    
    // Show the form and hide search results
    document.getElementById('selected-tag-form').style.display = 'block';
    document.getElementById('tag-search-results').style.display = 'none';
    
    // Clear search input
    document.getElementById('tag-search').value = '';
}

/**
 * Cancel tag selection
 */
function cancelTagSelection() {
    // Hide the form and show search results
    document.getElementById('selected-tag-form').style.display = 'none';
    document.getElementById('tag-search-results').style.display = 'block';
    
    // Clear form
    document.getElementById('selected-tag-id').value = '';
    document.getElementById('selected-tag-display').innerHTML = '';
    document.getElementById('tag-is-required').checked = false;
    
    // Reset search results to initial state
    document.getElementById('tag-search-results').innerHTML = `
        <div class="text-center text-muted p-4">
            <i class="fas fa-search fa-2x mb-2"></i>
            <p>Start typing to search for tags...</p>
        </div>
    `;
}

/**
 * Select a tag group for assignment
 */
function selectTagGroup(tagGroupId, tagGroupName) {
    // Set the selected tag group
    document.getElementById('selected-tag-group-id').value = tagGroupId;
    document.getElementById('selected-tag-group-display').innerHTML = 
        `<span class="badge bg-secondary">${tagGroupName}</span>`;
    
    // Show the form and hide tag groups list
    document.getElementById('selected-tag-group-form').style.display = 'block';
    document.getElementById('tag-groups-list').style.display = 'none';
}

/**
 * Cancel tag group selection
 */
function cancelTagGroupSelection() {
    // Hide the form and show tag groups list
    document.getElementById('selected-tag-group-form').style.display = 'none';
    document.getElementById('tag-groups-list').style.display = 'block';
    
    // Clear form
    document.getElementById('selected-tag-group-id').value = '';
    document.getElementById('selected-tag-group-display').innerHTML = '';
    document.getElementById('tag-group-is-required').checked = false;
}

/**
 * Submit tag assignment form
 */
function submitTagAssignment() {
    const form = document.getElementById('assign-tag-form');
    const formData = new FormData(form);
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        }
    })
    .then(response => {
        if (response.ok) {
            // Reload the page to show the updated tags list and any messages
            window.location.reload();
        } else {
            throw new Error('Failed to assign tag');
        }
    })
    .catch(error => {
        console.error('Error assigning tag:', error);
        alert('Failed to assign tag. Please try again.');
    });
}

/**
 * Utility function to show loading state
 */
function showLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="text-center text-muted p-4">
                <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                <p>Loading...</p>
            </div>
        `;
    }
}

/**
 * Utility function to show error state
 */
function showError(elementId, message) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="text-center text-danger p-4">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>${message}</p>
            </div>
        `;
    }
}
