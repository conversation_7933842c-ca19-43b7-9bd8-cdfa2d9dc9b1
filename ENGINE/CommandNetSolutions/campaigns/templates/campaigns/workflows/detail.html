{% extends 'campaigns/base.html' %}
{% load static %}

{% block title %}{{ workflow.name }} - Workflow Details{% endblock %}

{% block campaign_content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ workflow.name }}</h1>
            <p class="text-muted">{{ workflow.description|default:"No description provided" }}</p>
        </div>
        <div>
            <a href="{% url 'campaigns:workflow_dashboard' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Status and Actions -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Workflow Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    {% if workflow.status == 'pending' %}
                                        <span class="badge bg-warning fs-6">{{ workflow.get_status_display }}</span>
                                    {% elif workflow.status == 'running' %}
                                        <span class="badge bg-success fs-6">{{ workflow.get_status_display }}</span>
                                    {% elif workflow.status == 'paused' %}
                                        <span class="badge bg-info fs-6">{{ workflow.get_status_display }}</span>
                                    {% elif workflow.status == 'completed' %}
                                        <span class="badge bg-primary fs-6">{{ workflow.get_status_display }}</span>
                                    {% elif workflow.status == 'failed' %}
                                        <span class="badge bg-danger fs-6">{{ workflow.get_status_display }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary fs-6">{{ workflow.get_status_display }}</span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Campaign</label>
                                <div>
                                    <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="text-decoration-none">
                                        {{ campaign.name }}
                                    </a>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Whitelist Accounts</label>
                                <div>{{ workflow.whitelist_count }} accounts</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Daily Limit</label>
                                <div>{{ workflow.daily_limit }} actions/day</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Action Delay</label>
                                <div>{{ workflow.delay_between_actions }} seconds</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Created</label>
                                <div>{{ workflow.created_at|date:"M d, Y H:i" }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <label class="form-label text-muted mb-0">Overall Progress</label>
                            <small class="text-muted">{{ workflow.overall_progress|floatformat:1 }}%</small>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-success" role="progressbar" 
                                 style="width: {{ workflow.overall_progress }}%"></div>
                        </div>
                    </div>
                    
                    <!-- Selected Actions -->
                    <div class="mb-3">
                        <label class="form-label text-muted">Selected Actions</label>
                        <div class="d-flex flex-wrap gap-1">
                            {% for action in workflow.selected_workflows %}
                                <span class="badge bg-secondary">{{ action|title }}</span>
                            {% empty %}
                                <span class="text-muted">No actions selected</span>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <!-- Timing Information -->
                    {% if workflow.started_at or workflow.completed_at %}
                    <div class="row">
                        {% if workflow.started_at %}
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Started At</label>
                                <div>{{ workflow.started_at|date:"M d, Y H:i" }}</div>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if workflow.completed_at %}
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Completed At</label>
                                <div>{{ workflow.completed_at|date:"M d, Y H:i" }}</div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Actions Panel -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if workflow.status == 'pending' %}
                            <form method="post" action="{% url 'campaigns:workflow_start' workflow.id %}">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-play me-2"></i>Start Workflow
                                </button>
                            </form>
                        {% elif workflow.status == 'running' %}
                            <form method="post" action="{% url 'campaigns:workflow_pause' workflow.id %}">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-pause me-2"></i>Pause Workflow
                                </button>
                            </form>
                        {% elif workflow.status == 'paused' %}
                            <form method="post" action="{% url 'campaigns:workflow_resume' workflow.id %}">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-play me-2"></i>Resume Workflow
                                </button>
                            </form>
                        {% endif %}
                        
                        {% if workflow.status in 'pending,running,paused' %}
                            <form method="post" action="{% url 'campaigns:workflow_stop' workflow.id %}">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-danger w-100" 
                                        onclick="return confirm('Are you sure you want to stop this workflow? This action cannot be undone.')">
                                    <i class="fas fa-stop me-2"></i>Stop Workflow
                                </button>
                            </form>
                        {% endif %}
                        
                        <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-project-diagram me-2"></i>View Campaign
                        </a>
                        
                        <button type="button" class="btn btn-outline-secondary w-100" onclick="refreshStatus()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh Status
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Template Information -->
    {% if template %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Template Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Template Name</label>
                                <div>{{ template.name }}</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Category</label>
                                <div>
                                    <span class="badge bg-secondary">{{ template.category|title }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Description</label>
                                <div>{{ template.description }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshStatus() {
    const refreshBtn = document.querySelector('button[onclick="refreshStatus()"]');
    const originalText = refreshBtn.innerHTML;
    
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Refreshing...';
    refreshBtn.disabled = true;
    
    fetch('{% url "campaigns:api_workflow_status" workflow.id %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update progress bar
                const progressBar = document.querySelector('.progress-bar');
                const progressText = document.querySelector('.progress + .mb-3 small');
                
                if (progressBar && progressText) {
                    progressBar.style.width = data.workflow.progress + '%';
                    progressText.textContent = data.workflow.progress.toFixed(1) + '%';
                }
                
                // Reload page if status changed
                if (data.workflow.status !== '{{ workflow.status }}') {
                    location.reload();
                }
            }
        })
        .catch(error => {
            console.error('Error fetching workflow status:', error);
            alert('Error refreshing status. Please try again.');
        })
        .finally(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        });
}

// Auto-refresh every 30 seconds if workflow is active
{% if workflow.status in 'pending,running,paused' %}
setInterval(refreshStatus, 30000);
{% endif %}
</script>
{% endblock %}
