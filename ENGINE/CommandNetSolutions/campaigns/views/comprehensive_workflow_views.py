"""
Views for the comprehensive workflow system.
"""
import json
import logging
from django.views.generic import TemplateView, View
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse
from django.http import JsonResponse
from django.contrib import messages
from django.core.exceptions import ValidationError

from campaigns.models import Campaign
from campaigns.models.workflow import Workflow, WorkflowTemplate
from campaigns.services.comprehensive_workflow_service import ComprehensiveWorkflowService
from instagram.models import WhiteListEntry

logger = logging.getLogger(__name__)


class ComprehensiveWorkflowCreateView(TemplateView):
    """
    Multi-step workflow creation wizard using the new comprehensive system.
    """
    template_name = 'campaigns/workflows/comprehensive_create.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get available campaigns with whitelists
        available_campaigns = []
        campaigns = Campaign.objects.filter(
            is_in_cep=False,
            status__in=['completed', 'stopped']
        ).order_by('-created_at')

        for campaign in campaigns:
            try:
                whitelist_count = WhiteListEntry.objects.filter(
                    account__campaign_id=str(campaign.id)
                ).count()
                
                if whitelist_count > 0:
                    campaign.whitelist_count = whitelist_count
                    available_campaigns.append(campaign)
            except Exception as e:
                logger.warning(f"Error getting whitelist count for campaign {campaign.id}: {e}")
                continue

        # Get workflow templates
        workflow_templates = WorkflowTemplate.objects.filter(
            is_active=True
        ).order_by('category', 'name')

        context.update({
            'available_campaigns': available_campaigns,
            'workflow_templates': workflow_templates,
        })

        return context

    def post(self, request):
        """Handle workflow creation form submission."""
        try:
            # Get form data
            workflow_name = request.POST.get('workflow_name')
            workflow_description = request.POST.get('workflow_description', '')
            source_campaign_ids = request.POST.getlist('source_campaigns')
            template_id = request.POST.get('template_id')
            enabled_actions = request.POST.getlist('enabled_actions')
            
            # Get action parameters
            action_parameters = {}
            for action in enabled_actions:
                action_parameters[action] = {
                    'enabled': True,
                    'priority': request.POST.get(f'{action}_priority', 1),
                    'custom_params': request.POST.get(f'{action}_params', '{}')
                }

            # Get limits
            daily_limit = int(request.POST.get('daily_limit', 50))
            hourly_limit = int(request.POST.get('hourly_limit', 10))
            delay_between_actions = int(request.POST.get('delay_between_actions', 30))
            
            # Get scheduling
            schedule_type = request.POST.get('schedule_type', 'immediate')
            scheduled_start = None
            if schedule_type != 'immediate':
                scheduled_start_str = request.POST.get('scheduled_start')
                if scheduled_start_str:
                    from django.utils.dateparse import parse_datetime
                    scheduled_start = parse_datetime(scheduled_start_str)

            # Validate required fields
            if not all([workflow_name, source_campaign_ids, template_id, enabled_actions]):
                messages.error(request, 'Please fill in all required fields.')
                return redirect('campaigns:comprehensive_workflow_create')

            # Create workflow using service
            service = ComprehensiveWorkflowService()
            workflow = service.create_workflow(
                name=workflow_name,
                description=workflow_description,
                source_campaign_ids=source_campaign_ids,
                template_id=template_id,
                enabled_actions=enabled_actions,
                action_parameters=action_parameters,
                daily_limit=daily_limit,
                hourly_limit=hourly_limit,
                delay_between_actions=delay_between_actions,
                schedule_type=schedule_type,
                scheduled_start=scheduled_start
            )

            messages.success(request, f'Workflow "{workflow_name}" created successfully!')
            return redirect('campaigns:comprehensive_workflow_detail', pk=workflow.id)

        except ValidationError as e:
            messages.error(request, str(e))
            return redirect('campaigns:comprehensive_workflow_create')
        except Exception as e:
            logger.error(f"Error creating comprehensive workflow: {str(e)}")
            messages.error(request, f'Error creating workflow: {str(e)}')
            return redirect('campaigns:comprehensive_workflow_create')


class ComprehensiveWorkflowDetailView(TemplateView):
    """
    Detail view for comprehensive workflows.
    """
    template_name = 'campaigns/workflows/comprehensive_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        workflow_id = kwargs.get('pk')
        workflow = get_object_or_404(Workflow, id=workflow_id)
        
        # Get workflow statistics
        service = ComprehensiveWorkflowService()
        workflow_status = service.get_workflow_status(workflow_id)
        
        context.update({
            'workflow': workflow,
            'workflow_status': workflow_status,
            'source_campaigns': workflow.source_campaigns.all(),
        })
        
        return context


class ComprehensiveWorkflowActionView(View):
    """
    Handle workflow actions (start, pause, stop).
    """
    
    def post(self, request, pk, action):
        """Handle workflow action requests."""
        try:
            service = ComprehensiveWorkflowService()
            workflow = get_object_or_404(Workflow, id=pk)
            
            success = False
            if action == 'start':
                success = service.start_workflow(pk)
                action_name = 'started'
            elif action == 'pause':
                success = service.pause_workflow(pk)
                action_name = 'paused'
            elif action == 'stop':
                success = service.stop_workflow(pk)
                action_name = 'stopped'
            else:
                messages.error(request, f'Invalid action: {action}')
                return redirect('campaigns:comprehensive_workflow_detail', pk=pk)
            
            if success:
                messages.success(request, f'Workflow "{workflow.name}" {action_name} successfully!')
            else:
                messages.error(request, f'Unable to {action} workflow. Check workflow status.')
            
            return redirect('campaigns:comprehensive_workflow_detail', pk=pk)
            
        except Exception as e:
            logger.error(f"Error performing workflow action {action} on {pk}: {str(e)}")
            messages.error(request, f'Error performing action: {str(e)}')
            return redirect('campaigns:comprehensive_workflow_detail', pk=pk)


class ComprehensiveWorkflowStatusAPIView(View):
    """
    API view to get comprehensive workflow status.
    """
    
    def get(self, request, pk):
        """Get workflow status as JSON."""
        try:
            service = ComprehensiveWorkflowService()
            status = service.get_workflow_status(pk)
            
            if 'error' in status:
                return JsonResponse({
                    'success': False,
                    'error': status['error']
                }, status=404 if status['error'] == 'Workflow not found' else 500)
            
            return JsonResponse({
                'success': True,
                'workflow': status
            })
            
        except Exception as e:
            logger.error(f"Error getting workflow status for {pk}: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)


class WorkflowResourceManagerView(TemplateView):
    """
    View for managing workflow resources and limits.
    """
    template_name = 'campaigns/workflows/resource_manager.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        service = ComprehensiveWorkflowService()
        resource_manager = service.resource_manager
        
        # Get current workflow statistics
        active_workflows = Workflow.objects.filter(
            status__in=['pending', 'running', 'paused']
        )
        
        completed_workflows = Workflow.objects.filter(
            status__in=['completed', 'failed', 'stopped']
        ).order_by('-completed_at')[:10]
        
        context.update({
            'resource_manager': resource_manager,
            'active_workflows': active_workflows,
            'completed_workflows': completed_workflows,
        })
        
        return context

    def post(self, request):
        """Handle resource manager configuration updates."""
        try:
            service = ComprehensiveWorkflowService()
            resource_manager = service.resource_manager
            
            # Update limits
            resource_manager.max_daily_actions = int(request.POST.get('max_daily_actions', 500))
            resource_manager.max_hourly_actions = int(request.POST.get('max_hourly_actions', 50))
            resource_manager.max_concurrent_actions = int(request.POST.get('max_concurrent_actions', 5))
            resource_manager.priority_mode = request.POST.get('priority_mode', 'cep')
            resource_manager.max_queue_size = int(request.POST.get('max_queue_size', 1000))
            
            resource_manager.save()
            
            messages.success(request, 'Resource manager settings updated successfully!')
            return redirect('campaigns:workflow_resource_manager')
            
        except Exception as e:
            logger.error(f"Error updating resource manager: {str(e)}")
            messages.error(request, f'Error updating settings: {str(e)}')
            return redirect('campaigns:workflow_resource_manager')
